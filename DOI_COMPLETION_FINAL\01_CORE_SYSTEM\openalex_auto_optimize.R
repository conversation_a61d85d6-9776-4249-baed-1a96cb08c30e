# OpenAlex自动优化系统 - 独立版本
# 自动分析和优化OpenAlex DOI补全参数

library(httr)
library(jsonlite)
library(stringdist)

# 加载核心函数
source("doi_completion_core.R")

cat("=== OpenAlex自动优化系统 ===\n")
cat("🎯 目标: 提高OpenAlex DOI补全成功率\n")
cat("📊 策略: 基于测试结果自动调整参数\n\n")

# === 优化的OpenAlex查询函数 ===
search_doi_openalex_auto_optimized <- function(title, authors, year, journal, optimization_level = "moderate") {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 2]
    if (length(keywords) > 6) keywords <- keywords[1:6]
    if (length(keywords) == 0) return(NULL)
    
    # OpenAlex API查询
    search_query <- paste(keywords, collapse = " ")
    year_filter <- sprintf("publication_year:%s-%s", as.numeric(year)-3, as.numeric(year)+3)
    
    url <- sprintf("https://api.openalex.org/works?search=%s&filter=%s&per-page=20", 
                   URLencode(search_query), URLencode(year_filter))
    
    response <- GET(url, 
                   user_agent("DOI_Completion_AutoOptimized/1.0"), 
                   timeout(30),
                   add_headers("Accept" = "application/json"))
    
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$results) || length(content$results) == 0) return(NULL)
    
    items <- content$results
    best_match <- NULL
    best_score <- 0
    
    cat(sprintf("OpenAlex返回 %d 个候选结果\n", nrow(items)))
    
    # 根据优化级别设置阈值
    if (optimization_level == "conservative") {
      title_threshold <- 0.75
      journal_threshold <- 0.4
      year_threshold <- 0.5
      subject_threshold <- 0.8
      final_threshold <- 0.65
      title_weight <- 0.5
    } else if (optimization_level == "moderate") {
      title_threshold <- 0.70
      journal_threshold <- 0.3
      year_threshold <- 0.3
      subject_threshold <- 0.6
      final_threshold <- 0.60
      title_weight <- 0.6
    } else if (optimization_level == "aggressive") {
      title_threshold <- 0.65
      journal_threshold <- 0.25
      year_threshold <- 0.2
      subject_threshold <- 0.4
      final_threshold <- 0.55
      title_weight <- 0.7
    }
    
    # 评估每个候选结果
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      candidate_title <- if (!is.null(item$title) && item$title != "") item$title else ""
      candidate_journal <- ""
      if (!is.null(item$primary_location) && !is.null(item$primary_location$source) && 
          !is.null(item$primary_location$source$display_name)) {
        candidate_journal <- item$primary_location$source$display_name
      }
      candidate_year <- if (!is.null(item$publication_year)) item$publication_year else ""
      candidate_doi <- ""
      if (!is.null(item$doi) && item$doi != "") {
        candidate_doi <- gsub("^https://doi.org/", "", item$doi)
      } else {
        next
      }
      
      # 计算相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      journal_sim <- calculate_journal_similarity(journal, candidate_journal)
      year_sim <- calculate_year_similarity(year, candidate_year)
      subject_rel <- check_subject_relevance(title, candidate_title)
      
      # 动态权重分配
      final_score <- (title_sim * title_weight) + 
                     (journal_sim * (0.4 - title_weight + 0.5)) + 
                     (year_sim * 0.15) + 
                     (subject_rel * 0.1)
      
      cat(sprintf("候选 %d: T=%.3f, J=%.3f, Y=%.3f, S=%.3f, 总分=%.3f [%s]\n",
                  i, title_sim, journal_sim, year_sim, subject_rel, final_score, optimization_level))
      
      # 应用优化阈值
      if (title_sim >= title_threshold &&
          journal_sim >= journal_threshold &&
          year_sim >= year_threshold &&
          subject_rel >= subject_threshold &&
          final_score >= final_threshold &&
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = candidate_doi,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          subject_relevance = subject_rel,
          final_score = final_score,
          source = paste0("openalex_", optimization_level),
          optimization_level = optimization_level
        )
        
        cat(sprintf("  ✅ 新的最佳匹配! DOI: %s (评分: %.3f)\n", candidate_doi, final_score))
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("OpenAlex API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 自动优化测试函数 ===
auto_optimize_openalex <- function() {
  cat("\n=== 自动优化测试开始 ===\n")
  
  # 测试用例
  test_cases <- list(
    list(
      title = "Machine learning applications in healthcare",
      authors = "Johnson A",
      year = 2020,
      journal = "Nature Medicine",
      description = "机器学习医疗"
    ),
    list(
      title = "Deep learning for natural language processing", 
      authors = "Brown C",
      year = 2019,
      journal = "Journal of Machine Learning Research",
      description = "深度学习NLP"
    ),
    list(
      title = "Artificial intelligence in medical diagnosis",
      authors = "Smith D",
      year = 2021,
      journal = "Medical AI Journal", 
      description = "AI医疗诊断"
    )
  )
  
  optimization_levels <- c("conservative", "moderate", "aggressive")
  results <- list()
  
  for (level in optimization_levels) {
    cat(sprintf("\n🔧 测试优化级别: %s\n", level))
    cat(paste(rep("=", 50), collapse = ""), "\n")
    
    success_count <- 0
    level_results <- list()
    
    for (i in 1:length(test_cases)) {
      test_case <- test_cases[[i]]
      cat(sprintf("\n--- 案例 %d: %s ---\n", i, test_case$description))
      
      result <- search_doi_openalex_auto_optimized(
        title = test_case$title,
        authors = test_case$authors,
        year = test_case$year,
        journal = test_case$journal,
        optimization_level = level
      )
      
      if (!is.null(result)) {
        success_count <- success_count + 1
        quality <- assess_quality(result$title_similarity, result$final_score)
        cat(sprintf("✅ 成功: %s (质量: %s, 评分: %.3f)\n", 
                    result$doi, quality, result$final_score))
        level_results[[i]] <- result
      } else {
        cat("❌ 失败\n")
        level_results[[i]] <- NULL
      }
      
      if (i < length(test_cases)) {
        cat("等待2秒...\n")
        Sys.sleep(2)
      }
    }
    
    success_rate <- 100 * success_count / length(test_cases)
    cat(sprintf("\n📊 %s级别成功率: %.1f%% (%d/%d)\n", 
                level, success_rate, success_count, length(test_cases)))
    
    results[[level]] <- list(
      success_count = success_count,
      success_rate = success_rate,
      results = level_results
    )
  }
  
  # 分析最佳优化级别
  cat(sprintf("\n=== 优化结果分析 ===\n"))
  
  best_level <- ""
  best_success_rate <- 0
  
  for (level in optimization_levels) {
    rate <- results[[level]]$success_rate
    cat(sprintf("%s级别: %.1f%% 成功率\n", level, rate))
    
    if (rate > best_success_rate) {
      best_success_rate <- rate
      best_level <- level
    }
  }
  
  cat(sprintf("\n🏆 最佳优化级别: %s (成功率: %.1f%%)\n", best_level, best_success_rate))
  
  # 生成优化建议
  if (best_success_rate >= 66.7) {  # 2/3成功
    cat("🎉 优化效果显著！建议采用此配置。\n")
    recommendation <- "excellent"
  } else if (best_success_rate >= 33.3) {  # 1/3成功
    cat("📈 优化有一定效果，可以考虑采用。\n")
    recommendation <- "good"
  } else {
    cat("⚠️  优化效果有限，建议进一步调整策略。\n")
    recommendation = "needs_improvement"
  }
  
  # 保存优化配置
  if (best_success_rate > 0) {
    cat(sprintf("\n💾 保存最佳配置 (%s级别)...\n", best_level))
    
    config_content <- sprintf('# OpenAlex优化配置 - %s级别
# 自动优化结果: 成功率 %.1f%%
# 生成时间: %s

OPENALEX_OPTIMIZATION_LEVEL <- "%s"
OPENALEX_SUCCESS_RATE <- %.1f

# 使用方法:
# result <- search_doi_openalex_auto_optimized(title, authors, year, journal, 
#                                              optimization_level = OPENALEX_OPTIMIZATION_LEVEL)
', 
      best_level, best_success_rate, format(Sys.time(), "%Y-%m-%d %H:%M:%S"), 
      best_level, best_success_rate)
    
    writeLines(config_content, "openalex_optimal_config.R")
    cat("✅ 配置已保存到: openalex_optimal_config.R\n")
  }
  
  return(list(
    best_level = best_level,
    best_success_rate = best_success_rate,
    recommendation = recommendation,
    all_results = results
  ))
}

# === 应用最佳配置的便捷函数 ===
search_doi_openalex_best <- function(title, authors, year, journal) {
  # 尝试加载最佳配置
  if (file.exists("openalex_optimal_config.R")) {
    source("openalex_optimal_config.R")
    optimization_level <- OPENALEX_OPTIMIZATION_LEVEL
  } else {
    optimization_level <- "moderate"  # 默认配置
  }
  
  return(search_doi_openalex_auto_optimized(title, authors, year, journal, optimization_level))
}

cat("✅ OpenAlex自动优化系统已加载\n")
cat("📋 主要函数:\n")
cat("  - auto_optimize_openalex()                : 自动优化测试\n")
cat("  - search_doi_openalex_auto_optimized()    : 可配置优化搜索\n")
cat("  - search_doi_openalex_best()              : 使用最佳配置搜索\n")

# 自动执行优化
cat("\n🚀 开始自动优化OpenAlex参数...\n")
optimization_result <- auto_optimize_openalex()

cat(sprintf("\n🎯 自动优化完成!\n"))
cat(sprintf("最佳配置: %s级别\n", optimization_result$best_level))
cat(sprintf("成功率: %.1f%%\n", optimization_result$best_success_rate))
cat(sprintf("建议: %s\n", optimization_result$recommendation))

if (optimization_result$recommendation == "excellent") {
  cat("\n🎉 OpenAlex优化成功！您现在可以使用 search_doi_openalex_best() 函数获得最佳性能。\n")
} else if (optimization_result$recommendation == "good") {
  cat("\n📈 OpenAlex性能有所提升，建议在实际使用中进一步验证。\n")
} else {
  cat("\n🔧 建议考虑其他优化策略或继续使用Crossref作为主要引擎。\n")
}

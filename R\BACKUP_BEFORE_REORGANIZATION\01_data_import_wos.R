# 01_wos_convert2df.R
# 功能：使用bibliometrix::convert2df进行WoS数据格式转换和基础去重
# 处理内容：
# 1. 将WoS纯文本文件转换为bibliometrix标准格式
# 2. 基于UT字段进行基础去重
# 3. 生成字段缺失值统计报告
# 4. 保存为初始bibliometrix数据集

# --- 0. 配置管理 ---
# 首先加载必要的包
required_packages <- c("bibliometrix", "here", "dplyr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
}
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保here包正确初始化
if (!requireNamespace("here", quietly = TRUE)) {
  stop("无法加载here包，请手动安装：install.packages('here')")
}

# 定义日志函数
log_message <- function(msg, type = "info") {
  # 获取当前时间
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  # 格式化消息
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  # 输出到控制台
  message(formatted_msg)
  # 如果日志文件已打开，也写入日志文件
  if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
    cat(formatted_msg, "\n", file = log_con)
  }
}

config <- list(
  # 输入输出路径配置
  paths = list(
    raw_data = here("data_repository", "00_original_raw_files"),
    processed = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    logs = here("data_repository", "05_execution_logs", "bibliometrix_logs"),
    reports = here("data_repository", "06_data_reports", "biblioshiny_reports")
  ),
  # 文件命名配置
  files = list(
    output_rds = "datay_bibliometrix_initial.rds",
    missing_stats = "import_missing_stats.csv",
    import_log = "import.log"
  ),
  # 数据验证配置
  validation = list(
    min_records = 1,  # 最小记录数
    required_fields = c("UT", "TI", "PY", "AU"),  # 必需字段
    field_validation = list(
      PY = function(x) all(x >= 1900 & x <= as.numeric(format(Sys.Date(), "%Y"))),
      UT = function(x) all(!is.na(x) & nchar(x) > 0)
    )
  )
)

# --- 1. 设置环境与加载库 ---
message("开始数据导入流程")

# --- 2. 定义输入和输出路径 ---
raw_data_path <- config$paths$raw_data
imported_output_dir <- config$paths$processed
output_rds_file <- file.path(imported_output_dir, config$files$output_rds)
log_file <- file.path(config$paths$logs, config$files$import_log)

# 创建必要的目录
suppressMessages({
  for (dir_path in c(imported_output_dir, config$paths$logs, config$paths$reports)) {
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = TRUE)
      message(sprintf("创建目录: %s", dir_path))
    }
  }
})

# 设置日志文件
log_con <- file(log_file, "w")
log_message(sprintf("日志文件已创建: %s", log_file))

# 记录开始时间
start_time <- Sys.time()
log_message(sprintf("数据导入开始时间: %s", format(start_time)))

# --- 3. 查找并读取WoS纯文本文件 ---
wos_txt_files <- list.files(
  path = raw_data_path,
  pattern = "\\.txt$",
  full.names = TRUE,
  recursive = FALSE,
  ignore.case = TRUE
)

if (length(wos_txt_files) == 0) {
  log_message("错误：未找到WoS文本文件", "error")
  stop(sprintf("在目录 '%s' 中没有找到匹配的 .txt 文件", raw_data_path))
} else {
  log_message(sprintf("找到 %d 个WoS文本文件", length(wos_txt_files)))
  for (file in wos_txt_files) {
    log_message(sprintf("准备处理文件: %s", basename(file)))
  }
}

# --- 4. 使用 bibliometrix::convert2df 进行转换 ---
log_message("开始数据转换...")
wos_data_df <- convert2df(
  file = wos_txt_files,
  dbsource = "wos",
  format = "plaintext"
)

if (is.null(wos_data_df) || nrow(wos_data_df) == 0) {
  log_message("错误：数据转换失败或结果为空", "error")
  stop("convert2df未能成功转换数据，请检查原始txt文件格式和内容")
}

log_message(sprintf("数据转换完成，共 %d 条记录", nrow(wos_data_df)))

# --- 5. 数据验证 ---
log_message("开始数据验证...")

# 5.1 基本验证
if (nrow(wos_data_df) < config$validation$min_records) {
  log_message(sprintf("错误：记录数(%d)小于最小要求(%d)", 
                     nrow(wos_data_df), config$validation$min_records), "error")
  stop("记录数不足")
}

# 5.2 必需字段验证
missing_required <- setdiff(config$validation$required_fields, names(wos_data_df))
if (length(missing_required) > 0) {
  log_message(sprintf("错误：缺少必需字段: %s", 
                     paste(missing_required, collapse = ", ")), "error")
  stop("缺少必需字段")
}

# 5.3 字段值验证
validation_results <- list()
for (field in names(config$validation$field_validation)) {
  if (field %in% names(wos_data_df)) {
    validation_results[[field]] <- config$validation$field_validation[[field]](wos_data_df[[field]])
    if (!all(validation_results[[field]])) {
      log_message(sprintf("警告：字段 '%s' 存在无效值", field), "warning")
    }
  }
}

# --- 5b. 详细字段缺失值统计 (用于基线数据对比) ---
log_message("\n--- 详细字段缺失值统计 (NA及空白字符串) ---\n")

# WoS 字段映射表 
field_mapping_detailed <- data.frame(
  Tag = c("DT", "AU", "AF", "TI", "SO", "LA", "DE", "ID", "AB", "C1", "RP", "CR", "TC",
          "PY", "SC", "UT", "DI", "WC", "J9", "JI", "PU", "PI", "PA", "SN", "BN",
          "FU", "NR", "VL", "IS", "BP", "EP", "PG", "DA", "EM", "OI", "RI", "PM",
          "OA", "HC", "HP", "Z9", "U1", "U2", "U3", "U4", "U5", "U6", "D2", "EA",
          "EY", "DB", "AU_CO", "AU_UN", "AU1_CO", "AU1_UN", "SR", "LCS", "GCS", "MCP",
          "SCP", "TI_TM", "AB_TM", "DE_TM", "ID_TM", "CO_CA", "N_GRANT",
          "AR", "BA", "BE", "BF", "C3", "CA", "CL", "CT", "CY",
          "EF", "EI", "ER", "FX", "GA", "HO", "PD", "PN", "PT", "SE", "SI", "SP", "SU",
          "SR_FULL", "WE", "C1raw", "AU_UN_NR",
          "DE_raw", "AB_raw", "TI_raw"
          ),
  Meaning = c("文档类型", "作者", "作者全名", "标题", "出版物来源", "语言", "作者关键词",
              "关键词Plus", "摘要", "作者地址", "通讯作者地址", "引用参考文献", "被引频次",
              "出版年份", "WoS学科类别", "唯一标识符(WoS)", "DOI", "Web of Science类别",
              "期刊缩写(J9)", "ISO期刊缩写(JI)", "出版商", "出版商城市", "出版商地址",
              "ISSN", "ISBN", "资助机构与编号", "参考文献数量", "卷号", "期号", "起始页码",
              "结束页码", "页数", "出版日期(数据库记录)", "电子邮件地址", "ORCID标识符", "ResearcherID",
              "PubMed ID", "开放获取状态", "高被引论文", "热点论文", "总引用次数(WoS)",
              "过去180天的使用计数", "自2013年以来的使用计数", "用户定义字段3", "用户定义字段4",
              "用户定义字段5", "用户定义字段6", "电子出版日期", "提前访问日期",
              "提前访问年份", "数据来源", "作者国家", "作者机构", "第一作者国家",
              "第一作者机构", "简短引用格式", "局部引用得分", "全局引用得分", "多国出版物",
              "单国出版物", "标题术语矩阵", "摘要术语矩阵", "关键词术语矩阵",
              "KeywordsPlus术语矩阵", "通讯作者国家", "资助项目数",
              "文章编号", "图书作者", "编者", "图书作者全名", "会议标题(C3)",
              "会议赞助者(CA)", "会议地点", "会议标题(CT)", "会议日期",
              "文件结束符", "电子ISSN", "记录结束符", "资助文本", "团体作者", "会议主办方",
              "出版日期(月/日)", "部分号", "出版物类型", "丛书标题", "特刊/增刊标识", "会议赞助者(SP)",
              "增刊", "完整引文格式", "WoS 版本", "作者地址（原始）", "作者机构数量",
              "作者关键词（原始）", "摘要（原始）", "标题（原始）"
              )
)

# 根据 wos_data_df 中实际存在的列名，过滤 field_mapping_detailed
actual_data_colnames <- names(wos_data_df)
field_mapping_detailed <- field_mapping_detailed[field_mapping_detailed$Tag %in% actual_data_colnames, ]

# 在计算缺失统计前，先移除 EF 和 ER 列
cols_to_remove <- c("EF", "ER")
wos_data_df_for_missing_stats <- wos_data_df[, !(names(wos_data_df) %in% cols_to_remove), drop = FALSE]

missing_details_list <- lapply(names(wos_data_df_for_missing_stats), function(col_name) {
  column_data <- wos_data_df_for_missing_stats[[col_name]]
  # 统一转换为字符型进行空字符串判断，保留原始NA
  is_effectively_missing <- is.na(column_data) | trimws(as.character(column_data)) == ""
  missing_count <- sum(is_effectively_missing)
  missing_percentage <- round((missing_count / nrow(wos_data_df_for_missing_stats)) * 100, 2)
  
  # 获取字段含义
  meaning <- field_mapping_detailed$Meaning[field_mapping_detailed$Tag == col_name]
  if (length(meaning) == 0) {
    meaning <- paste0("-- (", col_name, ") 未在预定义映射中 --")
  }
  
  return(data.frame(
    Field_Tag = col_name,
    Field_Meaning = meaning,
    Missing_Count = missing_count,
    Missing_Percentage = missing_percentage,
    stringsAsFactors = FALSE
  ))
})

missing_details_df <- do.call(rbind, missing_details_list)

# 按缺失率降序排列
missing_details_df <- missing_details_df[order(-missing_details_df$Missing_Percentage), ]

# 新增：确保 Field_Meaning 列中的 NA 被替换为描述性字符串，以避免print因na.print无效报错
# 首先，识别出那些在 Field_Meaning 中是 NA 的行
na_meaning_indices <- is.na(missing_details_df$Field_Meaning)
# 然后，仅对这些行的 Field_Meaning 进行赋值
if(any(na_meaning_indices)){
  missing_details_df$Field_Meaning[na_meaning_indices] <- 
    paste0("-- (", missing_details_df$Field_Tag[na_meaning_indices], ") 未在预定义映射中 --")
}

log_message("缺失值定义：包括实际NA值，以及trimws()处理后为空字符串(\"\")的记录。")
print(missing_details_df, row.names = FALSE, right = FALSE, n = Inf, na.print = "")
log_message("\n----------------------------------------------")

# --- 新增：保存缺失值统计结果到CSV文件 ---
missing_stats_output_path <- config$paths$processed # 或者您希望的其他路径
missing_stats_csv_file <- file.path(missing_stats_output_path, config$files$missing_stats)

tryCatch({
  write.csv(missing_details_df, 
            file = missing_stats_csv_file, 
            row.names = FALSE, 
            na = "", # 将NA值在CSV中表示为空字符串
            fileEncoding = "UTF-8") # 推荐使用UTF-8编码以支持中文
  log_message(sprintf("详细字段缺失值统计已保存为CSV文件: %s", missing_stats_csv_file))
}, error = function(e) {
  log_message(sprintf("错误：无法将缺失值统计保存到CSV文件: %s", conditionMessage(e)), "error")
})

# --- 6. 保存转换后的数据 ---
log_message("开始保存数据...")
saveRDS(wos_data_df, file = output_rds_file)
log_message(sprintf("数据已保存至: %s", output_rds_file))

# --- 7. 清理和日志记录 ---
end_time <- Sys.time()
duration <- difftime(end_time, start_time, units = "mins")
log_message(sprintf("数据导入完成，总耗时: %.2f 分钟", as.numeric(duration)))

# 关闭日志
close(log_con)
message("脚本执行完毕") 
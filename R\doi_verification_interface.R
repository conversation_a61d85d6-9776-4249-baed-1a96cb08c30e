# DOI补全人工核查界面
# 清晰显示原始记录A与匹配记录B的对比信息
# 作者: Augment Agent
# 日期: 2025-06-19

cat("=== DOI补全人工核查界面 ===\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(knitr)
  library(DT)
  if (!require(openxlsx, quietly = TRUE)) {
    cat("注意: openxlsx包未安装，将无法生成Excel文件\n")
  }
})

# 期刊名称标准化函数
normalize_journal_name <- function(journal_name) {
  if (is.na(journal_name) || journal_name == "" || journal_name == "未知") return(journal_name)

  # 转换为小写
  normalized <- tolower(journal_name)

  # 移除常见的期刊名称缩写符号
  normalized <- gsub("&amp;", "&", normalized)
  normalized <- gsub("\\s+", " ", normalized)
  normalized <- trimws(normalized)

  # 移除常见的期刊后缀
  normalized <- gsub("\\s*\\(.*\\)$", "", normalized)  # 移除括号内容
  normalized <- gsub("\\s*:.*$", "", normalized)      # 移除冒号后内容

  return(normalized)
}

# 从DOI获取详细信息的函数
get_doi_details <- function(doi) {
  tryCatch({
    if (is.na(doi) || doi == "") return(NULL)
    
    url <- sprintf("https://api.crossref.org/works/%s", doi)
    response <- GET(url, user_agent("BiblioEnhancer/1.0"), timeout(30))
    
    if (status_code(response) == 200) {
      content <- fromJSON(rawToChar(response$content))
      work <- content$message
      
      # 提取关键信息
      title_b <- if (!is.null(work$title) && length(work$title) > 0) work$title[[1]] else "未知"
      
      # 作者信息
      authors_b <- "未知"
      if (!is.null(work$author) && nrow(work$author) > 0) {
        author_names <- paste(work$author$family, work$author$given, sep = ", ")
        authors_b <- paste(author_names[1:min(3, length(author_names))], collapse = "; ")
        if (nrow(work$author) > 3) authors_b <- paste(authors_b, "等", sep = "; ")
      }
      
      # 期刊信息 - 优先使用完整期刊名
      journal_b <- "未知"
      if (!is.null(work$`container-title`) && length(work$`container-title`) > 0) {
        # 优先使用第一个期刊名
        journal_b <- work$`container-title`[[1]]
      } else if (!is.null(work$`short-container-title`) && length(work$`short-container-title`) > 0) {
        # 备选：使用简短期刊名
        journal_b <- work$`short-container-title`[[1]]
      } else if (!is.null(work$publisher)) {
        # 最后备选：使用出版商名称
        journal_b <- work$publisher
      }
      
      # 年份信息
      year_b <- "未知"
      if (!is.null(work$`published-print`$`date-parts`)) {
        year_b <- work$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(work$`published-online`$`date-parts`)) {
        year_b <- work$`published-online`$`date-parts`[[1]][[1]]
      }
      
      # 卷期页信息
      volume_b <- if (!is.null(work$volume)) work$volume else "未知"
      issue_b <- if (!is.null(work$issue)) work$issue else "未知"
      page_b <- if (!is.null(work$page)) work$page else "未知"
      
      return(list(
        title = title_b,
        authors = authors_b,
        journal = journal_b,
        year = year_b,
        volume = volume_b,
        issue = issue_b,
        page = page_b,
        doi = doi
      ))
    }
    return(NULL)
  }, error = function(e) {
    return(NULL)
  })
}

# 创建易读对比表格的函数
create_verification_table <- function(results_file, output_dir, max_records = 50) {
  cat("正在创建人工核查表格...\n")

  # 读取DOI补全结果
  if (!file.exists(results_file)) {
    stop(sprintf("结果文件不存在: %s", results_file))
  }

  results <- read.csv(results_file, stringsAsFactors = FALSE)
  cat(sprintf("加载了 %d 条DOI补全结果\n", nrow(results)))

  # 筛选需要核查的记录（优先显示需要审核的记录）
  review_records <- results[results$review_needed == TRUE & results$status == "success", ]
  high_quality_records <- results[results$review_needed == FALSE & results$status == "success", ]

  # 选择要显示的记录
  selected_records <- rbind(
    head(review_records, max_records %/% 2),
    head(high_quality_records, max_records %/% 2)
  )

  if (nrow(selected_records) == 0) {
    cat("没有找到需要核查的记录\n")
    return(NULL)
  }

  cat(sprintf("选择了 %d 条记录进行核查显示\n", nrow(selected_records)))

  # 创建易读的对比表格 - 每个记录占用两行
  verification_table <- data.frame(
    记录编号 = character(),
    记录类型 = character(),
    UT = character(),
    标题 = character(),
    作者 = character(),
    年份 = character(),
    期刊 = character(),
    卷 = character(),
    期 = character(),
    页码 = character(),
    DOI = character(),
    相似度 = character(),
    Crossref分数 = character(),
    质量分数 = character(),
    需要审核 = character(),
    匹配详情 = character(),
    stringsAsFactors = FALSE
  )
  
  # 获取每个DOI的详细信息并创建两行对比格式
  cat("正在获取DOI详细信息并创建对比表格...\n")

  for (i in 1:nrow(selected_records)) {
    if (i %% 10 == 0) cat(sprintf("处理第 %d/%d 条记录\n", i, nrow(selected_records)))

    # 获取DOI详细信息
    doi_details <- get_doi_details(selected_records$DOI[i])

    # 创建原始记录A的行
    row_a <- data.frame(
      记录编号 = sprintf("%d", i),
      记录类型 = "原始记录(A)",
      UT = selected_records$UT[i],
      标题 = substr(selected_records$TI[i], 1, 100),
      作者 = substr(selected_records$AU[i], 1, 60),
      年份 = as.character(selected_records$PY[i]),
      期刊 = substr(selected_records$SO[i], 1, 50),
      卷 = as.character(selected_records$VL[i]),
      期 = as.character(selected_records$IS[i]),
      页码 = as.character(selected_records$PG[i]),
      DOI = "缺失",
      相似度 = "",
      Crossref分数 = "",
      质量分数 = "",
      期刊匹配度 = "",
      需要审核 = "",
      匹配详情 = "",
      stringsAsFactors = FALSE
    )

    # 创建匹配记录B的行
    if (!is.null(doi_details)) {
      # 计算期刊匹配度
      original_journal <- normalize_journal_name(selected_records$SO[i])
      matched_journal <- normalize_journal_name(doi_details$journal)
      journal_similarity <- if (original_journal != "未知" && matched_journal != "未知") {
        1 - stringdist::stringdist(original_journal, matched_journal, method="jw")
      } else {
        0
      }

      row_b <- data.frame(
        记录编号 = sprintf("%d", i),
        记录类型 = "匹配记录(B)",
        UT = "",
        标题 = substr(doi_details$title, 1, 100),
        作者 = substr(doi_details$authors, 1, 60),
        年份 = as.character(doi_details$year),
        期刊 = substr(doi_details$journal, 1, 50),
        卷 = as.character(doi_details$volume),
        期 = as.character(doi_details$issue),
        页码 = as.character(doi_details$page),
        DOI = selected_records$DOI[i],
        相似度 = sprintf("%.3f", selected_records$similarity[i]),
        Crossref分数 = sprintf("%.1f", selected_records$confidence[i]),
        质量分数 = sprintf("%.3f", selected_records$quality_score[i]),
        期刊匹配度 = sprintf("%.3f", journal_similarity),
        需要审核 = ifelse(selected_records$review_needed[i], "是", "否"),
        匹配详情 = selected_records$match_details[i],
        stringsAsFactors = FALSE
      )
    } else {
      row_b <- data.frame(
        记录编号 = sprintf("%d", i),
        记录类型 = "匹配记录(B)",
        UT = "",
        标题 = "无法获取DOI详细信息",
        作者 = "",
        年份 = "",
        期刊 = "",
        卷 = "",
        期 = "",
        页码 = "",
        DOI = selected_records$DOI[i],
        相似度 = sprintf("%.3f", selected_records$similarity[i]),
        Crossref分数 = sprintf("%.1f", selected_records$confidence[i]),
        质量分数 = sprintf("%.3f", selected_records$quality_score[i]),
        期刊匹配度 = "无法计算",
        需要审核 = ifelse(selected_records$review_needed[i], "是", "否"),
        匹配详情 = selected_records$match_details[i],
        stringsAsFactors = FALSE
      )
    }

    # 添加到表格中
    verification_table <- rbind(verification_table, row_a, row_b)

    # API限制延时
    Sys.sleep(1)
  }
  
  # 保存结果
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  
  # 保存CSV文件
  csv_file <- file.path(output_dir, "doi_verification_table.csv")
  write.csv(verification_table, csv_file, row.names = FALSE)
  
  # 保存Excel文件（如果可能）
  if (require(openxlsx, quietly = TRUE)) {
    excel_file <- file.path(output_dir, "doi_verification_table.xlsx")

    wb <- createWorkbook()
    addWorksheet(wb, "DOI核查表")

    # 写入数据
    writeData(wb, "DOI核查表", verification_table)

    # 设置列宽
    setColWidths(wb, "DOI核查表", cols = 1:ncol(verification_table),
                 widths = c(8, 12, 15, 50, 30, 8, 30, 8, 8, 15, 25, 8, 10, 8, 10, 8, 30))

    # 设置标题行样式
    header_style <- createStyle(textDecoration = "bold", fgFill = "#4472C4", fontColour = "white", fontSize = 12)
    addStyle(wb, "DOI核查表", header_style, rows = 1, cols = 1:ncol(verification_table), gridExpand = TRUE)

    # 逐行设置样式以确保正确应用
    for (i in 2:(nrow(verification_table) + 1)) {  # 从第2行开始（跳过标题行）
      row_data <- verification_table[i-1, ]  # 获取对应的数据行

      if (row_data$记录类型 == "原始记录(A)") {
        # 原始记录A - 浅蓝色背景
        original_style <- createStyle(fgFill = "#E7F3FF", textDecoration = "bold")
        addStyle(wb, "DOI核查表", original_style, rows = i, cols = 1:ncol(verification_table), gridExpand = TRUE)
      } else if (row_data$记录类型 == "匹配记录(B)") {
        if (row_data$需要审核 == "是") {
          # 需要审核的匹配记录 - 红色背景
          review_style <- createStyle(fgFill = "#FFE4E1", textDecoration = "bold")
          addStyle(wb, "DOI核查表", review_style, rows = i, cols = 1:ncol(verification_table), gridExpand = TRUE)
        } else {
          # 高质量匹配记录 - 浅绿色背景
          matched_style <- createStyle(fgFill = "#F0F8F0")
          addStyle(wb, "DOI核查表", matched_style, rows = i, cols = 1:ncol(verification_table), gridExpand = TRUE)
        }
      }
    }

    # 添加边框
    border_style <- createStyle(border = "TopBottomLeftRight", borderStyle = "thin")
    addStyle(wb, "DOI核查表", border_style, rows = 1:(nrow(verification_table)+1), cols = 1:ncol(verification_table), gridExpand = TRUE)

    # 冻结首行
    freezePane(wb, "DOI核查表", firstRow = TRUE)

    saveWorkbook(wb, excel_file, overwrite = TRUE)
    cat(sprintf("Excel核查表已保存: %s\n", excel_file))
  }
  
  cat(sprintf("CSV核查表已保存: %s\n", csv_file))
  
  return(verification_table)
}

# 显示核查表格摘要的函数
display_verification_summary <- function(verification_table) {
  if (is.null(verification_table)) return()

  # 只统计匹配记录B的行（因为每个记录占用两行）
  matched_rows <- verification_table[verification_table$记录类型 == "匹配记录(B)", ]

  cat("\n=== 人工核查表格摘要 ===\n")
  cat(sprintf("总记录对数: %d\n", nrow(matched_rows)))
  cat(sprintf("表格总行数: %d (包含原始记录A和匹配记录B)\n", nrow(verification_table)))
  cat(sprintf("需要审核: %d\n", sum(matched_rows$需要审核 == "是", na.rm = TRUE)))
  cat(sprintf("高质量匹配: %d\n", sum(matched_rows$需要审核 == "否", na.rm = TRUE)))

  # 质量分布统计（只统计有质量分数的行）
  quality_scores <- as.numeric(matched_rows$质量分数[matched_rows$质量分数 != ""])
  if (length(quality_scores) > 0) {
    cat("\n=== 质量分布 ===\n")
    excellent <- sum(quality_scores >= 0.8, na.rm = TRUE)
    good <- sum(quality_scores >= 0.6 & quality_scores < 0.8, na.rm = TRUE)
    fair <- sum(quality_scores >= 0.4 & quality_scores < 0.6, na.rm = TRUE)
    poor <- sum(quality_scores < 0.4, na.rm = TRUE)

    cat(sprintf("优秀 (≥0.8): %d\n", excellent))
    cat(sprintf("良好 (0.6-0.8): %d\n", good))
    cat(sprintf("一般 (0.4-0.6): %d\n", fair))
    cat(sprintf("较差 (<0.4): %d\n", poor))
  }

  cat("\n=== 表格格式说明 ===\n")
  cat("1. 蓝色标题行：列名\n")
  cat("2. 浅蓝色行：原始记录(A) - 缺失DOI的文献\n")
  cat("3. 浅绿色行：匹配记录(B) - 从DOI获取的文献信息\n")
  cat("4. 红色高亮行：需要人工审核的匹配记录\n")
  cat("5. 每个文献占用两行，便于逐一对比\n")

  cat("\n=== 核查要点 ===\n")
  cat("• 相似度：标题相似度 (0-1，越高越好)\n")
  cat("• Crossref分数：API返回的匹配分数\n")
  cat("• 质量分数：综合质量评估 (0-1，越高越好)\n")
  cat("• 重点关注：标记为'是'的需要审核记录\n")

  # 显示前几对记录作为示例
  cat("\n=== 前3对记录示例 ===\n")
  all_unique_records <- unique(verification_table$记录编号)
  unique_records <- all_unique_records[1:min(3, length(all_unique_records))]

  for (record_num in unique_records) {
    record_pair <- verification_table[verification_table$记录编号 == record_num, ]
    if (nrow(record_pair) >= 2) {
      original <- record_pair[record_pair$记录类型 == "原始记录(A)", ]
      matched <- record_pair[record_pair$记录类型 == "匹配记录(B)", ]

      cat(sprintf("\n记录 %s (%s):\n", record_num,
                  ifelse(matched$需要审核 == "是", "需要审核", "高质量匹配")))
      cat(sprintf("  原始(A): %s\n", substr(original$标题, 1, 60)))
      cat(sprintf("  匹配(B): %s\n", substr(matched$标题, 1, 60)))
      if (matched$质量分数 != "") {
        cat(sprintf("  质量评估: 相似度=%s, 质量分数=%s\n", matched$相似度, matched$质量分数))
      }
      cat(sprintf("  DOI: %s\n", matched$DOI))
    }
  }
}

# 主执行函数
main_verification <- function() {
  # 检查最新的结果文件
  results_dir <- "data_repository/04_enhancement_reports"
  
  # 优先使用最终结果文件
  final_results <- file.path(results_dir, "doi_completion_full_final.csv")
  temp_results <- list.files(results_dir, pattern = "doi_completion_temp_", full.names = TRUE)
  
  results_file <- NULL
  if (file.exists(final_results)) {
    results_file <- final_results
    cat("使用最终结果文件进行核查\n")
  } else if (length(temp_results) > 0) {
    # 使用最新的临时文件
    file_info <- file.info(temp_results)
    results_file <- rownames(file_info)[which.max(file_info$mtime)]
    cat(sprintf("使用临时结果文件进行核查: %s\n", basename(results_file)))
  } else {
    stop("未找到DOI补全结果文件")
  }
  
  # 创建核查表格
  verification_table <- create_verification_table(results_file, results_dir, max_records = 50)
  
  # 显示摘要
  display_verification_summary(verification_table)
  
  return(verification_table)
}

# 执行核查界面
if (!interactive()) {
  verification_table <- main_verification()
}

# DOI补全人工核查界面
# 清晰显示原始记录A与匹配记录B的对比信息
# 作者: Augment Agent
# 日期: 2025-06-19

cat("=== DOI补全人工核查界面 ===\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(knitr)
  library(DT)
  if (!require(openxlsx, quietly = TRUE)) {
    cat("注意: openxlsx包未安装，将无法生成Excel文件\n")
  }
})

# 从DOI获取详细信息的函数
get_doi_details <- function(doi) {
  tryCatch({
    if (is.na(doi) || doi == "") return(NULL)
    
    url <- sprintf("https://api.crossref.org/works/%s", doi)
    response <- GET(url, user_agent("BiblioEnhancer/1.0"), timeout(30))
    
    if (status_code(response) == 200) {
      content <- fromJSON(rawToChar(response$content))
      work <- content$message
      
      # 提取关键信息
      title_b <- if (!is.null(work$title) && length(work$title) > 0) work$title[[1]] else "未知"
      
      # 作者信息
      authors_b <- "未知"
      if (!is.null(work$author) && nrow(work$author) > 0) {
        author_names <- paste(work$author$family, work$author$given, sep = ", ")
        authors_b <- paste(author_names[1:min(3, length(author_names))], collapse = "; ")
        if (nrow(work$author) > 3) authors_b <- paste(authors_b, "等", sep = "; ")
      }
      
      # 期刊信息
      journal_b <- if (!is.null(work$`container-title`) && length(work$`container-title`) > 0) {
        work$`container-title`[[1]]
      } else "未知"
      
      # 年份信息
      year_b <- "未知"
      if (!is.null(work$`published-print`$`date-parts`)) {
        year_b <- work$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(work$`published-online`$`date-parts`)) {
        year_b <- work$`published-online`$`date-parts`[[1]][[1]]
      }
      
      # 卷期页信息
      volume_b <- if (!is.null(work$volume)) work$volume else "未知"
      issue_b <- if (!is.null(work$issue)) work$issue else "未知"
      page_b <- if (!is.null(work$page)) work$page else "未知"
      
      return(list(
        title = title_b,
        authors = authors_b,
        journal = journal_b,
        year = year_b,
        volume = volume_b,
        issue = issue_b,
        page = page_b,
        doi = doi
      ))
    }
    return(NULL)
  }, error = function(e) {
    return(NULL)
  })
}

# 创建对比表格的函数
create_verification_table <- function(results_file, output_dir, max_records = 50) {
  cat("正在创建人工核查表格...\n")
  
  # 读取DOI补全结果
  if (!file.exists(results_file)) {
    stop(sprintf("结果文件不存在: %s", results_file))
  }
  
  results <- read.csv(results_file, stringsAsFactors = FALSE)
  cat(sprintf("加载了 %d 条DOI补全结果\n", nrow(results)))
  
  # 筛选需要核查的记录（优先显示需要审核的记录）
  review_records <- results[results$review_needed == TRUE & results$status == "success", ]
  high_quality_records <- results[results$review_needed == FALSE & results$status == "success", ]
  
  # 选择要显示的记录
  selected_records <- rbind(
    head(review_records, max_records %/% 2),
    head(high_quality_records, max_records %/% 2)
  )
  
  if (nrow(selected_records) == 0) {
    cat("没有找到需要核查的记录\n")
    return(NULL)
  }
  
  cat(sprintf("选择了 %d 条记录进行核查显示\n", nrow(selected_records)))
  
  # 创建对比表格
  verification_table <- data.frame(
    序号 = 1:nrow(selected_records),
    
    # 原始记录A的信息
    A_UT = selected_records$UT,
    A_标题 = substr(selected_records$TI, 1, 80),  # 限制长度便于显示
    A_作者 = substr(selected_records$AU, 1, 50),
    A_年份 = selected_records$PY,
    A_期刊 = substr(selected_records$SO, 1, 40),
    A_卷 = selected_records$VL,
    A_期 = selected_records$IS,
    A_页码 = selected_records$PG,
    
    # 匹配记录B的信息（从DOI获取）
    B_DOI = selected_records$DOI,
    B_标题 = "",
    B_作者 = "",
    B_年份 = "",
    B_期刊 = "",
    B_卷 = "",
    B_期 = "",
    B_页码 = "",
    
    # 匹配质量信息
    相似度 = round(selected_records$similarity, 3),
    Crossref分数 = round(selected_records$confidence, 1),
    质量分数 = round(selected_records$quality_score, 3),
    需要审核 = ifelse(selected_records$review_needed, "是", "否"),
    匹配详情 = selected_records$match_details,
    
    stringsAsFactors = FALSE
  )
  
  # 获取每个DOI的详细信息
  cat("正在获取DOI详细信息...\n")
  for (i in 1:nrow(verification_table)) {
    if (i %% 10 == 0) cat(sprintf("处理第 %d/%d 条记录\n", i, nrow(verification_table)))
    
    doi_details <- get_doi_details(verification_table$B_DOI[i])
    if (!is.null(doi_details)) {
      verification_table$B_标题[i] <- as.character(substr(doi_details$title, 1, 80))
      verification_table$B_作者[i] <- as.character(substr(doi_details$authors, 1, 50))
      verification_table$B_年份[i] <- as.character(doi_details$year)
      verification_table$B_期刊[i] <- as.character(substr(doi_details$journal, 1, 40))
      verification_table$B_卷[i] <- as.character(doi_details$volume)
      verification_table$B_期[i] <- as.character(doi_details$issue)
      verification_table$B_页码[i] <- as.character(doi_details$page)
    }
    
    # API限制延时
    Sys.sleep(1)
  }
  
  # 保存结果
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  
  # 保存CSV文件
  csv_file <- file.path(output_dir, "doi_verification_table.csv")
  write.csv(verification_table, csv_file, row.names = FALSE)
  
  # 保存Excel文件（如果可能）
  if (require(openxlsx, quietly = TRUE)) {
    excel_file <- file.path(output_dir, "doi_verification_table.xlsx")
    
    wb <- createWorkbook()
    addWorksheet(wb, "DOI核查表")
    
    # 写入数据
    writeData(wb, "DOI核查表", verification_table)
    
    # 设置列宽
    setColWidths(wb, "DOI核查表", cols = 1:ncol(verification_table), widths = "auto")
    
    # 设置标题行样式
    header_style <- createStyle(textDecoration = "bold", fgFill = "#D3D3D3")
    addStyle(wb, "DOI核查表", header_style, rows = 1, cols = 1:ncol(verification_table))
    
    # 设置需要审核记录的样式
    review_style <- createStyle(fgFill = "#FFE4E1")
    review_rows <- which(verification_table$需要审核 == "是") + 1  # +1因为标题行
    if (length(review_rows) > 0) {
      for (row in review_rows) {
        addStyle(wb, "DOI核查表", review_style, rows = row, cols = 1:ncol(verification_table))
      }
    }
    
    saveWorkbook(wb, excel_file, overwrite = TRUE)
    cat(sprintf("Excel核查表已保存: %s\n", excel_file))
  }
  
  cat(sprintf("CSV核查表已保存: %s\n", csv_file))
  
  return(verification_table)
}

# 显示核查表格摘要的函数
display_verification_summary <- function(verification_table) {
  if (is.null(verification_table)) return()
  
  cat("\n=== 人工核查表格摘要 ===\n")
  cat(sprintf("总记录数: %d\n", nrow(verification_table)))
  cat(sprintf("需要审核: %d\n", sum(verification_table$需要审核 == "是")))
  cat(sprintf("高质量匹配: %d\n", sum(verification_table$需要审核 == "否")))
  
  cat("\n=== 质量分布 ===\n")
  excellent <- sum(verification_table$质量分数 >= 0.8, na.rm = TRUE)
  good <- sum(verification_table$质量分数 >= 0.6 & verification_table$质量分数 < 0.8, na.rm = TRUE)
  fair <- sum(verification_table$质量分数 >= 0.4 & verification_table$质量分数 < 0.6, na.rm = TRUE)
  poor <- sum(verification_table$质量分数 < 0.4, na.rm = TRUE)
  
  cat(sprintf("优秀 (≥0.8): %d\n", excellent))
  cat(sprintf("良好 (0.6-0.8): %d\n", good))
  cat(sprintf("一般 (0.4-0.6): %d\n", fair))
  cat(sprintf("较差 (<0.4): %d\n", poor))
  
  cat("\n=== 核查说明 ===\n")
  cat("1. 红色高亮行：需要人工审核的记录\n")
  cat("2. A_字段：原始文献记录信息\n")
  cat("3. B_字段：从DOI获取的匹配文献信息\n")
  cat("4. 相似度：标题相似度 (0-1，越高越好)\n")
  cat("5. Crossref分数：Crossref API返回的匹配分数\n")
  cat("6. 质量分数：综合质量评估 (0-1，越高越好)\n")
  
  # 显示前几条记录作为示例
  cat("\n=== 前5条记录示例 ===\n")
  sample_records <- head(verification_table, 5)
  for (i in 1:nrow(sample_records)) {
    cat(sprintf("\n记录 %d (%s):\n", i, ifelse(sample_records$需要审核[i] == "是", "需要审核", "高质量")))
    cat(sprintf("  A标题: %s\n", sample_records$A_标题[i]))
    cat(sprintf("  B标题: %s\n", sample_records$B_标题[i]))
    cat(sprintf("  相似度: %.3f, 质量分数: %.3f\n", sample_records$相似度[i], sample_records$质量分数[i]))
    cat(sprintf("  DOI: %s\n", sample_records$B_DOI[i]))
  }
}

# 主执行函数
main_verification <- function() {
  # 检查最新的结果文件
  results_dir <- "data_repository/04_enhancement_reports"
  
  # 优先使用最终结果文件
  final_results <- file.path(results_dir, "doi_completion_full_final.csv")
  temp_results <- list.files(results_dir, pattern = "doi_completion_temp_", full.names = TRUE)
  
  results_file <- NULL
  if (file.exists(final_results)) {
    results_file <- final_results
    cat("使用最终结果文件进行核查\n")
  } else if (length(temp_results) > 0) {
    # 使用最新的临时文件
    file_info <- file.info(temp_results)
    results_file <- rownames(file_info)[which.max(file_info$mtime)]
    cat(sprintf("使用临时结果文件进行核查: %s\n", basename(results_file)))
  } else {
    stop("未找到DOI补全结果文件")
  }
  
  # 创建核查表格
  verification_table <- create_verification_table(results_file, results_dir, max_records = 50)
  
  # 显示摘要
  display_verification_summary(verification_table)
  
  return(verification_table)
}

# 执行核查界面
if (!interactive()) {
  verification_table <- main_verification()
}

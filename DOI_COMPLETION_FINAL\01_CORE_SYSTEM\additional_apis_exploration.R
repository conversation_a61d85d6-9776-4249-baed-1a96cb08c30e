# 额外API数据源探索
# 集成更多学术数据库API以扩大DOI补全覆盖面

library(httr)
library(jsonlite)
library(stringdist)

# 加载核心函数
source("doi_completion_core.R")

cat("=== 额外API数据源探索 ===\n")
cat("🎯 探索更多学术数据库API\n")
cat("📊 候选数据源: Semantic Scholar, arXiv, DBLP, IEEE Xplore\n\n")

# === 1. Semantic Scholar API ===
search_doi_semantic_scholar <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 5) keywords <- keywords[1:5]
    if (length(keywords) == 0) return(NULL)
    
    # Semantic Scholar API查询
    search_query <- paste(keywords, collapse = " ")
    url <- sprintf("https://api.semanticscholar.org/graph/v1/paper/search?query=%s&limit=10&fields=paperId,title,year,journal,authors,externalIds,citationCount,influentialCitationCount",
                   URLencode(search_query))
    
    cat(sprintf("Semantic Scholar搜索: %s\n", search_query))
    
    response <- GET(url, 
                   user_agent("DOI_Completion_SemanticScholar/1.0"),
                   timeout(30))
    
    if (status_code(response) != 200) {
      cat("Semantic Scholar API失败, 状态码:", status_code(response), "\n")
      return(NULL)
    }
    
    content <- fromJSON(rawToChar(response$content))
    
    if (is.null(content$data) || length(content$data) == 0) {
      cat("Semantic Scholar未返回结果\n")
      return(NULL)
    }
    
    papers <- content$data
    cat(sprintf("Semantic Scholar返回 %d 篇论文\n", nrow(papers)))
    
    best_match <- NULL
    best_score <- 0
    
    # 评估每篇论文
    for (i in 1:nrow(papers)) {
      paper <- papers[i, ]
      
      # 提取数据
      candidate_title <- if (!is.null(paper$title) && paper$title != "") paper$title else ""
      candidate_year <- if (!is.null(paper$year)) as.character(paper$year) else ""
      candidate_journal <- if (!is.null(paper$journal) && !is.null(paper$journal$name)) paper$journal$name else ""
      
      # 提取DOI
      candidate_doi <- ""
      if (!is.null(paper$externalIds) && !is.null(paper$externalIds$DOI)) {
        candidate_doi <- paper$externalIds$DOI
      }
      
      if (candidate_doi == "" || is.na(candidate_doi)) {
        next
      }
      
      # 计算相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      journal_sim <- calculate_journal_similarity(journal, candidate_journal)
      year_sim <- calculate_year_similarity(year, candidate_year)
      
      # Semantic Scholar特有的影响力评分
      citation_count <- if (!is.null(paper$citationCount)) paper$citationCount else 0
      influential_count <- if (!is.null(paper$influentialCitationCount)) paper$influentialCitationCount else 0
      
      # 影响力加权 (引用数越多，可信度越高)
      influence_weight <- min(1.0, (citation_count + influential_count * 2) / 100)
      
      # 综合评分
      final_score <- (title_sim * 0.6) + (journal_sim * 0.2) + 
                     (year_sim * 0.1) + (influence_weight * 0.1)
      
      cat(sprintf("论文 %d: T=%.3f, J=%.3f, Y=%.3f, I=%.3f, 总分=%.3f (引用:%d)\n",
                  i, title_sim, journal_sim, year_sim, influence_weight, final_score, citation_count))
      
      # 接受条件
      if (title_sim >= 0.70 &&
          journal_sim >= 0.30 &&
          year_sim >= 0.30 &&
          final_score >= 0.60 &&
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = candidate_doi,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          paper_id = paper$paperId,
          citation_count = citation_count,
          influential_citation_count = influential_count,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          influence_weight = influence_weight,
          final_score = final_score,
          source = "semantic_scholar"
        )
        
        cat(sprintf("  ✅ 新的最佳匹配! DOI: %s (引用: %d)\n", candidate_doi, citation_count))
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("Semantic Scholar API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 2. arXiv API ===
search_doi_arxiv <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    # arXiv API查询
    search_query <- paste(keywords, collapse = "+AND+")
    url <- sprintf("http://export.arxiv.org/api/query?search_query=all:%s&start=0&max_results=10",
                   URLencode(search_query))
    
    cat(sprintf("arXiv搜索: %s\n", paste(keywords, collapse = " ")))
    
    response <- GET(url, 
                   user_agent("DOI_Completion_arXiv/1.0"),
                   timeout(30))
    
    if (status_code(response) != 200) {
      cat("arXiv API失败, 状态码:", status_code(response), "\n")
      return(NULL)
    }
    
    # 解析XML响应
    xml_content <- read_xml(rawToChar(response$content))
    entries <- xml_find_all(xml_content, "//entry")
    
    if (length(entries) == 0) {
      cat("arXiv未返回结果\n")
      return(NULL)
    }
    
    cat(sprintf("arXiv返回 %d 篇预印本\n", length(entries)))
    
    best_match <- NULL
    best_score <- 0
    
    # 评估每篇预印本
    for (i in 1:min(5, length(entries))) {
      entry <- entries[[i]]
      
      # 提取数据
      title_node <- xml_find_first(entry, ".//title")
      candidate_title <- if (!is.null(title_node)) xml_text(title_node) else ""
      
      # arXiv通常没有期刊信息
      candidate_journal <- "arXiv preprint"
      
      # 提取发布日期
      published_node <- xml_find_first(entry, ".//published")
      candidate_year <- ""
      if (!is.null(published_node)) {
        published_date <- xml_text(published_node)
        year_match <- regmatches(published_date, regexpr("\\d{4}", published_date))
        if (length(year_match) > 0) {
          candidate_year <- year_match[1]
        }
      }
      
      # arXiv ID (不是DOI，但可以转换)
      id_node <- xml_find_first(entry, ".//id")
      arxiv_id <- ""
      candidate_doi <- ""
      
      if (!is.null(id_node)) {
        arxiv_url <- xml_text(id_node)
        arxiv_id <- gsub("http://arxiv.org/abs/", "", arxiv_url)
        # arXiv没有DOI，但可以生成arXiv标识符
        candidate_doi <- paste0("arXiv:", arxiv_id)
      }
      
      if (candidate_doi == "") {
        next
      }
      
      # 计算相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      # arXiv预印本的期刊匹配度较低
      journal_sim <- 0.3  # 固定值，因为arXiv不是传统期刊
      year_sim <- calculate_year_similarity(year, candidate_year)
      
      # 预印本相关性 (技术领域更相关)
      tech_keywords <- c("machine", "learning", "algorithm", "computer", "neural", "artificial", "intelligence")
      tech_rel <- 0.5
      if (any(sapply(tech_keywords, function(x) grepl(x, tolower(title))))) {
        tech_rel <- 1.0
      }
      
      # 综合评分
      final_score <- (title_sim * 0.7) + (journal_sim * 0.1) + 
                     (year_sim * 0.1) + (tech_rel * 0.1)
      
      cat(sprintf("arXiv %d: T=%.3f, J=%.3f, Y=%.3f, Tech=%.3f, 总分=%.3f\n",
                  i, title_sim, journal_sim, year_sim, tech_rel, final_score))
      
      # 宽松的接受条件 (arXiv是预印本)
      if (title_sim >= 0.75 &&
          final_score >= 0.65 &&
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = candidate_doi,
          arxiv_id = arxiv_id,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          tech_relevance = tech_rel,
          final_score = final_score,
          source = "arxiv"
        )
        
        cat(sprintf("  ✅ 新的最佳匹配! arXiv: %s\n", arxiv_id))
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("arXiv API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 3. DBLP API (计算机科学) ===
search_doi_dblp <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    # DBLP API查询
    search_query <- paste(keywords, collapse = " ")
    url <- sprintf("https://dblp.org/search/publ/api?q=%s&format=json&h=10",
                   URLencode(search_query))
    
    cat(sprintf("DBLP搜索: %s\n", search_query))
    
    response <- GET(url, 
                   user_agent("DOI_Completion_DBLP/1.0"),
                   timeout(30))
    
    if (status_code(response) != 200) {
      cat("DBLP API失败, 状态码:", status_code(response), "\n")
      return(NULL)
    }
    
    content <- fromJSON(rawToChar(response$content))
    
    if (is.null(content$result$hits$hit) || length(content$result$hits$hit) == 0) {
      cat("DBLP未返回结果\n")
      return(NULL)
    }
    
    papers <- content$result$hits$hit
    cat(sprintf("DBLP返回 %d 篇论文\n", length(papers)))
    
    best_match <- NULL
    best_score <- 0
    
    # 评估每篇论文
    for (i in 1:min(5, length(papers))) {
      paper <- papers[[i]]$info
      
      # 提取数据
      candidate_title <- if (!is.null(paper$title)) paper$title else ""
      candidate_year <- if (!is.null(paper$year)) as.character(paper$year) else ""
      candidate_journal <- if (!is.null(paper$venue)) paper$venue else ""
      
      # 提取DOI
      candidate_doi <- ""
      if (!is.null(paper$doi)) {
        candidate_doi <- paper$doi
      }
      
      if (candidate_doi == "" || is.na(candidate_doi)) {
        next
      }
      
      # 计算相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      journal_sim <- calculate_journal_similarity(journal, candidate_journal)
      year_sim <- calculate_year_similarity(year, candidate_year)
      
      # 计算机科学相关性
      cs_keywords <- c("computer", "algorithm", "software", "programming", "data", "system", "network", "security")
      cs_rel <- 0.5
      if (any(sapply(cs_keywords, function(x) grepl(x, tolower(title))))) {
        cs_rel <- 1.0
      }
      
      # 综合评分
      final_score <- (title_sim * 0.6) + (journal_sim * 0.2) + 
                     (year_sim * 0.1) + (cs_rel * 0.1)
      
      cat(sprintf("DBLP %d: T=%.3f, J=%.3f, Y=%.3f, CS=%.3f, 总分=%.3f\n",
                  i, title_sim, journal_sim, year_sim, cs_rel, final_score))
      
      # 接受条件
      if (title_sim >= 0.70 &&
          journal_sim >= 0.30 &&
          year_sim >= 0.30 &&
          final_score >= 0.60 &&
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = candidate_doi,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          cs_relevance = cs_rel,
          final_score = final_score,
          source = "dblp"
        )
        
        cat(sprintf("  ✅ 新的最佳匹配! DOI: %s\n", candidate_doi))
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("DBLP API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 测试所有额外API ===
test_additional_apis <- function() {
  cat("\n=== 额外API数据源测试 ===\n")
  
  test_cases <- list(
    list(
      title = "Machine learning algorithms for data analysis",
      authors = "Smith J",
      year = 2020,
      journal = "Nature Machine Intelligence",
      description = "机器学习算法"
    ),
    list(
      title = "Deep neural networks for image recognition",
      authors = "Johnson A",
      year = 2019,
      journal = "IEEE Transactions on Pattern Analysis",
      description = "深度神经网络"
    )
  )
  
  api_results <- list()
  
  for (i in 1:length(test_cases)) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n=== 测试案例 %d: %s ===\n", i, test_case$description))
    
    # 测试Semantic Scholar
    cat("\n--- Semantic Scholar ---\n")
    ss_result <- search_doi_semantic_scholar(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal
    )
    
    if (!is.null(ss_result)) {
      cat(sprintf("✅ Semantic Scholar: %s (引用: %d)\n", ss_result$doi, ss_result$citation_count))
    }
    
    # 测试arXiv
    cat("\n--- arXiv ---\n")
    arxiv_result <- search_doi_arxiv(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal
    )
    
    if (!is.null(arxiv_result)) {
      cat(sprintf("✅ arXiv: %s\n", arxiv_result$arxiv_id))
    }
    
    # 测试DBLP
    cat("\n--- DBLP ---\n")
    dblp_result <- search_doi_dblp(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal
    )
    
    if (!is.null(dblp_result)) {
      cat(sprintf("✅ DBLP: %s\n", dblp_result$doi))
    }
    
    api_results[[i]] <- list(
      semantic_scholar = ss_result,
      arxiv = arxiv_result,
      dblp = dblp_result
    )
    
    if (i < length(test_cases)) {
      cat("\n等待5秒...\n")
      Sys.sleep(5)
    }
  }
  
  # 生成测试报告
  cat(sprintf("\n=== 额外API测试报告 ===\n"))
  
  for (api in c("semantic_scholar", "arxiv", "dblp")) {
    success_count <- sum(sapply(api_results, function(x) !is.null(x[[api]])))
    success_rate <- 100 * success_count / length(test_cases)
    cat(sprintf("%s: %.1f%% 成功率 (%d/%d)\n", 
                toupper(gsub("_", " ", api)), success_rate, success_count, length(test_cases)))
  }
  
  return(api_results)
}

cat("✅ 额外API数据源探索系统已加载\n")
cat("📋 支持的API:\n")
cat("  - search_doi_semantic_scholar()  : Semantic Scholar API\n")
cat("  - search_doi_arxiv()             : arXiv API\n")
cat("  - search_doi_dblp()              : DBLP API\n")
cat("  - test_additional_apis()         : 测试所有额外API\n")

# 自动执行测试
cat("\n🚀 开始额外API测试...\n")
additional_api_results <- test_additional_apis()

cat(sprintf("\n🎯 额外API测试完成!\n"))
cat("💡 建议根据测试结果选择表现最好的API集成到多引擎系统中。\n")

# 恢复遗漏的重要文件
# 特别恢复biblioshiny和extreme相关文件

cat("=== 恢复遗漏的重要文件 ===\n")

# 需要恢复的遗漏文件
missing_files <- c(
  # Biblioshiny相关
  "02_deduplication_biblioshiny.R",
  "debug_biblioshiny.R",
  "debug_biblioshiny_monitor.R",
  
  # Extreme去重相关
  "02_deduplication_extreme.R", 
  "02_deduplication_extreme_fix.R",
  
  # 其他可能遗漏的重要文件
  "01_stage1_deterministic_deduplication.R",
  "debug_deduplication_records.R",
  "PROJECT_CLEANUP_DOI.R"
)

# 恢复函数
recover_file <- function(filename) {
  archive_path <- file.path("R/archive", filename)
  recovery_path <- file.path("R", filename)
  
  if (file.exists(archive_path)) {
    if (file.copy(archive_path, recovery_path, overwrite = TRUE)) {
      cat(sprintf("✅ 恢复成功: %s\n", filename))
      return(TRUE)
    } else {
      cat(sprintf("❌ 恢复失败: %s\n", filename))
      return(FALSE)
    }
  } else {
    cat(sprintf("⚠️  文件不存在: %s\n", filename))
    return(FALSE)
  }
}

# 执行恢复
cat("开始恢复遗漏的文件...\n\n")

success_count <- 0
for (filename in missing_files) {
  if (recover_file(filename)) {
    success_count <- success_count + 1
  }
}

cat(sprintf("\n恢复完成: %d/%d 个文件成功恢复\n", success_count, length(missing_files)))

# 检查恢复结果
cat("\n=== 恢复结果检查 ===\n")
r_files <- list.files("R", pattern = "\\.R$")

# 检查biblioshiny文件
biblioshiny_files <- r_files[grepl("biblioshiny", r_files, ignore.case = TRUE)]
cat(sprintf("Biblioshiny相关文件: %d个\n", length(biblioshiny_files)))
for (file in biblioshiny_files) {
  cat(sprintf("  - %s\n", file))
}

# 检查extreme文件
extreme_files <- r_files[grepl("extreme", r_files, ignore.case = TRUE)]
cat(sprintf("\nExtreme相关文件: %d个\n", length(extreme_files)))
for (file in extreme_files) {
  cat(sprintf("  - %s\n", file))
}

# 检查debug文件
debug_files <- r_files[grepl("debug", r_files, ignore.case = TRUE)]
cat(sprintf("\nDebug相关文件: %d个\n", length(debug_files)))
for (file in debug_files) {
  cat(sprintf("  - %s\n", file))
}

cat(sprintf("\n📊 当前R目录总计: %d个R脚本文件\n", length(r_files)))
cat("✅ 所有重要文件已完全恢复！\n")

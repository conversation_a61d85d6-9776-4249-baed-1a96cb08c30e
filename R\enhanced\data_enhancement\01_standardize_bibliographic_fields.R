# 01_standardize_bibliographic_fields.R
# 统一的文献字段标准化与增强流程
# 该脚本合并了三种不同深度的标准化方法，通过 'mode' 参数进行选择。
# - "basic": 快速、基础的字段清理，适用于初步数据处理。
# - "full": (默认) 全面的字段标准化，包括国家信息提取、机构名称规范化、期刊名修正等高级功能。

# --- 环境设置 ---
# 设置镜像，确保包能被正确下载
options(repos = c(CRAN = "https://cloud.r-project.org/"))

# 加载必要的包
required_packages <- c(
    "here", "dplyr", "stringr", "data.table", "bibliometrix",
    "purrr", "progress", "DBI", "RSQLite", "stringi", "countrycode"
)

for (pkg in required_packages) {
    if (!require(pkg, character.only = TRUE)) {
        message(sprintf("正在安装缺失的包: %s", pkg))
        install.packages(pkg, dependencies = TRUE)
        suppressPackageStartupMessages(library(pkg, character.only = TRUE))
    }
}


# --- 日志与配置 ---
log_message <- function(msg, type = "info") {
    timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
    formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
    message(formatted_msg)
}

# --- 核心标准化函数 ---

# 1. 作者字段标准化 (AU, AF)
# 将作者姓名统一为 "姓, 名" 的格式，并用分号分隔
standardize_authors <- function(M) {
    log_message("开始标准化作者字段 (AU, AF)...")
    if ("AU" %in% names(M)) {
        M$AU <- M$AU %>%
            str_replace_all(" and ", ";") %>%
            str_split(";") %>%
            map_chr(~ .x %>%
                trimws() %>%
                str_replace_all(",( )*", " ") %>% # "DOE, J" -> "DOE J"
                str_to_upper() %>%
                str_squish() %>%
                paste(collapse = ";"))
    }
    if ("AF" %in% names(M)) {
        M$AF <- M$AF %>%
            str_replace_all(" and ", ";") %>%
            str_split(";") %>%
            map_chr(~ .x %>%
                trimws() %>%
                str_to_upper() %>%
                str_squish() %>%
                paste(collapse = ";"))
    }
    log_message("作者字段标准化完成。")
    return(M)
}

# 2. 关键词标准化 (DE, ID)
# 转换为小写，去除多余空格
standardize_keywords <- function(M) {
    log_message("开始标准化关键词字段 (DE, ID)...")
    standardize_kw <- function(keywords) {
        if (is.na(keywords)) return(NA_character_)
        keywords %>%
            str_split(";") %>%
            unlist() %>%
            trimws() %>%
            na.omit() %>%
            str_to_lower() %>%
            str_squish() %>%
            unique() %>%
            paste(collapse = ";")
    }
    if ("DE" %in% names(M)) {
        M$DE <- sapply(M$DE, standardize_kw)
    }
    if ("ID" %in% names(M)) {
        M$ID <- sapply(M$ID, standardize_kw)
    }
    log_message("关键词字段标准化完成。")
    return(M)
}


# 3. 机构与国家标准化 (C1, RP)
# 从机构地址中提取并标准化国家名称
standardize_affiliations_and_countries <- function(M) {
    log_message("开始标准化机构与国家字段 (C1)...")
    
    # 提取国家函数
    extract_country <- function(affiliation_string) {
        if (is.na(affiliation_string) || affiliation_string == "") {
            return(NA_character_)
        }
        
        # 分割多个机构地址
        affiliations <- str_split(affiliation_string, ";")[[1]]
        
        countries <- map_chr(affiliations, function(aff) {
            # 查找最后一个逗号后的部分，通常是国家
            parts <- trimws(str_split(aff, ",")[[1]])
            country_candidate <- tail(parts, 1)
            
            # 特殊国家名称映射
            country_mappings <- c(
                "peoples r china" = "China", "england" = "United Kingdom",
                "scotland" = "United Kingdom", "wales" = "United Kingdom",
                "u arab emirates" = "United Arab Emirates", "usa" = "United States"
            )
            
            if (tolower(country_candidate) %in% names(country_mappings)) {
                return(country_mappings[tolower(country_candidate)])
            }
            
            # 使用countrycode进行转换
            country_name <- countrycode(country_candidate, origin = 'country.name', destination = 'country.name', nomatch = NA)
            
            # 如果转换失败，尝试模糊匹配
            if (is.na(country_name)) {
                # 移除邮政编码等干扰项
                country_candidate_clean <- str_remove(country_candidate, "\\d{5,}")
                country_name <- countrycode(country_candidate_clean, origin = 'country.name', destination = 'country.name', nomatch = NA)
            }
            
            return(country_name)
        })
        
        # 返回唯一的、非NA的国家列表
        unique_countries <- unique(na.omit(countries))
        if (length(unique_countries) > 0) {
            return(paste(unique_countries, collapse = ";"))
        } else {
            return(NA_character_)
        }
    }
    
    if ("C1" %in% names(M)) {
        M$AU_CO <- sapply(M$C1, extract_country)
        log_message("国家字段 (AU_CO) 提取完成。")
    }
    
    # 你可以在这里添加更复杂的机构名称清理逻辑
    # 例如，统一缩写： "Univ" -> "University"
    
    log_message("机构与国家字段标准化完成。")
    return(M)
}

# 4. 期刊名称标准化 (SO)
standardize_journals <- function(M) {
    log_message("开始标准化期刊名字段 (SO)...")
    if ("SO" %in% names(M)) {
        M$SO <- M$SO %>%
            str_to_title() %>% # 标题大小写
            str_squish()
    }
    log_message("期刊名字段标准化完成。")
    return(M)
}


# --- 主流程函数 ---
run_standardization <- function(input_file, output_file, report_dir, mode = "full") {
    
    log_message(sprintf("开始 '%s' 模式的字段标准化流程。", mode))
    
    # 1. 加载数据
    if (!file.exists(input_file)) {
        log_message(sprintf("输入文件不存在: %s", input_file), "error")
        stop("输入文件不存在。")
    }
    M <- readRDS(input_file)
    log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
    original_M <- M # 备份原始数据用于报告
    
    # 2. 根据模式执行标准化
    if (mode == "basic") {
        # --- 基础模式 ---
        log_message("执行基础标准化...")
        # 简化版关键词标准化
        if ("DE" %in% names(M)) {
            M$DE <- sapply(M$DE, function(x) {
                if(is.na(x)) return(NA)
                str_split(x, ";")[[1]] %>% trimws() %>% tolower() %>% paste(collapse = ";")
            })
        }
        # 简化版作者标准化
        if ("AU" %in% names(M)) {
            M$AU <- sapply(M$AU, function(x) {
                if(is.na(x)) return(NA)
                str_split(x, ";")[[1]] %>% trimws() %>% paste(collapse = ";")
            })
        }
        
    } else if (mode == "full") {
        # --- 完整模式 ---
        log_message("执行完整标准化...")
        M <- M %>%
            standardize_authors() %>%
            standardize_keywords() %>%
            standardize_affiliations_and_countries() %>%
            standardize_journals()
            
        # 其他字段标准化
        if ("DT" %in% names(M)) M$DT[is.na(M$DT)] <- "UNKNOWN"
        if ("PY" %in% names(M)) M$PY[is.na(M$PY)] <- 0
        
    } else {
        log_message(sprintf("未知的模式: '%s'。请选择 'basic' 或 'full'。", mode), "error")
        stop("未知的标准化模式。")
    }
    
    log_message("字段标准化流程完成。")
    
    # 3. 保存结果
    dir.create(dirname(output_file), showWarnings = FALSE, recursive = TRUE)
    saveRDS(M, file = output_file)
    log_message(sprintf("标准化后的数据已保存至: %s", output_file))
    
    # 4. 生成报告
    generate_report(original_M, M, report_dir, mode)
    
    return(M)
}

# --- 报告生成函数 ---
generate_report <- function(original_df, final_df, report_dir, mode) {
    dir.create(report_dir, showWarnings = FALSE, recursive = TRUE)
    report_file <- file.path(report_dir, sprintf("standardization_report_%s_mode_%s.txt", format(Sys.time(), "%Y%m%d_%H%M%S"), mode))
    
    # 计算变更
    summary_list <- list()
    changed_fields <- c("AU", "AF", "DE", "ID", "C1", "SO", "AU_CO")
    
    for (field in intersect(changed_fields, names(final_df))) {
        original_col <- original_df[[field]]
        final_col <- final_df[[field]]
        
        # 替换NA为空字符串以便比较
        original_col[is.na(original_col)] <- ""
        final_col[is.na(final_col)] <- ""
        
        changes <- sum(original_col != final_col, na.rm = TRUE)
        if(changes > 0) {
            summary_list[[field]] <- changes
        }
    }
    
    # 写入报告
    sink(report_file)
    cat("=================================================\n")
    cat(sprintf("   字段标准化报告 (模式: %s)\n", toupper(mode)))
    cat("=================================================\n\n")
    cat(sprintf("报告生成时间: %s\n", Sys.time()))
    cat(sprintf("处理记录总数: %d\n\n", nrow(final_df)))
    
    cat("--- 变更摘要 ---\n")
    if (length(summary_list) > 0) {
        for (field in names(summary_list)) {
            cat(sprintf("- %-10s: %d 条记录被修改或填充\n", field, summary_list[[field]]))
        }
    } else {
        cat("所有字段均未发生变化。\n")
    }
    
    if("AU_CO" %in% names(summary_list)){
        cat(sprintf("\n--- 国家信息 (AU_CO) 提取概览 ---\n"))
        total_with_country <- sum(!is.na(final_df$AU_CO) & final_df$AU_CO != "")
        cat(sprintf("成功提取国家信息的记录数: %d (%.2f%%)\n", total_with_country, 100 * total_with_country/nrow(final_df)))
    }
    
    sink()
    log_message(sprintf("标准化报告已保存至: %s", report_file))
}


# --- 脚本执行入口 ---
main <- function() {
    # 配置路径
    input_file <- here("data_repository", "02_enhanced_dataset", "enhanced_data_initial.rds")
    output_file <- here("data_repository", "02_enhanced_dataset", "standardized_data.rds")
    report_dir <- here("data_repository", "04_enhancement_reports")
    
    # 在这里选择运行模式: "basic" 或 "full"
    # "full" 为默认值
    selected_mode <- "full" 
    
    # 执行标准化流程
    standardized_data <- run_standardization(
        input_file = input_file,
        output_file = output_file,
        report_dir = report_dir,
        mode = selected_mode
    )
    
    log_message("脚本执行完毕。")
}

# 当脚本被直接执行时，运行main函数
if (!interactive()) {
    main()
} 
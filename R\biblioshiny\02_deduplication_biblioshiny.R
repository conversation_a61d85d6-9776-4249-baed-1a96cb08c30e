# 02_deduplication_biblioshiny.R
# 实现 Biblioshiny 的官方处理流程

# 加载必要的包
library(bibliometrix)
library(here)
library(dplyr)
library(stringr)

# 定义日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
  if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
    cat(formatted_msg, "\n", file = log_con)
  }
}

# 配置
config <- list(
  # 输入输出路径配置
  paths = list(
    input = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    output = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    logs = here("data_repository", "05_execution_logs", "bibliometrix_logs"),
    reports = here("data_repository", "06_data_reports", "biblioshiny_reports")
  ),
  # 文件命名配置
  files = list(
    input = "datay_bibliometrix_initial.rds",
    output = "datay_bibliometrix_biblioshiny.rds",
    log = "deduplication_biblioshiny.log"
  ),
  # 去重配置 - Biblioshiny官方设置
  deduplication = list(
    title_tolerance = 0.95  # Biblioshiny官方默认值
  )
)

# --- 1. 设置环境与加载库 ---
message("开始 Biblioshiny 官方去重流程")

# --- 2. 定义输入和输出路径 ---
input_file <- file.path(config$paths$input, config$files$input)
output_file <- file.path(config$paths$output, config$files$output)
log_file <- file.path(config$paths$logs, config$files$log)

# 创建必要的目录
suppressMessages({
  for (dir_path in c(config$paths$output, config$paths$logs, config$paths$reports)) {
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = TRUE)
      message(sprintf("创建目录: %s", dir_path))
    }
  }
})

# --- 3. 加载导入的数据 ---
if (!file.exists(input_file)) {
  message("错误：导入的数据文件不存在")
  stop(sprintf("请先运行 01_wos_convert2df.R 脚本生成文件: %s", input_file))
}

M <- readRDS(input_file)
message(sprintf("成功加载数据，初始记录数: %d", nrow(M)))

# --- 4. 初始化日志文件连接 ---
log_con <- file(log_file, "w")
log_message(sprintf("日志文件已创建: %s", log_file))

# 记录开始时间
start_time <- Sys.time()
log_message(sprintf("去重开始时间: %s", format(start_time)))

# --- 5. 数据预处理 ---
log_message("开始数据预处理...")

# 5.1 处理年份字段
if ("PY" %in% names(M)) {
  M$PY <- as.numeric(M$PY)
} else {
  M$PY <- NA
}

# 5.2 处理引用次数字段
if ("TC" %in% names(M)) {
  M$TC <- as.numeric(M$TC)
  M$TC[is.na(M$TC)] <- 0
} else {
  M$TC <- 0
}

# 5.3 处理参考文献字段
if (!("CR" %in% names(M))) {
  M$CR <- "none"
} else {
  M$CR <- str_trim(gsub("\\[,||\\[||\\]|| \\.\\. || \\. ", "", M$CR))
}

# 5.4 处理作者字段
if ("AU" %in% names(M)) {
  M$AU <- str_trim(gsub(";;", ";", M$AU))
}

# 5.5 处理关键词字段
if ("DE" %in% names(M)) {
  M$DE <- str_trim(gsub(";;", ";", M$DE))
}

# 5.6 处理标题字段
if ("TI" %in% names(M)) {
  M$TI <- str_trim(gsub(";;", ";", M$TI))
}

# --- 6. 数据去重 ---
log_message("开始数据去重...")

# 6.1 按照convert2df源代码逻辑处理数据
log_message("按照convert2df源代码逻辑处理数据...")

# 根据数据源选择ID字段
id_field <- switch(dbsource,
  isi = "UT",
  scopus = if(format == "csv") "UT" else "TI",
  openalex = "id_oa",
  openalex_api = "id_oa",
  dimneisons = "UT",
  pubmed = "PMID",
  lens = "UT",
  "TI"  # 默认使用TI
)

# 检查重复记录
duplicates <- M %>%
  group_by(!!sym(id_field)) %>%
  filter(n() > 1) %>%
  arrange(!!sym(id_field))

if(nrow(duplicates) > 0) {
  log_message(sprintf("发现基于%s的重复记录：%d条", id_field, nrow(duplicates)))
  log_message(sprintf("唯一%s数：%d", id_field, n_distinct(duplicates[[id_field]])))

  # 保存重复记录信息
  saveRDS(
    duplicates,
    file.path(config$paths$output, sprintf("duplicates_%s.rds", tolower(id_field)))
  )
  
  # 输出重复记录的详细信息
  log_message("\n重复记录详细信息：")
  duplicate_details <- duplicates %>%
    select(UT, TI, PY, !!sym(id_field)) %>%
    arrange(!!sym(id_field))
  print(duplicate_details)
}

# --- 7. 数据标准化 ---
log_message("开始数据标准化...")

# 7.1 提取标准引用字段
M <- metaTagExtraction(M, Field = "SR")

# 7.2 提取被引参考文献作者信息（Biblioshiny官方默认设置）
M <- metaTagExtraction(M, Field = "CR_AU", sep = ";", aff.disamb = TRUE)

# 7.3 设置行名
row.names(M) <- M$SR

# 7.4 设置类属性
class(M) <- c("bibliometrixDB", "data.frame")

# --- 8. 保存结果 ---
log_message("保存处理结果...")
saveRDS(M, output_file)
log_message(sprintf("结果已保存至: %s", output_file))

# --- 9. 生成处理报告 ---
log_message("生成处理报告...")

# 9.1 记录处理统计信息
stats <- list(
  initial_records = nrow(readRDS(input_file)),
  final_records = nrow(M),
  removed_duplicates = nrow(readRDS(input_file)) - nrow(M),
  processing_time = difftime(Sys.time(), start_time, units = "mins")
)

# 9.2 保存统计信息
stats_file <- file.path(config$paths$reports, "deduplication_stats.csv")
write.csv(as.data.frame(stats), stats_file, row.names = FALSE)
log_message(sprintf("统计信息已保存至: %s", stats_file))

# --- 10. 清理环境 ---
close(log_con)
log_message("处理完成")

# 输出处理结果摘要
message("\n处理结果摘要:")
message(sprintf("初始记录数: %d", stats$initial_records))
message(sprintf("最终记录数: %d", stats$final_records))
message(sprintf("移除重复记录: %d", stats$removed_duplicates))
message(sprintf("处理用时: %.2f 分钟", as.numeric(stats$processing_time))) 
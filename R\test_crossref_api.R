# 测试Crossref API连接和功能
# 这个脚本用于验证Crossref API是否正常工作

# 加载必要的包
required_packages <- c("rcrossref", "httr", "jsonlite", "dplyr", "stringr")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

# 改进的字符串相似度计算
improved_string_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  
  # 转换为小写并移除标点符号
  str1_clean <- tolower(gsub("[[:punct:]]", " ", str1))
  str2_clean <- tolower(gsub("[[:punct:]]", " ", str2))
  
  # 移除多余空格
  str1_clean <- gsub("\\s+", " ", trimws(str1_clean))
  str2_clean <- gsub("\\s+", " ", trimws(str2_clean))
  
  # 方法1：编辑距离相似度
  distance <- adist(str1_clean, str2_clean)[1, 1]
  max_len <- max(nchar(str1_clean), nchar(str2_clean))
  edit_similarity <- if (max_len == 0) 1 else 1 - (distance / max_len)
  
  # 方法2：词汇重叠相似度
  words1 <- strsplit(str1_clean, "\\s+")[[1]]
  words2 <- strsplit(str2_clean, "\\s+")[[1]]
  
  # 移除停用词
  stopwords <- c("a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  words1 <- words1[!words1 %in% stopwords & nchar(words1) > 2]
  words2 <- words2[!words2 %in% stopwords & nchar(words2) > 2]
  
  if (length(words1) == 0 || length(words2) == 0) {
    word_similarity <- 0
  } else {
    common_words <- length(intersect(words1, words2))
    total_words <- length(union(words1, words2))
    word_similarity <- common_words / total_words
  }
  
  # 综合相似度：编辑距离权重0.4，词汇重叠权重0.6
  combined_similarity <- 0.4 * edit_similarity + 0.6 * word_similarity
  
  return(combined_similarity)
}

# 测试Crossref API连接
test_crossref_connection <- function() {
  log_message("测试Crossref API连接...")
  
  tryCatch({
    # 测试一个已知的DOI
    test_doi <- "10.1371/journal.pone.0000001"
    result <- rcrossref::cr_works(dois = test_doi)
    
    if (!is.null(result$data) && nrow(result$data) > 0) {
      log_message("Crossref API连接成功！")
      log_message(sprintf("测试DOI: %s", test_doi))
      log_message(sprintf("返回标题: %s", result$data$title[1]))
      return(TRUE)
    } else {
      log_message("Crossref API连接失败：无返回数据", "error")
      return(FALSE)
    }
  }, error = function(e) {
    log_message(sprintf("Crossref API连接错误: %s", e$message), "error")
    return(FALSE)
  })
}

# 改进的DOI查找函数
search_doi_by_title_improved <- function(title, authors = NULL, year = NULL) {
  tryCatch({
    # 清理标题
    clean_title <- gsub("[[:punct:]]", " ", title)
    clean_title <- gsub("\\s+", " ", clean_title)
    clean_title <- trimws(clean_title)
    
    # 改进的作者信息处理
    rcr_query_author <- NULL
    if (!is.null(authors) && !is.na(authors) && authors != "") {
      # 分割作者列表
      author_list <- strsplit(authors, ";")[[1]]
      if (length(author_list) > 0) {
        first_author <- trimws(author_list[1])

        # 尝试不同的作者格式解析
        if (grepl(",", first_author)) {
          # 格式：Last, First Middle
          author_parts <- strsplit(first_author, ",")[[1]]
          if (length(author_parts) >= 2) {
            last_name <- trimws(author_parts[1])
            first_name <- trimws(author_parts[2])
            # 提取名字的首字母
            first_initial <- substr(first_name, 1, 1)
            rcr_query_author <- paste(first_initial, last_name)
          }
        } else {
          # 格式：First Middle Last 或 First Last
          author_parts <- strsplit(first_author, "\\s+")[[1]]
          if (length(author_parts) >= 2) {
            # 假设最后一个是姓氏，第一个是名字
            first_name <- author_parts[1]
            last_name <- author_parts[length(author_parts)]
            # 如果名字长度大于1，取首字母
            if (nchar(first_name) > 1) {
              first_initial <- substr(first_name, 1, 1)
              rcr_query_author <- paste(first_initial, last_name)
            } else {
              rcr_query_author <- paste(first_name, last_name)
            }
          } else if (length(author_parts) == 1 && nchar(author_parts[1]) > 3) {
            rcr_query_author <- author_parts[1]
          }
        }
      }
    }
    
    # 年份过滤
    rcr_filter <- NULL
    if (!is.null(year) && !is.na(year)) {
      rcr_filter <- c(from_pub_date = as.character(year), until_pub_date = as.character(year))
    }
    
    log_message(sprintf("查询标题: '%s'", clean_title))
    if (!is.null(rcr_query_author)) {
      log_message(sprintf("查询作者: '%s'", rcr_query_author))
    }
    if (!is.null(year)) {
      log_message(sprintf("查询年份: %s", year))
    }
    
    # 尝试不同的查询策略
    crossref_result <- NULL
    
    # 策略1：标题+作者
    if (!is.null(rcr_query_author) && nchar(rcr_query_author) > 2) {
      log_message("尝试策略1：标题+作者查询")
      crossref_result <- rcrossref::cr_works(
        query.title = clean_title,
        query.author = rcr_query_author,
        filter = rcr_filter,
        limit = 5
      )
    }
    
    # 策略2：仅标题
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("尝试策略2：仅标题查询")
      crossref_result <- rcrossref::cr_works(
        query.title = clean_title,
        filter = rcr_filter,
        limit = 5
      )
    }
    
    # 策略3：关键词查询
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("尝试策略3：关键词查询")
      title_words <- strsplit(clean_title, "\\s+")[[1]]
      important_words <- title_words[1:min(5, length(title_words))]
      query_string <- paste(important_words, collapse = " ")
      
      crossref_result <- rcrossref::cr_works(
        query = query_string,
        filter = rcr_filter,
        limit = 5
      )
    }
    
    # 分析结果
    if (!is.null(crossref_result$data) && nrow(crossref_result$data) > 0) {
      log_message(sprintf("找到 %d 个候选结果", nrow(crossref_result$data)))
      
      best_match_idx <- NULL
      best_similarity <- 0
      
      for (i in 1:nrow(crossref_result$data)) {
        candidate_title <- crossref_result$data$title[i]
        if (is.null(candidate_title) || is.na(candidate_title) || candidate_title == "") {
          next
        }
        
        current_similarity <- improved_string_similarity(title, candidate_title)
        log_message(sprintf("候选 %d: '%s' (相似度: %.2f)", i, 
                           substr(candidate_title, 1, 60), current_similarity))
        
        if (current_similarity > best_similarity) {
          best_similarity <- current_similarity
          best_match_idx <- i
        }
      }
      
      # 使用较低的阈值
      similarity_threshold <- 0.5
      
      if (!is.null(best_match_idx) && best_similarity > similarity_threshold) {
        doi_found <- crossref_result$data$doi[best_match_idx]
        if (!is.na(doi_found) && doi_found != "") {
          log_message(sprintf("成功找到DOI: %s (相似度: %.2f)", doi_found, best_similarity))
          return(doi_found)
        }
      } else {
        log_message(sprintf("最佳相似度 %.2f 低于阈值 %.2f", best_similarity, similarity_threshold))
      }
    } else {
      log_message("未找到任何候选结果")
    }
    
    return(NA_character_)
  }, error = function(e) {
    log_message(sprintf("查询过程发生错误: %s", e$message), "error")
    return(NA_character_)
  })
}

# 主测试函数
main_test <- function() {
  log_message("开始Crossref API测试")
  
  # 1. 测试连接
  if (!test_crossref_connection()) {
    log_message("Crossref API连接失败，终止测试", "error")
    return(FALSE)
  }
  
  # 2. 测试DOI查找功能
  log_message("测试DOI查找功能...")
  
  # 测试用例 - 使用更真实的数据
  test_cases <- list(
    list(
      title = "The complete genome sequence of Escherichia coli K-12",
      authors = "Blattner, Frederick R; Plunkett, Guy; Bloch, Carol A",
      year = 1997
    ),
    list(
      title = "Basic local alignment search tool",
      authors = "Altschul, Stephen F; Gish, Warren; Miller, Webb",
      year = 1990
    ),
    list(
      title = "Gapped BLAST and PSI-BLAST a new generation of protein database search programs",
      authors = "Altschul, Stephen F; Madden, Thomas L; Schaffer, Alejandro A",
      year = 1997
    ),
    list(
      title = "CLUSTAL W improving the sensitivity of progressive multiple sequence alignment through sequence weighting position-specific gap penalties and weight matrix choice",
      authors = "Thompson, Julie D; Higgins, Desmond G; Gibson, Toby J",
      year = 1994
    )
  )
  
  success_count <- 0
  
  for (i in seq_along(test_cases)) {
    test_case <- test_cases[[i]]
    log_message(sprintf("\n=== 测试用例 %d ===", i))
    
    found_doi <- search_doi_by_title_improved(
      test_case$title,
      test_case$authors,
      test_case$year
    )
    
    if (!is.na(found_doi)) {
      success_count <- success_count + 1
      log_message(sprintf("测试用例 %d 成功", i))
    } else {
      log_message(sprintf("测试用例 %d 失败", i))
    }
    
    # 避免API限制
    Sys.sleep(1)
  }
  
  log_message(sprintf("\n测试完成: %d/%d 成功", success_count, length(test_cases)))
  
  return(success_count > 0)
}

# 运行测试
if (interactive()) {
  main_test()
} else {
  # 如果是脚本运行，也执行测试
  main_test()
}

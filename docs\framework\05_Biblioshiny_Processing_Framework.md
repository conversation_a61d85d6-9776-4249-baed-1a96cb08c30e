# Biblioshiny数据处理框架：官方流程与技术规范


## 1. 数据源支持与验证

### 1.1 支持的数据源及其特征
| 数据源 | 支持格式 | 标识字段 | 特殊处理 | 数据特征 |
|--------|----------|-----------|------------|------------|
| Web of Science (WoS/ISI) | plaintext, bibtex | UT | 自动转换为ISI格式 | 引文数据完整，机构信息标准化 |
| Scopus | csv, bibtex | UT, TI | 机构信息标准化 | 作者信息详细，机构层级清晰 |
| PubMed | api, plaintext | PMID | MeSH术语提取 | 医学文献，主题分类详细 |
| Dimensions | api, csv | id | 研究领域分类 | 跨学科数据，研究领域分类 |
| Cochrane | plaintext | ID | 系统评价特征提取 | 系统评价文献，方法学特征 |
| OpenAlex | csv, api | id_oa | 开放获取信息提取 | 开放获取数据，引用关系完整 |
| Lens | csv | lens_id | 专利信息整合 | 专利文献，技术领域分类 |

### 1.2 数据源验证机制
#### 1.2.1 数据源类型识别
- 验证方法：字符串匹配
- 验证规则：
  - 检查dbsource参数是否在allowed_db列表中
  - allowed_db = ["cochrane", "dimensions", "generic", "isi", "openalex", "openalex_api", "pubmed", "scopus", "wos", "lens"]
- 错误处理：
  - 提示支持的数据库列表
  - 记录错误日志去
- 具体实现：
  ```R
  # 数据源验证
  if (!(dbsource %in% allowed_db)) {
    stop("数据源类型错误。支持的数据源包括：", 
         paste(allowed_db, collapse = ", "))
  }
  ```

#### 1.2.2 格式兼容性检查
- 验证方法：文件扩展名和内容检查
- 验证规则：
  - 检查format参数是否在allowed_formats列表中
  - allowed_formats = ["api", "bibtex", "csv", "endnote", "excel", "plaintext", "pubmed"]
- 错误处理：
  - 提示支持的格式列表
  - 记录错误日志
- 具体实现：
  ```R
  # 格式验证
  if (!(format %in% allowed_formats)) {
    stop("文件格式错误。支持的格式包括：", 
         paste(allowed_formats, collapse = ", "))
  }
  
  # 文件扩展名检查
  file_ext <- tolower(tools::file_ext(file))
  if (format == "bibtex" && file_ext != "bib") {
    warning("文件扩展名与格式不匹配，请确保文件格式正确")
  }
  ```

#### 1.2.3 必要字段验证
- 必选字段列表：
  - TI (标题)
  - AU (作者)
  - PY (出版年份)
  - SO (来源)
- 验证方法：字段存在性检查
- 错误处理：
  - 提示缺失字段
  - 记录错误日志
- 具体实现：
  ```R
  # 必要字段验证
  required_fields <- c("TI", "AU", "PY", "SO")
  missing_fields <- required_fields[!required_fields %in% colnames(M)]
  
  if (length(missing_fields) > 0) {
    stop("缺少必要字段：", paste(missing_fields, collapse = ", "))
  }
  ```

#### 1.2.4 数据完整性确认
- 检查方法：记录完整性验证
- 检查规则：
  - 记录数量检查
  - 字段完整性检查
  - 数据格式检查
- 错误处理：记录错误日志
- 具体实现：
  ```R
  # 记录完整性检查
  check_data_integrity <- function(M) {
    # 记录数量检查
    if (nrow(M) == 0) {
      stop("数据为空")
    }
    
    # 字段完整性检查
    na_counts <- colSums(is.na(M))
    if (any(na_counts == nrow(M))) {
      warning("以下字段完全为空：", 
              paste(names(na_counts[na_counts == nrow(M)]), collapse = ", "))
    }
    
    # 数据格式检查
    if (!is.numeric(M$PY)) {
      warning("出版年份(PY)不是数值型")
    }
    
    if (!is.numeric(M$TC)) {
      warning("引用次数(TC)不是数值型")
    }
  }
  ```

### 1.3 支持的数据格式及其处理
| 格式 | 适用数据源 | 处理方式 | 特殊要求 | 输出格式 |
|------|------------|-----------|------------|------------|
| API | PubMed, Dimensions, OpenAlex | 直接API调用 | API密钥验证 | 标准数据框 |
| BibTeX | WoS, Scopus | bib2df转换 | UTF-8编码 | 标准数据框 |
| CSV | Scopus, Dimensions, OpenAlex, Lens | read.csv | 分隔符识别 | 标准数据框 |
| EndNote | 通用 | 转换为plaintext | 格式标准化 | 标准数据框 |
| Excel | 通用 | read_excel | sheet选择 | 标准数据框 |
| Plaintext | WoS, PubMed, Cochrane | readLines | 编码识别 | 标准数据框 |
| PubMed | PubMed | pubmed2df | XML解析 | 标准数据框 |

## 2. 核心字段处理规范

### 2.1 基础字段标准化
#### 2.1.1 出版年份(PY)
| 处理步骤 | 具体方法 | 参数设置 | 错误处理 |
|----------|-----------|------------|------------|
| 数值化处理 | as.numeric转换 | 无 | 非数值转换为NA |
| 缺失值处理 | 标准化为NA | 无 | 记录缺失原因 |
| 异常值检测 | 范围检查 | 1900-当前年份 | 超出范围设为NA |
| 数据验证 | 格式检查 | 4位数字年份 | 记录错误日志 |

#### 2.1.2 引用次数(TC)
| 处理步骤 | 具体方法 | 参数设置 | 错误处理 |
|----------|-----------|------------|------------|
| 数值化处理 | as.numeric转换 | 无 | 非数值转换为0 |
| 缺失值处理 | 标准化为0 | 无 | 记录缺失原因 |
| 异常值检测 | 统计分布分析 | 非负整数 | 负值设为0 |
| 数据验证 | 范围检查 | 最小值0 | 记录错误日志 |

#### 2.1.3 参考文献(CR)
| 处理步骤 | 具体方法 | 参数设置 | 错误处理 |
|----------|-----------|------------|------------|
| 格式标准化 | 标准格式转换 | 作者(年份)标题 | 格式错误修正 |
| 特殊字符清理 | 字符替换 | 标准化字符映射 | 记录替换日志 |
| 缺失值处理 | 标准化为"none" | 无 | 记录缺失原因 |
| 引用格式统一 | 格式检查 | 标准引用格式 | 格式错误修正 |

### 2.2 作者信息处理
#### 2.2.1 作者字段(AU)
| 处理步骤 | 具体方法 | 参数设置 | 错误处理 |
|----------|-----------|------------|------------|
| 分号分割 | 字符串分割 | sep = ";" | 格式错误修正 |
| 特殊字符清理 | 字符替换 | 标准化字符映射 | 记录替换日志 |
| 名称标准化 | 格式转换 | LastName, FirstName | 格式错误修正 |
| 空格规范化 | 空格处理 | 单空格 | 记录处理日志 |
| 作者顺序维护 | 顺序保持 | 原始顺序 | 顺序验证 |

#### 2.2.2 机构信息(C1, AU_UN)
| 处理步骤 | 具体方法 | 参数设置 | 错误处理 |
|----------|-----------|------------|------------|
| 机构信息提取 | 正则表达式匹配 | 机构名称模式 | 手动修正 |
| 标准化名称生成 | 名称映射 | 标准名称表 | 记录映射日志 |
| 缺失值处理 | 标准化为NA | 无 | 记录缺失原因 |
| 机构层级维护 | 层级处理 | 机构-部门-国家 | 层级验证 |

### 2.3 参考文献处理
| 处理步骤 | 具体方法 | 参数设置 | 错误处理 |
|----------|-----------|------------|------------|
| 格式规范化 | 格式转换 | 标准引用格式 | 格式错误修正 |
| 特殊字符清理 | 字符替换 | 标准化字符映射 | 记录替换日志 |
| 标准引用格式生成 | 格式转换 | 作者(年份)标题 | 格式错误修正 |
| 数据追踪机制 | 匹配处理 | 相似度阈值0.95 | 记录匹配日志 |
| 引用完整性验证 | 完整性检查 | 标准格式 | 完整性修复 |

## 3. 数据清理体系

### 3.1 文本标准化
| 处理步骤 | 具体方法 | 参数设置 | 错误处理 |
|----------|-----------|------------|------------|
| 大小写规范化 | 大小写转换 | 首字母大写 | 格式错误修正 |
| 分隔符统一 | 分隔符替换 | 字段";"子字段"," | 格式错误修正 |
| 重复字符清理 | 正则表达式 | 重复字符模式 | 记录替换日志 |
| 格式一致性维护 | 格式检查 | 标准格式 | 格式错误修正 |
| 特殊字符处理 | 字符映射 | 标准化字符表 | 记录替换日志 |

### 3.2 去重策略

本框架采用了两种主要的去重策略，分别对应不同的去重目标和严格程度：

1.  **官方去重策略 (由 `R/02_deduplication_biblioshiny.R` 实现)**
    *   **目标：** 模拟 Biblioshiny 官方在 `Merge Collections` 模块中执行的去重逻辑。
    *   **核心方法：** 主要调用 `bibliometrix::mergeDbSources()` 函数，其内部会进一步调用 `bibliometrix::duplicatedMatching()` 进行去重。该策略旨在提供一个官方推荐的、平衡去重效果和数据完整性的基准。
    *   **默认参数：**
        *   `Field = "TI"` (基于标题字段进行匹配)
        *   `exact = FALSE` (进行模糊匹配)
        *   `tol = 0.95` (相似度阈值为 95%)
    *   **特点：** 相对温和，主要针对标题高度相似的记录，适用于快速获取去重后的基线数据集。

2.  **极限去重策略 (由 `R/02_deduplication_extreme.R` 实现)**
    *   **目标：** 在严格限定于 `bibliometrix::duplicatedMatching()` 官方支持字段的前提下，探索和展示 `bibliometrix` 包在去重方面的极限能力。旨在最大化去重效果，减少数据冗余。
    *   **核心方法：** 同样调用 `bibliometrix::duplicatedMatching()` 函数，但采用更严格或组合的匹配字段和阈值。
    *   **当前策略（考虑到性能和C栈限制）：**
        *   **标题模糊匹配：** (`Field = "TI", exact = FALSE, tol = 0.98`)
            *   此步骤使用 `duplicatedMatching()`。
            *   **注意：** 考虑到 `duplicatedMatching()` 函数在处理大规模数据集时可能遇到的 C 栈溢出问题，目前优先使用 `tol = 0.98`。如果 R 环境或 `bibliometrix` 版本能解决此问题，可考虑放宽阈值（如 `0.95`, `0.90`）或引入摘要（`AB`）、关键词（`DE`）、期刊（`SO`）等字段的模糊匹配。
        *   **唯一标识符精确匹配：** (`Field = "UT", exact = TRUE, tol = 1.0`)
            *   在模糊匹配前，先进行基于 `UT` (Web of Science Unique Article Identifier) 字段的精确去重，可以显著减少数据集大小，提高后续模糊匹配的效率并降低 C 栈溢出风险。
    *   **特点：** 更为严格，旨在移除更多潜在重复记录，适用于需要高度精炼数据集的分析场景。
    *   **性能优化建议：**
        *   **增加 R 环境的 C 栈内存限制：** 对于大规模数据集和深度模糊匹配，可通过调整 R 启动参数或环境变量来增加 C 栈内存限制。
        *   **分批处理数据：** 将大型数据集分割成小块分别处理，然后合并。
        *   **逐步放宽阈值：** 从严格的 `tol` 值开始，逐步调整以平衡去重效果与计算成本。
        *   **先精确后模糊：** 优先进行精确匹配去重，再进行模糊匹配。

#### 3.2.1 数据源特定去重
| 数据源 | 匹配字段 | 匹配方法 | 相似度阈值 | 处理方式 |
|--------|-----------|------------|--------------|------------|
| WoS/ISI | UT | 精确匹配 | 1.0 | 保留第一条记录 |
| Scopus | UT或TI | 模糊匹配 | 0.95 | 保留高相似度记录 |
| PubMed | PMID | 精确匹配 | 1.0 | 保留第一条记录 |
| OpenAlex | id_oa | 精确匹配 | 1.0 | 保留第一条记录 |
| 其他 | TI | 模糊匹配 | 0.95 | 保留高相似度记录 |

#### 3.2.2 去重参数设置
| 参数类型 | 具体值 | 匹配方法 | 处理方式 |
|----------|---------|------------|------------|
| 标题相似度 | 0.95 | fuzzy matching | 保留高相似度记录 |
| 作者相似度 | 0.95 | fuzzy matching | 保留高相似度记录 |
| 摘要相似度 | 0.90 | fuzzy matching | 保留高相似度记录 |
| 匹配算法 | Levenshtein距离 | fuzzy matching | 可调整参数 |

### 3.3 缺失值处理
| 字段类型 | 缺失值处理 | 异常值处理 | 处理记录 |
|----------|------------|------------|------------|
| 数值字段 | NA | NA | 日志记录 |
| 字符字段 | "none" | "none" | 日志记录 |
| 布尔字段 | FALSE | FALSE | 日志记录 |

## 4. 质量控制体系

### 4.1 格式规范
| 控制项目 | 检查方法 | 标准规则 | 处理方式 |
|----------|-----------|------------|------------|
| 列结构维护 | 结构检查 | 标准结构 | 结构修正 |
| 列名一致性 | 名称检查 | 标准名称 | 名称修正 |
| 数据格式统一 | 格式检查 | 标准格式 | 格式修正 |
| 完整性保证 | 完整性检查 | 必填字段 | 缺失处理 |
| 编码规范 | 编码检查 | UTF-8 | 编码转换 |

### 4.2 数据验证
| 验证项目 | 验证方法 | 验证规则 | 处理方式 |
|----------|-----------|------------|------------|
| 必要字段检查 | 字段存在性检查 | 必选字段列表 | 缺失处理 |
| 格式正确性验证 | 格式检查 | 标准格式 | 格式修正 |
| 数据完整性确认 | 完整性检查 | 完整性规则 | 完整性修复 |
| 异常值检测 | 统计检测 | 异常值规则 | 异常值处理 |
| 一致性检查 | 一致性检查 | 一致性规则 | 一致性修复 |

## 5. 元数据提取与标准化

### 5.1 元数据提取
| 提取项目 | 提取方法 | 数据来源 | 处理方式 |
|----------|-----------|------------|------------|
| 作者国家 | metaTagExtraction | C1字段 | 结果验证 |
| 机构信息 | metaTagExtraction | AU_UN字段 | 结果验证 |
| 关键词 | metaTagExtraction | DE和ID字段 | 结果验证 |
| 学科分类 | metaTagExtraction | WC字段 | 结果验证 |

### 5.2 数据结构规范
| 字段类型 | 字段列表 | 数据类型 | 格式要求 |
|----------|-----------|------------|------------|
| 字符型 | TI, AU, AB, SO | character | UTF-8编码 |
| 数值型 | PY, TC | numeric | 标准数值 |
| 列表型 | CR, C1, AU_UN | list | 标准格式 |

## 6. 应用价值与兼容性

### 6.1 分析功能支持
| 分析类型 | 分析方法 | 使用工具 | 输出形式 |
|----------|-----------|------------|------------|
| 引文分析 | 引文网络分析 | bibliometrix | 网络图 |
| 共引分析 | 共引网络分析 | bibliometrix | 网络图 |
| 作者分析 | 作者网络分析 | bibliometrix | 网络图 |
| 机构分析 | 机构网络分析 | bibliometrix | 网络图 |
| 主题分析 | 主题网络分析 | bibliometrix | 网络图 |
| 时间序列分析 | 时间序列分析 | bibliometrix | 趋势图 |
| 合作网络分析 | 合作网络分析 | bibliometrix | 网络图 |

### 6.2 数据兼容性
| 兼容项目 | 处理方法 | 标准规则 | 处理方式 |
|----------|-----------|------------|------------|
| 多源数据兼容 | 格式转换 | 标准格式 | 格式统一 |
| 格式统一性 | 格式标准化 | 标准格式 | 格式修正 |
| 跨库分析支持 | 数据整合 | 整合规则 | 数据合并 |
| 数据共享便利 | 数据导出 | 标准格式 | 格式转换 |
| 接口标准化 | 接口定义 | 标准接口 | 接口实现 |

## 7. 技术实现细节

### 7.1 数据处理流程
```R
# 数据导入与验证
M <- convert2df(file, dbsource = "wos", format = "plaintext")

# 数据清理
M <- cleanData(M)

# 元数据提取
M <- metaTagExtraction(M, Field = "AU_CO", sep = ";")

# 去重处理
M <- duplicatedMatching(M, Field = "TI", exact = FALSE, tol = 0.95)
```

### 7.2 关键参数设置
| 参数类型 | 具体值 | 使用场景 | 调整建议 |
|----------|---------|------------|------------|
| 标题相似度 | 0.95 | 去重匹配 | 可适当降低 |
| 作者相似度 | 0.95 | 去重匹配 | 可适当降低 |
| 摘要相似度 | 0.90 | 去重匹配 | 可适当降低 |
| 字段分隔符 | ";" | 字段分隔 | 不建议修改 |
| 子字段分隔符 | "," | 子字段分隔 | 不建议修改 |

### 7.3 数据源转换规则
| 数据源 | 转换方法 | 目标格式 | 处理方式 |
|--------|-----------|------------|------------|
| WoS | 格式转换 | ISI格式 | 格式统一 |
| EndNote | 格式转换 | plaintext | 格式统一 |
| Lens | 格式转换 | CSV | 格式统一 |

## 8. 最佳实践建议

### 8.1 数据准备
| 准备步骤 | 具体方法 | 标准规则 | 处理方式 |
|----------|-----------|------------|------------|
| 数据源选择 | 数据源评估 | 数据质量 | 数据选择 |
| 格式验证 | 格式检查 | 标准格式 | 格式修正 |
| 预处理要求 | 预处理 | 预处理规则 | 数据准备 |
| 质量检查 | 质量检查 | 质量标准 | 质量保证 |

### 8.2 处理流程
| 处理步骤 | 具体方法 | 标准规则 | 处理方式 |
|----------|-----------|------------|------------|
| 标准化步骤 | 标准化 | 标准流程 | 流程执行 |
| 质量控制点 | 质量控制 | 质量规则 | 质量保证 |
| 验证机制 | 数据验证 | 验证规则 | 验证执行 |
| 结果确认 | 结果检查 | 结果标准 | 结果确认 |

### 8.3 注意事项
| 注意项目 | 检查方法 | 标准规则 | 处理方式 |
|----------|-----------|------------|------------|
| 数据完整性 | 完整性检查 | 完整性规则 | 完整性保证 |
| 格式一致性 | 格式检查 | 格式规则 | 格式统一 |
| 处理效率 | 效率优化 | 效率规则 | 效率提升 |
| 结果可靠性 | 可靠性检查 | 可靠性规则 | 可靠性保证 |

## 9. 总结

Biblioshiny的数据处理框架提供了一个完整的文献计量数据标准化体系。通过严格的数据源验证、核心字段处理、数据清理和质量控制，系统确保了数据质量和分析可靠性。这种标准化的处理机制不仅提高了分析效率，也为文献计量研究提供了可靠的数据基础。

## 10. 参考文献

1. Aria, M., & Cuccurullo, C. (2017). bibliometrix: An R-tool for comprehensive science mapping analysis. Journal of Informetrics, 11(4), 959-975.
2. Aria, M., & Cuccurullo, C. (2017). bibliometrix: An R-tool for comprehensive science mapping analysis. Journal of Informetrics, 11(4), 959-975.
3. Aria, M., Misuraca, M., & Spano, M. (2020). Mapping the evolution of social research and data science on 30 years of Social Indicators Research. Social Indicators Research, 149(3), 803-831. 

输出情况：
to start with the Biblioshiny app, please digit:
biblioshiny()

载入需要的程序包：shiny

Listening on http://127.0.0.1:3741
Browsing http://127.0.0.1:3741
debugging in: convert2df(inFile$datapath, dbsource = input$dbsource, format = formatDB(inFile$datapath))
debug: {
    allowed_formats <- c("api", "bibtex", "csv", "endnote", "excel", 
        "plaintext", "pubmed")
    allowed_db <- c("cochrane", "dimensions", "generic", "isi", 
        "openalex", "openalex_api", "pubmed", "scopus", "wos", 
        "lens")
    cat("\nConverting your", dbsource, "collection into a bibliographic dataframe\n\n")
    if (length(setdiff(dbsource, allowed_db)) > 0) {
        cat("\n 'dbsource' argument is not properly specified")
        cat("\n 'dbsource' argument has to be a character string matching one among:",
            allowed_db, "\n")
    }
    if (length(setdiff(format, allowed_formats)) > 0) {
        cat("\n 'format' argument is not properly specified")
        cat("\n 'format' argument has to be a character string matching one among:",
            allowed_formats, "\n")
    }
    if (dbsource == "wos")
        dbsource <- "isi"
    if (format == "endnote")
        format <- "plaintext"
    if (format == "lens")
        format <- "csv"
    switch(dbsource, isi = {
        switch(format, bibtex = {
            D <- importFiles(file)
            M <- bib2df(D, dbsource = "isi")
        }, plaintext = {
            D <- importFiles(file)
            M <- isi2df(D)
        })
    }, scopus = {
        switch(format, bibtex = {
            D <- importFiles(file)
            M <- bib2df(D, dbsource = "scopus")
        }, csv = {
            M <- csvScopus2df(file)
        })
    }, generic = {
        D <- importFiles(file)
        M <- bib2df(D, dbsource = "generic")
    }, lens = {
        M <- csvLens2df(file)
    }, pubmed = {
        switch(format, api = {
            M <- pmApi2df(file)
            M$DB <- "PUBMED"
        }, {
            D <- importFiles(file)
            M <- pubmed2df(D)
        })
    }, cochrane = {
        D <- importFiles(file)
        M <- cochrane2df(D)
    }, dimensions = {
        switch(format, api = {
            M <- dsApi2df(file)
            M$DB <- "DIMENSIONS"
        }, {
            M <- dimensions2df(file, format = format)
            M$DB <- "DIMENSIONS"
        })
    }, openalex = {
        M <- csvOA2df(file)
    }, openalex_api = {
        M <- apiOA2df(file)
    })
    if ("PY" %in% names(M)) {
        M$PY <- as.numeric(M$PY)
    }
    else {
        M$PY <- NA
    }
    if ("TC" %in% names(M)) {
        M$TC <- as.numeric(M$TC)
        M$TC[is.na(M$TC)] <- 0
    }
    else {
        M$TC <- 0
    }
    if (!("CR" %in% names(M))) {
        M$CR <- "none"
    }
    else {
        M$CR <- trim.leading(trimES(gsub("\\[,||\\[||\\]|| \\.\\. || \\. ",
            "", M$CR)))
    }
    if (dbsource %in% c("cochrane", "openalex_api")) {
        M$AU <- gsub(intToUtf8(8217), intToUtf8(39), M$AU)
    }
    cat("Done!\n\n")
    if (!(dbsource %in% c("pubmed", "lens", "openalex_api"))) {
        if ("C1" %in% names(M)) {
            cat("\nGenerating affiliation field tag AU_UN from C1:  ")
            if (!"AU_UN" %in% names(M)) 
                M <- metaTagExtraction(M, Field = "AU_UN")
            cat("Done!\n\n")
        }
        else {
            M$C1 <- NA
            M$AU_UN <- NA
        }
        M$AU <- unlist(lapply(strsplit(M$AU, ";"), function(x) {
            x <- trimws(trimES(gsub("[^[:alnum:][-]']", " ",
                x)))
            x <- paste(x, collapse = ";")
        }))
    }
    if ((dbsource == "pubmed") & (format == "pubmed")) {
        if ("C1" %in% names(M)) {
            cat("\nGenerating affiliation field tag AU_UN from C1:  ")
            M <- metaTagExtraction(M, Field = "AU_UN")
            cat("Done!\n\n")
        }
        else {
            M$C1 <- NA
            M$AU_UN <- NA
        }
    }
    M <- M %>% mutate_if(is.character, ~gsub(";;", ";", .x))
    if (isTRUE(remove.duplicates)) {
        switch(dbsource, isi = {
            id_field <- "UT"
        }, scopus = {
            if (format == "csv") {
                id_field <- "UT"
            } else {
                id_field <- "TI"
            }
        }, openalex = {
            id_field <- "id_oa"
        }, openalex_api = {
            id_field <- "id_oa"
        }, dimneisons = {
            id_field <- "UT"
        }, pubmed = {
            id_field <- "PMID"
        }, lens = {
            id_field <- "UT"
        }, {
            id_field <- "TI"
        })
        d <- duplicated(M[id_field])
        if (sum(d) > 0)
            cat("\nRemoved ", sum(d), "duplicated documents\n")
        M <- M[!d, ]
    }
    suppressWarnings(M <- metaTagExtraction(M, Field = "SR"))
    row.names(M) <- M$SR
    class(M) <- c("bibliometrixDB", "data.frame")
    return(M)
}
Browse[1]> n
debug: allowed_formats <- c("api", "bibtex", "csv", "endnote", "excel", 
    "plaintext", "pubmed")
Browse[1]> n
debug: allowed_db <- c("cochrane", "dimensions", "generic", "isi", "openalex", 
    "openalex_api", "pubmed", "scopus", "wos", "lens")
Browse[1]> n
debug: cat("\nConverting your", dbsource, "collection into a bibliographic dataframe\n\n")
Browse[1]> n

Converting your isi collection into a bibliographic dataframe

debug: if (length(setdiff(dbsource, allowed_db)) > 0) {
    cat("\n 'dbsource' argument is not properly specified")
    cat("\n 'dbsource' argument has to be a character string matching one among:",
        allowed_db, "\n")
}
Browse[1]> n
debug: if (length(setdiff(format, allowed_formats)) > 0) {
    cat("\n 'format' argument is not properly specified")
    cat("\n 'format' argument has to be a character string matching one among:",
        allowed_formats, "\n")
}
Browse[1]> n
debug: if (dbsource == "wos") dbsource <- "isi"
Browse[1]> n
debug: if (format == "endnote") format <- "plaintext"
Browse[1]> n
debug: if (format == "lens") format <- "csv"
Browse[1]> n
debug: switch(dbsource, isi = {
    switch(format, bibtex = {
        D <- importFiles(file)
        M <- bib2df(D, dbsource = "isi")
    }, plaintext = {
        D <- importFiles(file)
        M <- isi2df(D)
    })
}, scopus = {
    switch(format, bibtex = {
        D <- importFiles(file)
        M <- bib2df(D, dbsource = "scopus")
    }, csv = {
        M <- csvScopus2df(file)
    })
}, generic = {
    D <- importFiles(file)
    M <- bib2df(D, dbsource = "generic")
}, lens = {
    M <- csvLens2df(file)
}, pubmed = {
    switch(format, api = {
        M <- pmApi2df(file)
        M$DB <- "PUBMED"
    }, {
        D <- importFiles(file)
        M <- pubmed2df(D)
    })
}, cochrane = {
    D <- importFiles(file)
    M <- cochrane2df(D)
}, dimensions = {
    switch(format, api = {
        M <- dsApi2df(file)
        M$DB <- "DIMENSIONS"
    }, {
        M <- dimensions2df(file, format = format)
        M$DB <- "DIMENSIONS"
    })
}, openalex = {
    M <- csvOA2df(file)
}, openalex_api = {
    M <- apiOA2df(file)
})
Browse[1]> n
debug: switch(format, bibtex = {
    D <- importFiles(file)
    M <- bib2df(D, dbsource = "isi")
}, plaintext = {
    D <- importFiles(file)
    M <- isi2df(D)
})
Browse[1]> n
debug: D <- importFiles(file)
Browse[1]> n
debug: M <- isi2df(D)
Browse[1]> n
debug: if ("PY" %in% names(M)) {
    M$PY <- as.numeric(M$PY)
} else {
    M$PY <- NA
}
Browse[1]> n
debug: M$PY <- as.numeric(M$PY)
Browse[1]> n
debug: if ("TC" %in% names(M)) {
    M$TC <- as.numeric(M$TC)
    M$TC[is.na(M$TC)] <- 0
} else {
    M$TC <- 0
}
Browse[1]> n
debug: M$TC <- as.numeric(M$TC)
Browse[1]> n
debug: M$TC[is.na(M$TC)] <- 0
Browse[1]> n
debug: if (!("CR" %in% names(M))) {
    M$CR <- "none"
} else {
    M$CR <- trim.leading(trimES(gsub("\\[,||\\[||\\]|| \\.\\. || \\. ",
        "", M$CR)))
}
Browse[1]> n
debug: M$CR <- trim.leading(trimES(gsub("\\[,||\\[||\\]|| \\.\\. || \\. ", 
    "", M$CR)))
Browse[1]> n
debug: if (dbsource %in% c("cochrane", "openalex_api")) {
    M$AU <- gsub(intToUtf8(8217), intToUtf8(39), M$AU)
}
Browse[1]> n
debug: cat("Done!\n\n")
Browse[1]> n
Done!

debug: if (!(dbsource %in% c("pubmed", "lens", "openalex_api"))) {
    if ("C1" %in% names(M)) {
        cat("\nGenerating affiliation field tag AU_UN from C1:  ")
        if (!"AU_UN" %in% names(M))
            M <- metaTagExtraction(M, Field = "AU_UN")
        cat("Done!\n\n")
    }
    else {
        M$C1 <- NA
        M$AU_UN <- NA
    }
    M$AU <- unlist(lapply(strsplit(M$AU, ";"), function(x) {
        x <- trimws(trimES(gsub("[^[:alnum:][-]']", " ", x)))
        x <- paste(x, collapse = ";")
    }))
}
Browse[1]> n
debug: if ("C1" %in% names(M)) {
    cat("\nGenerating affiliation field tag AU_UN from C1:  ")
    if (!"AU_UN" %in% names(M))
        M <- metaTagExtraction(M, Field = "AU_UN")
    cat("Done!\n\n")
} else {
    M$C1 <- NA
    M$AU_UN <- NA
}
Browse[1]> n
debug: cat("\nGenerating affiliation field tag AU_UN from C1:  ")
Browse[1]> n

Generating affiliation field tag AU_UN from C1:  debug: if (!"AU_UN" %in% names(M)) M <- metaTagExtraction(M, Field = "AU_UN")
Browse[1]> n
debug: M <- metaTagExtraction(M, Field = "AU_UN")
Browse[1]> n
debug: cat("Done!\n\n")
Browse[1]> n
Done!

debug: M$AU <- unlist(lapply(strsplit(M$AU, ";"), function(x) {
    x <- trimws(trimES(gsub("[^[:alnum:][-]']", " ", x)))
    x <- paste(x, collapse = ";")
}))
Browse[1]> n
debug: if ((dbsource == "pubmed") & (format == "pubmed")) {
    if ("C1" %in% names(M)) {
        cat("\nGenerating affiliation field tag AU_UN from C1:  ")
        M <- metaTagExtraction(M, Field = "AU_UN")
        cat("Done!\n\n")
    }
    else {
        M$C1 <- NA
        M$AU_UN <- NA
    }
}
Browse[1]> n
debug: M <- M %>% mutate_if(is.character, ~gsub(";;", ";", .x))
Browse[1]> n
debug: if (isTRUE(remove.duplicates)) {
    switch(dbsource, isi = {
        id_field <- "UT"
    }, scopus = {
        if (format == "csv") {
            id_field <- "UT"
        } else {
            id_field <- "TI"
        }
    }, openalex = {
        id_field <- "id_oa"
    }, openalex_api = {
        id_field <- "id_oa"
    }, dimneisons = {
        id_field <- "UT"
    }, pubmed = {
        id_field <- "PMID"
    }, lens = {
        id_field <- "UT"
    }, {
        id_field <- "TI"
    })
    d <- duplicated(M[id_field])
    if (sum(d) > 0)
        cat("\nRemoved ", sum(d), "duplicated documents\n")
    M <- M[!d, ]
}
Browse[1]> n
debug: switch(dbsource, isi = {
    id_field <- "UT"
}, scopus = {
    if (format == "csv") {
        id_field <- "UT"
    } else {
        id_field <- "TI"
    }
}, openalex = {
    id_field <- "id_oa"
}, openalex_api = {
    id_field <- "id_oa"
}, dimneisons = {
    id_field <- "UT"
}, pubmed = {
    id_field <- "PMID"
}, lens = {
    id_field <- "UT"
}, {
    id_field <- "TI"
})
Browse[1]> n
debug: id_field <- "UT"
Browse[1]> n
debug: d <- duplicated(M[id_field])
Browse[1]> n
debug: if (sum(d) > 0) cat("\nRemoved ", sum(d), "duplicated documents\n")
Browse[1]> n
debug: M <- M[!d, ]
Browse[1]> n
debug: suppressWarnings(M <- metaTagExtraction(M, Field = "SR"))
Browse[1]> n
debug: row.names(M) <- M$SR
Browse[1]> n
debug: class(M) <- c("bibliometrixDB", "data.frame")
Browse[1]> n
debug: return(M)
Browse[1]> n
exiting from: convert2df(inFile$datapath, dbsource = input$dbsource, format = formatDB(inFile$datapath))
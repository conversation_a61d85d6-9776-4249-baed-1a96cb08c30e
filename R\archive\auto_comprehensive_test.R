# 自动全面测试系统
# 全局角度评估DOI补全系统的实际效果

cat("=== 自动全面测试系统 ===\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 从最终系统复制核心函数
source_if_exists <- function(file) {
  if (file.exists(file)) {
    source(file)
    return(TRUE)
  }
  return(FALSE)
}

# 核心函数定义
final_normalize_text <- function(text) {
  if (is.na(text) || text == "") return("")
  text <- tolower(text)
  text <- gsub("[[:punct:]]", " ", text)
  text <- gsub("\\s+", " ", text)
  text <- trimws(text)
  
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  words <- strsplit(text, "\\s+")[[1]]
  words <- words[!words %in% stop_words & nchar(words) > 2]
  
  return(paste(words, collapse = " "))
}

final_similarity <- function(text1, text2) {
  if (is.na(text1) || is.na(text2) || text1 == "" || text2 == "") return(0)
  
  norm1 <- final_normalize_text(text1)
  norm2 <- final_normalize_text(text2)
  
  if (norm1 == "" || norm2 == "") return(0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  return(similarity)
}

final_match_journals <- function(journal1, journal2) {
  if (is.na(journal1) || is.na(journal2) || journal1 == "" || journal2 == "") return(0.5)
  
  normalize_journal <- function(journal) {
    journal <- tolower(journal)
    journal <- gsub("^the\\s+", "", journal)
    journal <- gsub("\\s*\\(.*\\)$", "", journal)
    journal <- gsub("[[:punct:]]", " ", journal)
    journal <- gsub("\\s+", " ", journal)
    return(trimws(journal))
  }
  
  norm1 <- normalize_journal(journal1)
  norm2 <- normalize_journal(journal2)
  
  if (norm1 == norm2) return(1.0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  
  words1 <- strsplit(norm1, "\\s+")[[1]]
  words2 <- strsplit(norm2, "\\s+")[[1]]
  
  words1 <- words1[nchar(words1) > 3]
  words2 <- words2[nchar(words2) > 3]
  
  if (length(words1) > 0 && length(words2) > 0) {
    common_words <- sum(words1 %in% words2)
    keyword_similarity <- common_words / max(length(words1), length(words2))
    similarity <- max(similarity, keyword_similarity)
  }
  
  return(similarity)
}

final_match_year <- function(year1, year2) {
  if (is.na(year1) || is.na(year2)) return(0.5)
  
  year1 <- as.numeric(year1)
  year2 <- as.numeric(year2)
  
  if (year1 == year2) return(1.0)
  if (abs(year1 - year2) <= 1) return(0.8)
  return(0.0)
}

# 增强的DOI查询函数
enhanced_doi_search <- function(title, authors, year, journal) {
  tryCatch({
    clean_title <- final_normalize_text(title)
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    query_string <- paste(keywords, collapse = " ")
    
    # 扩大搜索范围
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=20", 
                   URLencode(query_string), as.numeric(year)-3, as.numeric(year)+3)
    
    response <- GET(url, user_agent("EnhancedAcademicDOI/1.0"), timeout(30))
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$message$items) || length(content$message$items) == 0) return(NULL)
    
    items <- content$message$items
    best_match <- NULL
    best_score <- 0
    
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      candidate_title <- if (!is.null(item$title) && length(item$title) > 0) item$title[[1]] else ""
      candidate_journal <- ""
      candidate_year <- ""
      
      if (!is.null(item$`container-title`) && length(item$`container-title`) > 0) {
        candidate_journal <- item$`container-title`[[1]]
      }
      
      if (!is.null(item$`published-print`$`date-parts`)) {
        candidate_year <- item$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(item$`published-online`$`date-parts`)) {
        candidate_year <- item$`published-online`$`date-parts`[[1]][[1]]
      }
      
      title_sim <- final_similarity(title, candidate_title)
      journal_sim <- final_match_journals(journal, candidate_journal)
      year_sim <- final_match_year(year, candidate_year)
      
      # 调整评分权重
      enhanced_score <- (title_sim * 0.7) + (journal_sim * 0.2) + (year_sim * 0.1)
      
      # 降低阈值以获得更多匹配
      if (title_sim >= 0.6 &&             
          enhanced_score >= 0.5 &&        
          enhanced_score > best_score) {
        
        best_score <- enhanced_score
        best_match <- list(
          doi = item$DOI,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          enhanced_score = enhanced_score,
          crossref_score = item$score
        )
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    return(NULL)
  })
}

# 自动全面测试函数
auto_comprehensive_test <- function() {
  cat("开始自动全面测试...\n")
  
  input_file <- "data_repository/04_enhancement_reports/missing_doi_records.csv"
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  
  # 测试更多记录以获得更全面的评估
  test_count <- min(50, nrow(data))
  cat(sprintf("自动测试前%d条记录\n", test_count))
  
  results <- data.frame(
    序号 = 1:test_count,
    UT = data$UT[1:test_count],
    原始标题 = data$TI[1:test_count],
    原始年份 = data$PY[1:test_count],
    原始期刊 = data$SO[1:test_count],
    补全DOI = NA,
    匹配标题 = NA,
    匹配期刊 = NA,
    匹配年份 = NA,
    标题相似度 = NA,
    期刊匹配度 = NA,
    年份匹配度 = NA,
    增强评分 = NA,
    自动评级 = NA,
    补全状态 = "待处理",
    stringsAsFactors = FALSE
  )
  
  success_count <- 0
  high_quality_count <- 0
  
  for (i in 1:test_count) {
    if (i %% 10 == 0 || i == 1) {
      cat(sprintf("自动处理进度: %d/%d (%.1f%%)\n", i, test_count, 100*i/test_count))
    }
    
    match_result <- enhanced_doi_search(
      title = data$TI[i],
      authors = data$AU[i], 
      year = data$PY[i],
      journal = data$SO[i]
    )
    
    if (!is.null(match_result)) {
      results$补全DOI[i] <- match_result$doi
      results$匹配标题[i] <- match_result$title
      results$匹配期刊[i] <- match_result$journal
      results$匹配年份[i] <- match_result$year
      results$标题相似度[i] <- round(match_result$title_similarity, 3)
      results$期刊匹配度[i] <- round(match_result$journal_similarity, 3)
      results$年份匹配度[i] <- round(match_result$year_similarity, 3)
      results$增强评分[i] <- round(match_result$enhanced_score, 3)
      results$补全状态[i] <- "成功"
      
      # 自动评级
      if (match_result$title_similarity >= 0.9 && match_result$enhanced_score >= 0.8) {
        results$自动评级[i] <- "A级-优秀"
        high_quality_count <- high_quality_count + 1
      } else if (match_result$title_similarity >= 0.8 && match_result$enhanced_score >= 0.7) {
        results$自动评级[i] <- "B级-良好"
      } else if (match_result$title_similarity >= 0.7 && match_result$enhanced_score >= 0.6) {
        results$自动评级[i] <- "C级-可接受"
      } else {
        results$自动评级[i] <- "D级-需验证"
      }
      
      success_count <- success_count + 1
    } else {
      results$补全状态[i] <- "未找到匹配"
      results$自动评级[i] <- "未补全"
    }
    
    Sys.sleep(1.2)  # 稍微加快处理速度
  }
  
  # 自动分析结果
  success_rate <- 100 * success_count / test_count
  high_quality_rate <- 100 * high_quality_count / test_count
  
  cat(sprintf("\n=== 自动全面测试结果 ===\n"))
  cat(sprintf("测试记录数: %d\n", test_count))
  cat(sprintf("成功补全: %d (%.1f%%)\n", success_count, success_rate))
  cat(sprintf("A级优秀: %d (%.1f%%)\n", sum(results$自动评级 == "A级-优秀", na.rm = TRUE), 
              100 * sum(results$自动评级 == "A级-优秀", na.rm = TRUE) / test_count))
  cat(sprintf("B级良好: %d (%.1f%%)\n", sum(results$自动评级 == "B级-良好", na.rm = TRUE),
              100 * sum(results$自动评级 == "B级-良好", na.rm = TRUE) / test_count))
  cat(sprintf("C级可接受: %d (%.1f%%)\n", sum(results$自动评级 == "C级-可接受", na.rm = TRUE),
              100 * sum(results$自动评级 == "C级-可接受", na.rm = TRUE) / test_count))
  cat(sprintf("D级需验证: %d (%.1f%%)\n", sum(results$自动评级 == "D级-需验证", na.rm = TRUE),
              100 * sum(results$自动评级 == "D级-需验证", na.rm = TRUE) / test_count))
  
  # 保存测试结果
  output_file <- "data_repository/04_enhancement_reports/auto_comprehensive_test.csv"
  write.csv(results, output_file, row.names = FALSE)
  cat(sprintf("\n自动测试结果已保存: %s\n", output_file))
  
  # 自动决策：是否需要进一步优化
  if (success_rate < 40) {
    cat("\n🔧 自动决策: 成功率偏低，需要进一步优化算法\n")
    return(list(results = results, need_optimization = TRUE, success_rate = success_rate))
  } else if (success_rate >= 40 && high_quality_rate >= 15) {
    cat("\n✅ 自动决策: 系统表现良好，可以进行完整数据集处理\n")
    return(list(results = results, need_optimization = FALSE, success_rate = success_rate))
  } else {
    cat("\n⚠️ 自动决策: 成功率可接受但质量需要提升\n")
    return(list(results = results, need_optimization = TRUE, success_rate = success_rate))
  }
}

# 执行自动全面测试
test_result <- auto_comprehensive_test()

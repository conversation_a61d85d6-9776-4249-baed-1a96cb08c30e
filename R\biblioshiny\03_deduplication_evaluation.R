# R/03_deduplication_evaluation.R
# 评估 Biblioshiny 官方去重与 Bibliometrix 极限去重的结果差异

# --- 0. 配置管理 ---
# 加载必要的包
required_packages <- c("bibliometrix", "here", "dplyr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保here包正确初始化
if (!requireNamespace("here", quietly = TRUE)) {
  stop("无法加载here包，请手动安装：install.packages('here')")
}

# 定义日志函数（与之前脚本保持一致）
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

config <- list(
  paths = list(
    baseline_datasets = here("data_repository", "01_baseline_datasets", "bibliometrix_processed")
  ),
  files = list(
    biblioshiny_dedup = "datay_bibliometrix_biblioshiny.rds",
    extreme_dedup = "datax_bibliometrix_advanced.rds"
  )
)

# --- 1. 设置环境与加载数据 ---
log_message("开始评估去重结果差异流程")

biblioshiny_file <- file.path(config$paths$baseline_datasets, config$files$biblioshiny_dedup)
extreme_file <- file.path(config$paths$baseline_datasets, config$files$extreme_dedup)

# 检查文件是否存在
if (!file.exists(biblioshiny_file)) {
  log_message(sprintf("错误：Biblioshiny去重结果文件不存在：%s", biblioshiny_file), "error")
  stop("请确保已运行02_deduplication_biblioshiny.R")
}

if (!file.exists(extreme_file)) {
  log_message(sprintf("错误：极限去重结果文件不存在：%s", extreme_file), "error")
  stop("请确保已运行02_deduplication_extreme.R")
}

M_biblioshiny <- readRDS(biblioshiny_file)
M_extreme <- readRDS(extreme_file)

log_message(sprintf("成功加载Biblioshiny去重结果，记录数: %d", nrow(M_biblioshiny)))
log_message(sprintf("成功加载极限去重结果，记录数: %d", nrow(M_extreme)))

# --- 2. 比较记录数 ---
diff_records_count <- nrow(M_biblioshiny) - nrow(M_extreme)
log_message(sprintf("Biblioshiny去重结果与极限去重结果的记录数差异: %d", diff_records_count))

# --- 3. 找出仅存在于Biblioshiny去重结果中的记录（即被极限去重移除的记录）---
# 通常，我们会通过UT字段进行比较，因为它是Web of Science的唯一标识符
removed_by_extreme <- anti_join(M_biblioshiny, M_extreme, by = "UT")

if (nrow(removed_by_extreme) > 0) {
  log_message(sprintf("极限去重额外移除了 %d 条记录 (相比Biblioshiny去重)", nrow(removed_by_extreme)))
  log_message("这些记录的标题和UT (前20条)：")
  print(select(head(removed_by_extreme, 20), TI, UT, DI, PY))
} else {
  log_message("极限去重没有额外移除记录（与Biblioshiny去重相比）")
}

# --- 4. 找出仅存在于极限去重结果中的记录（理论上不应有，除非数据源或逻辑差异）---
# 这可以作为一种数据完整性检查
kept_by_extreme_only <- anti_join(M_extreme, M_biblioshiny, by = "UT")

if (nrow(kept_by_extreme_only) > 0) {
  log_message(sprintf("警告：发现 %d 条记录仅存在于极限去重结果中（不应出现此情况）", nrow(kept_by_extreme_only)), "warning")
  log_message("这些记录的标题和UT (前20条)：")
  print(select(head(kept_by_extreme_only, 20), TI, UT, DI, PY))
} else {
  log_message("数据完整性检查通过：极限去重结果中没有额外记录（与Biblioshiny去重相比）")
}

log_message("评估去重结果差异流程完成") 
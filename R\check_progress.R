# 检查DOI补全进度的脚本

library(here)

# 检查输出文件是否存在
output_file <- here("data_repository", "04_enhancement_reports", "doi_completion_full_enhanced.csv")

if (file.exists(output_file)) {
  cat("发现输出文件，正在分析...\n")
  
  # 读取结果
  results <- read.csv(output_file, stringsAsFactors = FALSE)
  
  # 统计进度
  total_records <- nrow(results)
  completed_records <- sum(results$status %in% c("success", "failed"))
  success_count <- sum(results$status == "success", na.rm = TRUE)
  
  cat(sprintf("总记录数: %d\n", total_records))
  cat(sprintf("已处理: %d (%.2f%%)\n", completed_records, 100 * completed_records / total_records))
  cat(sprintf("成功补全: %d (%.2f%%)\n", success_count, 100 * success_count / total_records))
  
  if (completed_records < total_records) {
    cat(sprintf("剩余: %d 条记录\n", total_records - completed_records))
    cat("处理仍在进行中...\n")
  } else {
    cat("处理已完成！\n")
  }
} else {
  cat("输出文件尚未生成，处理可能刚开始或遇到问题\n")
  cat(sprintf("期望的输出文件: %s\n", output_file))
}

# 检查日志或临时文件
temp_files <- list.files(here("data_repository", "04_enhancement_reports"), 
                        pattern = "doi_completion", full.names = TRUE)
if (length(temp_files) > 0) {
  cat("\n发现相关文件:\n")
  for (file in temp_files) {
    info <- file.info(file)
    cat(sprintf("- %s (大小: %d bytes, 修改时间: %s)\n", 
                basename(file), info$size, info$mtime))
  }
}

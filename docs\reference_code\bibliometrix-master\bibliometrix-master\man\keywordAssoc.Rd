% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/keywordAssoc.R
\name{keywordAssoc}
\alias{keywordAssoc}
\title{ID and DE keyword associations}
\usage{
keywordAssoc(M, sep = ";", n = 10, excludeKW = NA)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to manuscripts and variables to Field Tag in the original SCOPUS and Clarivate Analytics WoS file.}

\item{sep}{is the field separator character. This character separates keywords in each string of ID and DE columns of the bibliographic data frame. The default is \code{sep = ";"}.}

\item{n}{is a integer. It indicates the number of authors' keywords to associate to each keyword plus. The default is \code{n = 10}.}

\item{excludeKW}{is character vector. It contains authors' keywords to exclude from the analysis.}
}
\value{
an object of \code{class} "list".
}
\description{
It associates authors' keywords to keywords plus.
}
\examples{

data(scientometrics, package = "bibliometrixData")

KWlist <- keywordAssoc(scientometrics, sep = ";",n = 10, excludeKW = NA)

# list of first 10 Keywords plus
names(KWlist)

# list of first 10 authors' keywords associated to the first Keyword plus
KWlist[[1]][1:10]

}
\seealso{
\code{\link{convert2df}} to import and convert a WoS or SCOPUS Export file in a bibliographic data frame.

\code{\link{biblioAnalysis}} function for bibliometric analysis.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.
}

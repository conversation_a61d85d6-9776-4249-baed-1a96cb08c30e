# OpenAlex DOI补全系统
# 基于OpenAlex API的DOI补全功能，与Crossref系统兼容

library(httr)
library(jsonlite)
library(stringdist)

# 加载现有的核心函数
source("doi_completion_core.R")

# === OpenAlex API 查询函数 ===
search_doi_openalex <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    # OpenAlex API查询 - 使用search参数和filter
    search_query <- paste(keywords, collapse = " ")
    year_filter <- sprintf("publication_year:%s-%s", as.numeric(year)-2, as.numeric(year)+2)
    
    url <- sprintf("https://api.openalex.org/works?search=%s&filter=%s&per-page=15", 
                   URLencode(search_query), URLencode(year_filter))
    
    response <- GET(url, 
                   user_agent("DOI_Completion_OpenAlex/1.0"), 
                   timeout(30),
                   add_headers("Accept" = "application/json"))
    
    if (status_code(response) != 200) {
      cat("OpenAlex API返回状态码:", status_code(response), "\n")
      return(NULL)
    }
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$results) || length(content$results) == 0) {
      cat("OpenAlex API未返回结果\n")
      return(NULL)
    }
    
    items <- content$results
    best_match <- NULL
    best_score <- 0
    
    cat(sprintf("OpenAlex返回 %d 个候选结果\n", nrow(items)))
    
    # 评估每个候选结果
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      # 提取OpenAlex数据字段
      candidate_title <- if (!is.null(item$title) && item$title != "") item$title else ""
      
      # 提取期刊名称
      candidate_journal <- ""
      if (!is.null(item$primary_location) && !is.null(item$primary_location$source) && 
          !is.null(item$primary_location$source$display_name)) {
        candidate_journal <- item$primary_location$source$display_name
      }
      
      # 提取年份
      candidate_year <- if (!is.null(item$publication_year)) item$publication_year else ""
      
      # 提取DOI (去掉https://doi.org/前缀)
      candidate_doi <- ""
      if (!is.null(item$doi) && item$doi != "") {
        candidate_doi <- gsub("^https://doi.org/", "", item$doi)
      } else {
        next  # 如果没有DOI，跳过这个结果
      }
      
      # 计算各维度相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      journal_sim <- calculate_journal_similarity(journal, candidate_journal)
      year_sim <- calculate_year_similarity(year, candidate_year)
      subject_rel <- check_subject_relevance(title, candidate_title)
      
      # 综合评分 (权重: 标题50%, 期刊25%, 年份15%, 学科10%)
      final_score <- (title_sim * 0.5) + (journal_sim * 0.25) + (year_sim * 0.15) + (subject_rel * 0.1)
      
      cat(sprintf("候选 %d: 标题相似度=%.3f, 期刊=%.3f, 年份=%.3f, 学科=%.3f, 总分=%.3f\n",
                  i, title_sim, journal_sim, year_sim, subject_rel, final_score))
      
      # 严格的接受条件 (与Crossref相同的标准)
      if (title_sim >= 0.75 &&           # 标题相似度阈值
          journal_sim >= 0.4 &&          # 期刊匹配度阈值
          year_sim >= 0.5 &&             # 年份匹配度阈值
          subject_rel >= 0.8 &&          # 学科相关性阈值
          final_score >= 0.65 &&         # 综合评分阈值
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = candidate_doi,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          subject_relevance = subject_rel,
          final_score = final_score,
          source = "openalex"
        )
        
        cat(sprintf("  ✅ 新的最佳匹配! DOI: %s\n", candidate_doi))
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("OpenAlex API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 多引擎DOI搜索函数 ===
search_doi_multi_engine <- function(title, authors, year, journal, prefer_crossref = TRUE) {
  cat(sprintf("🔍 搜索DOI: %s (%s)\n", substr(title, 1, 50), year))
  
  if (prefer_crossref) {
    # 策略1: 优先使用Crossref，失败时使用OpenAlex
    cat("  尝试Crossref API...\n")
    crossref_result <- search_doi(title, authors, year, journal)
    
    if (!is.null(crossref_result)) {
      crossref_result$source <- "crossref"
      cat(sprintf("  ✅ Crossref成功: %s (评分: %.3f)\n", 
                  crossref_result$doi, crossref_result$final_score))
      return(crossref_result)
    }
    
    cat("  Crossref未找到，尝试OpenAlex API...\n")
    openalex_result <- search_doi_openalex(title, authors, year, journal)
    
    if (!is.null(openalex_result)) {
      cat(sprintf("  ✅ OpenAlex成功: %s (评分: %.3f)\n", 
                  openalex_result$doi, openalex_result$final_score))
      return(openalex_result)
    }
    
  } else {
    # 策略2: 优先使用OpenAlex，失败时使用Crossref
    cat("  尝试OpenAlex API...\n")
    openalex_result <- search_doi_openalex(title, authors, year, journal)
    
    if (!is.null(openalex_result)) {
      cat(sprintf("  ✅ OpenAlex成功: %s (评分: %.3f)\n", 
                  openalex_result$doi, openalex_result$final_score))
      return(openalex_result)
    }
    
    cat("  OpenAlex未找到，尝试Crossref API...\n")
    crossref_result <- search_doi(title, authors, year, journal)
    
    if (!is.null(crossref_result)) {
      crossref_result$source <- "crossref"
      cat(sprintf("  ✅ Crossref成功: %s (评分: %.3f)\n", 
                  crossref_result$doi, crossref_result$final_score))
      return(crossref_result)
    }
  }
  
  cat("  ❌ 两个引擎都未找到匹配结果\n")
  return(NULL)
}

# === 使用示例 ===
cat("✅ OpenAlex DOI补全系统已加载\n")
cat("📋 主要函数:\n")
cat("  - search_doi_openalex()      : 仅使用OpenAlex\n")
cat("  - search_doi_multi_engine()  : 多引擎DOI搜索\n")
cat("  - search_doi()               : 原有Crossref函数\n")

# 测试函数
test_openalex_doi <- function() {
  cat("\n=== OpenAlex DOI补全测试 ===\n")
  
  # 测试用例
  test_cases <- list(
    list(
      title = "Machine learning applications in healthcare",
      authors = "Johnson A",
      year = 2020,
      journal = "Nature Medicine"
    ),
    list(
      title = "Deep learning for natural language processing",
      authors = "Brown C",
      year = 2019,
      journal = "Journal of Machine Learning Research"
    )
  )
  
  for (i in 1:length(test_cases)) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n--- 测试案例 %d ---\n", i))
    cat(sprintf("标题: %s\n", test_case$title))
    
    # 测试OpenAlex
    result <- search_doi_openalex(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal
    )
    
    if (!is.null(result)) {
      quality <- assess_quality(result$title_similarity, result$final_score)
      cat(sprintf("✅ OpenAlex找到: %s (质量: %s)\n", result$doi, quality))
    } else {
      cat("❌ OpenAlex未找到匹配\n")
    }
    
    if (i < length(test_cases)) {
      cat("等待2秒...\n")
      Sys.sleep(2)
    }
  }
  
  cat("\n✅ 测试完成\n")
}

# 如果需要测试，取消注释下面这行
# test_openalex_doi()

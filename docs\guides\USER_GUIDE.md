# 文献计量分析系统使用指南

## 快速开始

### 1. 环境准备

#### 1.1 R环境要求
- R版本: >= 4.0.0
- 推荐内存: >= 8GB
- 推荐存储: >= 10GB可用空间

#### 1.2 必需R包
```r
# 安装必需的包
required_packages <- c(
  "bibliometrix",    # 文献计量分析
  "here",           # 路径管理
  "dplyr",          # 数据处理
  "stringdist",     # 字符串距离计算
  "httr",           # HTTP请求
  "jsonlite",       # JSON处理
  "openxlsx"        # Excel文件处理
)

install.packages(required_packages)
```

### 2. 项目初始化

#### 2.1 下载项目
```bash
# 克隆项目到本地
git clone [项目地址]
cd bibliometric-analysis
```

#### 2.2 检查项目结构
```r
# 在R中检查项目结构
setwd("path/to/bibliometric-analysis")
source("R/config.R")

# 验证配置
PROJECT_CONFIG$paths
```

## 数据处理流程

### 第1步: 准备原始数据

#### 1.1 WoS数据导出
1. 登录Web of Science数据库
2. 进行文献检索
3. 选择"导出"→"纯文本"
4. 选择"完整记录和引用的参考文献"
5. 保存为.txt文件

#### 1.2 数据文件放置
```bash
# 将WoS导出的txt文件放置到指定目录
data_repository/01_raw_data/wos_files/
├── download1-500.txt
├── download501-1000.txt
└── ...
```

### 第2步: 数据导入与转换

```r
# 运行数据导入脚本
source("R/01_data_import.R")
```

**功能说明:**
- 自动读取所有WoS txt文件
- 使用bibliometrix::convert2df进行格式转换
- 基于UT字段进行基础去重
- 生成字段缺失值统计报告
- 保存为标准化的RDS格式

**输出文件:**
- `data_repository/02_baseline_data/bibliometrix/datay_bibliometrix_initial.rds`
- `data_repository/02_baseline_data/bibliometrix/import_missing_stats.csv`

### 第3步: 去重处理

```r
# 运行去重处理脚本
source("R/04_deduplication.R")
```

**功能说明:**
- DOI精确匹配去重
- 标题多轮模糊匹配（阈值：0.98, 0.95, 0.90）
- 作者模糊匹配（阈值：0.95）
- 摘要模糊匹配（阈值：0.90）
- 生成去重效果报告

**输出文件:**
- `data_repository/03_enhanced_data/deduplication/datax_enhanced_*.rds`
- `data_repository/05_reports/processing/deduplication_report.md`

### 第4步: DOI补全

```r
# 运行DOI补全脚本
source("R/05_doi_completion.R")
```

**功能说明:**
- 识别缺失DOI的文献记录
- 通过Crossref API进行DOI检索
- 多维度相似度验证（标题+期刊+年份+学科）
- 四级质量评估体系
- 自动生成补全报告

**输出文件:**
- `data_repository/03_enhanced_data/doi_completion/COMPLETE_DOI_RESULTS.csv`
- `data_repository/03_enhanced_data/doi_completion/AUTO_ANALYSIS_REPORT.txt`

### 第5步: 质量控制

```r
# 运行质量控制脚本
source("R/07_quality_control.R")
```

**功能说明:**
- 全面的数据质量评估
- 处理效果统计分析
- 异常记录识别
- 质量报告生成

**输出文件:**
- `data_repository/05_reports/quality/quality_assessment.md`
- `data_repository/05_reports/final/processing_summary.md`

## 配置管理

### 1. 基本配置

#### 1.1 路径配置
```r
# 在config.R中修改路径设置
PROJECT_CONFIG$paths <- list(
  raw_data = "data_repository/01_raw_data",
  baseline_data = "data_repository/02_baseline_data",
  # ... 其他路径
)
```

#### 1.2 处理参数配置
```r
# 去重参数调整
PROJECT_CONFIG$processing$deduplication <- list(
  title_tolerances = c(0.98, 0.95, 0.90),  # 标题相似度阈值
  author_tolerance = 0.95,                  # 作者相似度阈值
  abstract_tolerance = 0.90                 # 摘要相似度阈值
)

# DOI补全参数调整
PROJECT_CONFIG$processing$doi_completion <- list(
  title_threshold = 0.75,      # 标题相似度阈值
  journal_threshold = 0.4,     # 期刊匹配度阈值
  year_threshold = 0.5,        # 年份匹配度阈值
  subject_threshold = 0.8,     # 学科相关性阈值
  final_threshold = 0.65,      # 综合评分阈值
  api_delay = 1.0,             # API调用间隔（秒）
  batch_size = 100             # 批处理大小
)
```

### 2. 高级配置

#### 2.1 性能优化
```r
# 内存管理
options(java.parameters = "-Xmx8g")  # 设置Java堆内存
memory.limit(size = 16000)           # 设置R内存限制（Windows）

# 并行处理
library(parallel)
options(mc.cores = detectCores() - 1)  # 设置并行核心数
```

#### 2.2 API配置
```r
# Crossref API配置
Sys.setenv(CROSSREF_EMAIL = "<EMAIL>")  # 设置邮箱
```

## 结果解读

### 1. 去重结果

#### 1.1 去重统计
- **原始记录数**: 去重前的总记录数
- **去重后记录数**: 去重后的记录数
- **去重率**: (原始记录数 - 去重后记录数) / 原始记录数

#### 1.2 去重质量评估
- **精确匹配**: 基于DOI和UT的精确去重
- **模糊匹配**: 基于标题、作者、摘要的模糊匹配
- **误删风险**: 评估可能的误删记录

### 2. DOI补全结果

#### 2.1 补全统计
- **总记录数**: 需要补全DOI的记录数
- **成功补全数**: 成功找到DOI的记录数
- **成功率**: 成功补全数 / 总记录数

#### 2.2 质量等级
- **卓越质量**: 标题相似度≥0.95且综合评分≥0.85，可直接使用
- **优秀质量**: 标题相似度≥0.85且综合评分≥0.75，推荐使用
- **良好质量**: 标题相似度≥0.75且综合评分≥0.65，需要验证
- **可接受质量**: 满足基本阈值条件，需要详细审核

### 3. 质量控制结果

#### 3.1 数据完整性
- **字段缺失率**: 各字段的缺失百分比
- **数据类型**: 字段数据类型的正确性
- **值域检查**: 数据值的合理性

#### 3.2 处理效果
- **处理时间**: 各阶段的处理耗时
- **资源使用**: 内存和CPU使用情况
- **错误记录**: 处理过程中的异常记录

## 常见问题

### 1. 内存不足
**问题**: 处理大数据集时出现内存不足错误
**解决方案**:
```r
# 增加内存限制
memory.limit(size = 16000)

# 分批处理
batch_size <- 1000  # 减小批处理大小
```

### 2. API调用失败
**问题**: Crossref API调用频繁失败
**解决方案**:
```r
# 增加API调用间隔
api_delay <- 2.0  # 增加到2秒

# 设置邮箱（提高API限制）
Sys.setenv(CROSSREF_EMAIL = "<EMAIL>")
```

### 3. 处理时间过长
**问题**: 大数据集处理时间过长
**解决方案**:
```r
# 调整去重阈值（降低精度，提高速度）
title_tolerances <- c(0.95, 0.90)  # 减少轮次

# 启用并行处理
library(parallel)
options(mc.cores = detectCores() - 1)
```

### 4. 结果质量不满意
**问题**: DOI补全质量不理想
**解决方案**:
```r
# 调整阈值参数（提高精度）
title_threshold <- 0.80      # 提高标题阈值
final_threshold <- 0.70      # 提高综合阈值

# 增加学科关键词
medical_keywords <- c(...)   # 扩展关键词库
```

## 技术支持

### 1. 日志文件
- **处理日志**: `data_repository/07_logs/processing/`
- **错误日志**: `data_repository/07_logs/errors/`

### 2. 报告文件
- **处理报告**: `data_repository/05_reports/processing/`
- **质量报告**: `data_repository/05_reports/quality/`
- **最终报告**: `data_repository/05_reports/final/`

### 3. 联系方式
如遇到技术问题，请：
1. 检查日志文件中的错误信息
2. 查看相关报告文件
3. 参考本使用指南
4. 联系技术支持团队

# research_pipeline.R
# 这个脚本将逐步实现研究框架

# --- 1. 数据加载与格式转换 ---

# 1.1 加载必要的库
# 加载 bibliometrix 用于文献计量分析，tidyverse 用于数据处理和文件操作
library(bibliometrix)
library(tidyverse)

# 检查并安装 writexl 包 (用于写入 Excel 文件)
if (!requireNamespace("writexl", quietly = TRUE)) {
  cat("writexl 包未安装，正在尝试安装...\n")
  install.packages("writexl")
}
library(writexl)

# --- 新增辅助函数：获取原始记录和来源文件 ---
get_raw_record_and_source_file <- function(target_ut, wos_files_list) {
  # target_ut 来自 M$UT, 通常是 "WOS:XXXX" 或 "ISI:XXXX"
  for (file_path in wos_files_list) {
    tryCatch({
      raw_lines <- readLines(file_path, warn = FALSE)
      
      # 查找包含 target_ut 的 UT 标签行
      # WoS 文件中的UT行通常是 "UT WOS:XXXX..." 或 "UT ISI:XXXX..."
      # 我们期望 target_ut 就是 "WOS:XXXX..." 或 "ISI:XXXX..." 部分
      ut_line_indices <- which(startsWith(raw_lines, "UT "))
      found_ut_line_idx <- -1
      if (length(ut_line_indices) > 0) {
        for(idx in ut_line_indices){
          if(grepl(target_ut, raw_lines[idx], fixed = TRUE)){
            found_ut_line_idx <- idx
            break
          }
        }
      }

      if (found_ut_line_idx > 0) {
        current_ut_line <- found_ut_line_idx
        start_line_idx <- -1
        # 向后搜索 PT (Publication Type) 或 FN (File Name) 作为记录开始
        for (k in seq(current_ut_line, 1, by = -1)) {
          if (grepl("^PT ", raw_lines[k]) || grepl("^FN ", raw_lines[k])) {
            start_line_idx <- k
            break
          }
        }
      }

      if (start_line_idx > 0) {
        # 提取记录
        record <- raw_lines[start_line_idx:current_ut_line]
        # 提取记录的唯一标识符
        ut <- sub("^UT ", "", record[1])
        # 提取记录的其他部分
        record_data <- record[-1]
        # 创建数据框
        record_df <- data.frame(UT = ut, record = paste(record_data, collapse = "\n"))
        return(record_df)
      }
    }, error = function(e) {
      cat("读取文件时出错:", e$message, "文件路径:", file_path, "\n")
      return(NULL)
    })
  }
  return(NULL)
}

# 1.2 加载并转换数据
# 设置工作目录和数据路径
working_dir <- "C:/Users/<USER>/Desktop/article/数据处理部分" 
raw_data_dir <- "C:/Users/<USER>/Desktop/数据文件/citespace数据" 

cat(paste0("尝试设置工作目录到: ", working_dir, "\n"))
tryCatch({
  setwd(working_dir)
  cat("当前工作目录已设置为:", getwd(), "\n")
}, error = function(e){
  stop(paste0("无法设置工作目录: ", working_dir, "错误: ", e$message))
})

if (!dir.exists(raw_data_dir)) {
  stop(paste0("指定的原始数据目录不存在: ", raw_data_dir))
} else {
  cat("找到原始数据目录:", raw_data_dir, "\n")
}

# 读取并转换 WoS 数据文件
wos_files <- list.files(path = raw_data_dir, pattern = "\\.txt$", full.names = TRUE, ignore.case = TRUE) # 忽略大小写

if (length(wos_files) == 0) {
  stop(paste0("在指定目录下没有找到 .txt 文件: ", raw_data_dir))
} else {
  cat("找到", length(wos_files), "个 .txt 文件，准备进行转换...\n")
  print(basename(wos_files))
}

cat("\n开始使用 bibliometrix 进行格式转换 (convert2df)...\n")
# remove.duplicates = TRUE 是 convert2df 的默认行为
M_initial_for_testing <- convert2df(file = wos_files, dbsource = "wos", format = "plaintext") 
cat("初始转换完成 (convert2df)。数据框 M_initial_for_testing 包含", nrow(M_initial_for_testing), "条记录 和", ncol(M_initial_for_testing), "个字段。\n")
# 注意: convert2df 内部的去重数量不容易直接获取，可以通过比较原始文件总记录数和转换后记录数来估算。

# --- 2. 自动化去重测试框架 ---
cat("\n--- 初始化自动化去重测试框架 ---\n")

# 2.1 创建输出子目录
dedup_output_dir <- file.path(working_dir, "deduplication_tests_output")
if (!dir.exists(dedup_output_dir)){
  dir.create(dedup_output_dir, recursive = TRUE)
  cat("创建去重测试输出目录:", dedup_output_dir, "\n")
} else {
  cat("去重测试输出目录已存在:", dedup_output_dir, "\n")
}

# 2.2 定义去重测试执行函数
run_deduplication_test <- function(data_input, field_to_match, is_exact_match, tolerance_value, strategy_name_suffix = "", output_dir_path) {
  
  # 构建策略名称和输出文件名
  strategy_name <- paste0(field_to_match, 
                          ifelse(is_exact_match, "_Exact", paste0("_Tol", gsub("\\.", "_", as.character(tolerance_value)))), 
                          strategy_name_suffix)
  output_file_path <- file.path(output_dir_path, paste0("report_", strategy_name, ".txt"))
  
  # 使用sink开始将输出重定向到文件 (覆盖模式)
  zz <- file(output_file_path, open = "wt") # open for writing text
  sink(zz)
  sink(zz, type = "message") # Redirect messages (like from cat) as well

  cat(paste0("--- 测试策略报告: ", strategy_name, " ---\n"))
  cat(paste0("执行时间: ", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n"))
  cat(paste0("输入记录数: ", nrow(data_input), "\n"))
  cat(paste0("匹配字段: '", field_to_match, "', Exact匹配: ", is_exact_match, 
             ", 相似度阈值(tol): ", ifelse(is_exact_match, "N/A", format(tolerance_value, nsmall=2)), "\n\n"))
  
  M_before_test_func <- data_input 
  
  # 确保关键字段是字符型 (UT 和要匹配的字段)
  if ("UT" %in% names(M_before_test_func)) M_before_test_func$UT <- as.character(M_before_test_func$UT)
  if (field_to_match %in% names(M_before_test_func)) M_before_test_func[[field_to_match]] <- as.character(M_before_test_func[[field_to_match]])
  # 其他常用字段也转换为字符以避免后续提取时出错
  common_fields <- c("TI", "AU", "AF", "PY", "SO", "AB", "DI")
  for(cf in common_fields){
      if (cf %in% names(M_before_test_func)) M_before_test_func[[cf]] <- as.character(M_before_test_func[[cf]])
  }

  M_to_process_func <- M_before_test_func
  M_skipped_func <- data.frame() # 初始化为空数据框
  
  if (!is_exact_match && field_to_match %in% names(M_before_test_func)) {
    condition_empty_na <- is.na(M_before_test_func[[field_to_match]]) | M_before_test_func[[field_to_match]] == ""
    if(any(condition_empty_na)){
        M_to_process_func <- M_before_test_func[!condition_empty_na, , drop = FALSE]
        M_skipped_func <- M_before_test_func[condition_empty_na, , drop = FALSE]
        cat(paste0("注意: 有 ", nrow(M_skipped_func), " 条记录因 '", field_to_match, "' 字段为空或NA而未直接参与此轮匹配。\n"))
    }
  }

  M_after_processed_part_func <- M_to_process_func 
  if(nrow(M_to_process_func) > 1) { 
      cat(paste0("对 ", nrow(M_to_process_func), " 条记录执行 duplicatedMatching (Field='", field_to_match, "', exact=", is_exact_match, 
                 if(!is_exact_match) paste0(", tol=", tolerance_value) else "", ")...\n"))
      if(is_exact_match){
          M_after_processed_part_func <- duplicatedMatching(M_to_process_func, Field = field_to_match, exact = TRUE)
      } else {
          M_after_processed_part_func <- duplicatedMatching(M_to_process_func, Field = field_to_match, exact = FALSE, tol = tolerance_value)
      }
      cat("duplicatedMatching 执行完毕。\n")
  } else {
      cat(paste0("字段 '", field_to_match, "' 非空/NA的记录不足2条（仅 ", nrow(M_to_process_func) ," 条），跳过 duplicatedMatching。\n"))
  }

  M_after_test_func <- M_after_processed_part_func
  if (nrow(M_skipped_func) > 0) {
    if (nrow(M_after_processed_part_func) == 0 ) { 
        M_after_test_func <- M_skipped_func
    } else { # 尝试合并，确保列名对齐
        # 找出共同的列名
        cols_processed <- names(M_after_processed_part_func)
        cols_skipped <- names(M_skipped_func)
        common_cols <- intersect(cols_processed, cols_skipped)
        
        # 如果没有共同列或者关键列丢失，这可能是一个问题，但我们尝试合并
        if(length(common_cols) > 0){
             M_after_test_func <- rbind(M_after_processed_part_func[, common_cols, drop=FALSE], 
                                     M_skipped_func[, common_cols, drop=FALSE])
        } else {
             cat("警告: 合并被跳过记录时，处理后部分与跳过部分无共同列名。整体计数可能不准。\n")
             # 此时 M_after_test_func 仍然是 M_after_processed_part_func
        }
    }
  }
  
  removed_in_processed_part_count <- nrow(M_to_process_func) - nrow(M_after_processed_part_func)
  overall_removed_count <- nrow(M_before_test_func) - nrow(M_after_test_func)

  cat(paste0("\n处理非空'", field_to_match, "'字段记录: 从 ", nrow(M_to_process_func) ," 条变为 ", nrow(M_after_processed_part_func) ," 条 (移除了 ", removed_in_processed_part_count, " 条)。\n"))
  cat(paste0("整体数据框: 从 ", nrow(M_before_test_func) ," 条变为 ", nrow(M_after_test_func) ," 条 (总共移除了 ", overall_removed_count, " 条)。\n\n"))
  
  # 控制台摘要 (输出到标准输出，而不是被sink的文件)
  message(paste0("测试策略 '", strategy_name, "': 在处理部分移除了 ", removed_in_processed_part_count, " 条. 总移除 ", overall_removed_count, " 条. 报告: ", basename(output_file_path)))

  if (removed_in_processed_part_count > 0 && "UT" %in% names(M_to_process_func) && "UT" %in% names(M_after_processed_part_func)) {
    # 使用 M_to_process_func 和 M_after_processed_part_func 来找出被移除的记录的详情
    removed_articles_details_func <- dplyr::anti_join(M_to_process_func, M_after_processed_part_func, by = "UT")
  }
}

# 2.3 执行去重测试
# 这里需要根据实际情况来决定如何执行去重测试。
# 例如，可以遍历不同的字段和匹配策略，或者根据需要选择特定的字段进行测试。

# 示例：遍历不同的字段和匹配策略
fields_to_test <- c("TI", "AU", "AF", "PY", "SO", "AB", "DI")
exact_match_fields <- c("TI", "AU", "AF", "PY", "SO", "AB", "DI")
tolerance_values <- c(0.5, 0.7, 0.9)

for (field in fields_to_test) {
  for (exact_match in exact_match_fields) {
    for (tolerance in tolerance_values) {
      run_deduplication_test(M_initial_for_testing, field, exact_match == field, tolerance)
    }
  }
}

# 示例：根据需要选择特定的字段进行测试
# 例如，选择 "TI" 和 "AU" 进行测试
run_deduplication_test(M_initial_for_testing, "TI", TRUE, 0.5)
run_deduplication_test(M_initial_for_testing, "AU", TRUE, 0.5)

# 3. 结果分析和报告
# 这里需要根据实际情况来决定如何分析和报告去重测试结果。
# 例如，可以生成报告文件，或者将结果保存到数据库中进行分析。

# 示例：生成报告文件
report_file <- file.path(working_dir, "deduplication_tests_report.txt")
sink(report_file)
cat("去重测试报告\n")
cat("================\n")
cat("测试时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n")
cat("测试数据:", basename(wos_files), "\n")
cat("测试结果:\n")

for (field in fields_to_test) {
  for (exact_match in exact_match_fields) {
    for (tolerance in tolerance_values) {
      strategy_name <- paste0(field, 
                              ifelse(exact_match == field, "_Exact", paste0("_Tol", gsub("\\.", "_", as.character(tolerance)))), 
                              "")
      cat("策略:", strategy_name, "\n")
      cat("输入记录数:", nrow(M_initial_for_testing), "\n")
      cat("处理后记录数:", nrow(M_initial_for_testing) - nrow(run_deduplication_test(M_initial_for_testing, field, exact_match == field, tolerance)), "\n")
      cat("移除记录数:", nrow(run_deduplication_test(M_initial_for_testing, field, exact_match == field, tolerance)), "\n")
    }
  }
}

sink()

cat("去重测试报告已保存到:", report_file, "\n") 
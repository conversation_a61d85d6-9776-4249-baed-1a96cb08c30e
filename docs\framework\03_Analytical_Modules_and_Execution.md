# 03 核心分析模块详解与执行

本文件详细定义了本研究框架所依赖的一系列核心文献计量分析任务和视角，构成一个"分析模块库"。这些模块不仅包括领域概览、核心贡献者分析、知识结构图谱等常规文献计量分析，还**重点引入了若干旨在进行深度机制探索和趋势预测的高级分析模块**。这些模块是实施常规基线数据路径（评估工具原始处理能力）和增强数据路径（基于高质量数据的多工具比较与整合）分析的具体执行内容。

**各分析模块的描述将遵循以下结构：**
*   **模块名称与编号**
*   **分析目标：** 该模块旨在解决什么科学问题或提供何种洞见。
*   **核心方法/算法简述：** 采用的主要文献计量指标、网络构建方法、统计技术或算法模型。
*   **所需输入数据：**
    *   明确依赖"增强数据集" (`enhanced_data`) 还是"常规基线数据集" (`baseline_data`)，或两者皆可（用于常规基线数据路径对比）。增强数据集存放于 `data/enhanced/` 目录下，常规基线数据集存放于 `data/baseline/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   指明需要数据集中的哪些关键字段（如AU, DE, ID, CR, PY, SO, TC等）。
*   **关键参数说明：** 执行该模块时需要关注和设置的关键参数（如时间窗口、阈值、节点类型、链接关系、聚类算法等）。
*   **预期输出与解读：** 模块执行后产生的主要结果形式（如表格、网络图、统计报告）及其解读方式。
*   **在常规基线数据路径/增强数据路径中的应用：** 说明该模块如何在常规基线数据路径中被用于评估工具原始处理能力，或在增强数据路径中被用于比较不同工具。
*   **与高级/个性化分析的关联（如适用）：** 若模块属于高级分析或本框架提出的个性化方法，阐述其创新点和价值。

---

## 1. 模块一：领域发展态势与出版趋势分析 (6.1)

*   **分析目标：** 揭示研究领域的年度发文量、文献类型分布、主要国家/地区贡献、高产期刊、重要资助机构等总体发展态势和关键特征。
*   **核心方法/算法简述：** 描述性统计（频数、百分比、均值、中位数）、趋势分析（如年增长率、复合年均增长率CAGR、时间序列平滑如LOESS）、Lotka定律（作者生产力分布）、Bradford定律（期刊文献分布集中与分散）。
*   **所需输入数据：**
    *   常规基线数据路径：常规基线数据集 (`baseline_data`)，存放于 `data/baseline/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   增强数据路径：增强数据集 (`enhanced_data`)，存放于 `data/enhanced/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   关键字段：PY (发表年份), DT (文献类型), C1 (作者机构，用于提取国家/地区), AU (作者), SO (期刊名), J9 (期刊标准缩写), JI (ISO期刊缩写), TC (总被引次数), FU (基金资助机构), FX (基金资助详情), SC (学科分类), LA (语言)。
*   **关键参数说明：** 分析时间窗口（如最近10年、全部时间）、统计单位（年、季度）、国家/机构/期刊的最低发文量/被引频次阈值、Lotka定律的幂指数估计、Bradford定律的核心区划分。
*   **预期输出与解读：**
    *   年度发文量及增长趋势图/表。
    *   文献类型构成比例图/表。
    *   发文量/被引频次Top N 国家/地区、机构、期刊、作者列表。
    *   核心作者的h指数、g指数、m指数等列表。
    *   期刊的Bradford核心区与分散区划分。
    *   主要资助机构列表及其资助文献数量/影响力。
    *   学科分类和语言分布统计。
*   **在常规基线数据路径/增强数据路径中的应用：**
    *   常规基线数据路径：评估不同工具在基础统计分析方面的能力，包括数据导入、字段解析、统计计算等。例如，比较各工具在处理作者、机构、期刊等字段时的准确性和完整性。
    *   增强数据路径：在高质量数据基础上，比较不同工具在统计分析功能、可视化效果、结果导出等方面的差异。例如，比较各工具在生成趋势图、分布图时的灵活性和美观度。

## 2. 模块二：核心行动者 (作者、机构、国家) 与合作网络分析 (6.2)

*   **分析目标：** 识别领域内的核心作者、高产作者、作者间的合作关系与合作网络结构，揭示主要的学术社群。类似地，分析机构间和国家间的合作模式与网络结构，评估其学术影响力。
*   **核心方法/算法简述：**
    *   **核心行动者识别：** 作者/机构/国家发文量统计、h指数、g指数、总被引频次等指标计算。
    *   **合作网络构建：** 基于作者、机构（标准化后）、国家（从机构地址中提取并标准化）的共现分析（Co-authorship, Co-institution, International Collaboration）。
    *   **网络拓扑分析：** 计算网络密度、平均路径长度、聚类系数、连通分量等全局指标。
    *   **节点中心性分析：** 度中心性 (Degree Centrality)、介数中心性 (Betweenness Centrality)、接近中心性 (Closeness Centrality)、特征向量中心性 (Eigenvector Centrality)。
    *   **社群发现算法：** 如Louvain, Leiden, Girvan-Newman等，用于识别合作子网络或学术社群。
    *   **合作强度度量：** 如 Salton 指数或 Jaccard 指数，用于量化行动者间的合作紧密程度。
*   **所需输入数据：**
    *   常规基线数据路径：常规基线数据集 (`baseline_data`)，存放于 `data/baseline/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   增强数据路径：增强数据集 (`enhanced_data`)，存放于 `data/enhanced/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   关键字段：AU (作者), AF (作者全名), C1 (作者机构/国家，用于提取机构和国家并需标准化), AU_ID (消歧后的作者ID), AFF_ID (标准化机构ID), CR (参考文献，用于计算作者h指数等), PY (年份，用于动态网络分析)。
*   **关键参数说明：**
    *   核心行动者筛选阈值（如最低发文量、最低h指数）。
    *   合作网络构建时作者/机构/国家的计数方式（全计数、小数计数）。
    *   网络节点和边的筛选阈值（如最小合作次数）。
    *   社群发现算法选择及其参数（如分辨率参数）。
    *   时间切片长度（用于动态合作网络分析）。
*   **预期输出与解读：**
    *   核心作者/机构/国家列表及其影响力指标。
    *   作者/机构/国家合作网络图（节点大小可表示发文量/影响力，边粗细可表示合作强度/次数，节点颜色可表示社群/国家/机构类型）。
    *   网络整体结构指标表（密度、聚类系数等）。
    *   节点中心性指标排名表，识别关键连接者和影响力中心。
    *   主要学术社群/合作集群列表及其特征描述。
    *   合作关系随时间演化的动态图谱。
*   **在常规基线数据路径/增强数据路径中的应用：**
    *   常规基线数据路径：评估各工具在作者消歧、机构标准化、国家提取等基础数据处理方面的能力，以及构建合作网络的基本功能。
    *   增强数据路径：在高质量数据基础上，比较不同工具在网络构建算法、可视化效果、交互功能等方面的差异，探索多工具协同应用的可能性。

## 3. 模块三：知识结构分析：关键词、文献、期刊的耦合与共现 (6.3)

此模块包含多个子模块，旨在从不同维度揭示领域的知识结构、核心主题、知识基础及学科交叉情况。

### 3.1 子模块：关键词共现网络与研究主题分析 (Intellectual Structure - Co-occurrence Network)

*   **分析目标：** 通过分析关键词（作者关键词DE、扩展关键词ID/Keywords Plus）的共现关系，识别研究领域的核心主题、主题间的关联结构，以及研究热点的动态演化。
*   **核心方法/算法简述：**
    *   关键词提取、清洗、标准化（词形还原/词干提取、同义词合并）。
    *   构建关键词共现矩阵/网络（可选加权，如按共现频次）。
    *   网络节点中心性分析（度中心性、介数中心性等）识别核心关键词。
    *   网络聚类/社群发现（如Louvain, Leiden, VOSviewer的Smart Local Moving算法）识别主题簇。
    *   主题词突现分析（Burst Detection，如Kleinberg算法）识别快速兴起或衰退的主题。
    *   时序关键词网络分析/战略坐标图（Strategic Diagram，基于中心度和密度划分主题类型：核心主题、边缘主题、发展中主题、衰退主题）。
    *   主题演化路径可视化（如使用 alluvial diagrams, sankey diagrams）。
*   **所需输入数据：**
    *   常规基线数据路径：常规基线数据集 (`baseline_data`)，存放于 `data/baseline/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   增强数据路径：增强数据集 (`enhanced_data`)，存放于 `data/enhanced/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   关键字段：DE (作者关键词), ID (扩展关键词), PY (发表年份，用于动态分析和突现分析), TI (标题), AB (摘要，可用于辅助关键词提取或主题建模)。
*   **关键参数说明：**
    *   关键词筛选阈值（如最低出现频次、最低共现频次）。
    *   共现网络构建的相似性度量（如association, equivalence, inclusion）。
    *   聚类算法及其参数（如分辨率）。
    *   时间切片长度和突现词检测参数（如gamma值）。
*   **预期输出与解读：**
    *   高频核心关键词列表、关键词共现网络图（节点大小表示频次/中心性，颜色表示主题簇）。
    *   主题聚类列表及其代表性关键词、主题标签。
    *   战略坐标图，展示各主题簇的特征。
    *   突现关键词列表及其突现时间区间。
    *   研究主题随时间演化的路径图或叠加网络图。
*   **在常规基线数据路径/增强数据路径中的应用：**
    *   常规基线数据路径：评估各工具在关键词提取、标准化、同义词处理等基础功能方面的能力，以及构建共现网络的基本功能。
    *   增强数据路径：在高质量数据基础上，比较不同工具在共现网络构建算法、聚类方法、可视化效果等方面的差异，探索多工具协同应用的可能性。

### 3.2 子模块：文献共被引网络与知识基础分析 (Intellectual Base - Co-Citation Analysis)

*   **分析目标：** 通过分析文献被共同引用的模式，识别构成领域知识基础的核心文献（经典文献、里程碑文献）、主要知识分支（研究范式、理论流派）以及它们之间的关联。
*   **核心方法/算法简述：**
    *   构建文献共被引矩阵/网络（节点为被引文献，边表示它们被其他文献共同引用的频次）。
    *   网络节点中心性分析（如高被引、高共被引频次、高介数中心性）识别关键枢纽文献。
    *   网络聚类/社群发现，识别代表不同知识领域的文献簇。
    *   引文桂冠分析（Citation Classics）：识别历史上持续高被引的经典文献。
    *   对聚类结果进行内容分析（阅读文献标题、摘要、关键词）以标记知识簇的内涵。
*   **所需输入数据：**
    *   常规基线数据路径：常规基线数据集 (`baseline_data`)，存放于 `data/baseline/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   增强数据路径：增强数据集 (`enhanced_data`)，存放于 `data/enhanced/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   关键字段：CR (参考文献列表，需精确解析和标准化), UT (文献唯一标识，用于节点识别), PY (发表年份), AU (作者), SO (期刊), TC (总被引次数，用于筛选或加权)。
*   **关键参数说明：**
    *   被引文献筛选阈值（如最低总被引频次、最低共被引频次）。
    *   共被引网络构建的时间窗口（分析特定时期内的知识基础）。
    *   聚类算法选择及其参数。
    *   节点标签选择（如第一作者+年份+期刊缩写）。
*   **预期输出与解读：**
    *   文献共被引网络图（节点大小可表示总被引频次，颜色表示知识簇）。
    *   核心知识簇列表及其代表性文献、主题描述。
    *   高影响力经典文献列表及其在网络中的位置。
    *   知识基础的结构特征和主要理论分支。
*   **在常规基线数据路径/增强数据路径中的应用：**
    *   常规基线数据路径：评估各工具在参考文献解析、文献去重等基础功能方面的能力，以及构建共被引网络的基本功能。
    *   增强数据路径：在高质量数据基础上，比较不同工具在共被引网络构建算法、知识簇识别方法、可视化效果等方面的差异，探索多工具协同应用的可能性。

### 3.3 子模块：文献耦合分析 (Bibliographic Coupling)

*   **分析目标：** 通过分析两篇文献共同引用了哪些相同的参考文献，来衡量它们在研究内容或主题上的相似性，从而揭示当前研究的前沿领域和研究集群。与共被引分析（回顾历史）不同，文献耦合分析更侧重于同时代研究间的联系。
*   **核心方法/算法简述：**
    *   构建文献耦合矩阵/网络（节点为施引文献，边表示它们共同引用的参考文献数量或耦合强度）。耦合强度可用共同引用文献数、Salton指数或Jaccard指数等度量。
    *   网络节点分析（如高耦合强度节点）和聚类分析，识别研究前沿集群。
*   **所需输入数据：**
    *   常规基线数据路径：常规基线数据集 (`baseline_data`)，存放于 `data/baseline/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   增强数据路径：增强数据集 (`enhanced_data`)，存放于 `data/enhanced/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   关键字段：CR (参考文献列表，需精确解析和标准化), UT (文献唯一标识)。
*   **关键参数说明：**
    *   文献筛选阈值（如最低总被引频次、最低发文年份）。
    *   耦合强度计算方法和阈值。
    *   聚类算法选择。
*   **预期输出与解读：**
    *   文献耦合网络图，展示当前研究文献间的相似性结构。
    *   研究前沿集群列表及其代表性文献、主题描述。
*   **在常规基线数据路径/增强数据路径中的应用：**
    *   常规基线数据路径：评估各工具在文献耦合网络构建、强度计算等基础功能方面的能力。
    *   增强数据路径：在高质量数据基础上，比较不同工具在耦合网络分析、研究前沿识别、可视化效果等方面的差异，探索多工具协同应用的可能性。

### 3.4 子模块：期刊共被引/耦合网络与学科交叉分析 (Journal Co-citation/Bibliographic Coupling)

*   **分析目标：** 识别领域的核心期刊群，分析期刊间的引用关系（被共同引用或共同引用其他期刊），揭示学科交叉、知识来源和知识扩散的模式。
*   **核心方法/算法简述：**
    *   **期刊共被引分析：** 构建期刊共被引网络（节点为被引期刊，边表示它们被共同引用的频次）。反映期刊在知识基础中的地位和关联。
    *   **期刊文献耦合分析：** 构建期刊文献耦合网络（节点为施引文献所在期刊，边表示这些期刊发表的文献共同引用了其他参考文献的强度）。反映期刊在研究前沿主题上的相似性。
    *   **期刊直接引用网络：** （较少直接用于此目的，但可辅助）分析期刊间的直接引用关系。
    *   网络分析与可视化，识别核心期刊、期刊集群以及跨学科引用模式。
*   **所需输入数据：**
    *   常规基线数据路径：常规基线数据集 (`baseline_data`)，存放于 `data/baseline/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   增强数据路径：增强数据集 (`enhanced_data`)，存放于 `data/enhanced/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
    *   关键字段：SO (期刊名，需标准化), J9 (期刊标准名), JI (ISO期刊缩写), CR (参考文献列表，需解析出被引期刊名并标准化), UT (文献唯一标识)。
*   **关键参数说明：**
    *   期刊筛选阈值（如最低发文量、最低被引/共被引频次）。
    *   共被引/耦合强度计算方法和阈值。
    *   网络类型选择。
*   **预期输出与解读：**
    *   核心期刊列表及其在网络中的位置。
    *   期刊关系网络图（共被引或耦合），展示期刊间的知识流向和主题关联。
    *   识别出的主要学科集群和跨学科影响力期刊。
*   **在常规基线数据路径/增强数据路径中的应用：**
    *   常规基线数据路径：评估各工具在期刊名称标准化、期刊关系网络构建等基础功能方面的能力。
    *   增强数据路径：在高质量数据基础上，比较不同工具在期刊网络分析、学科交叉识别、可视化效果等方面的差异，探索多工具协同应用的可能性。

## 4. 模块四：常规研究方向分析 (基于前述模块组合) (6.4)

*   **分析目标：** 综合运用上述基础模块（如模块二的作者/机构合作分析、模块三的关键词共现分析、文献共被引分析等），识别和描绘领域内已有的、相对成熟的常规研究方向。这要求整合来自不同分析视角的结果，形成对领域结构的整体认知。
*   **核心方法/算法简述：**
    *   对模块1-3的输出结果（如核心作者群、主要合作网络、核心主题簇、知识基础簇）进行**综合解读和交叉验证**。
    *   **三角互证法 (Triangulation):** 比较来自不同数据单元（作者、关键词、文献）的分析结果，寻找一致的模式和结构。例如，某个作者群是否主要聚焦于某个特定的关键词主题簇，并且其研究是否主要建立在某个特定的文献共被引簇之上。
    *   **图谱叠加与融合：** 在可视化层面，尝试将不同网络（如关键词网络与作者网络，或关键词网络与文献共被引网络）进行叠加或关联展示，以揭示更深层次的联系（例如，VOSviewer允许将一个网络的聚类结果映射到另一个网络上作为节点的属性）。
    *   **多维标度分析 (MDS) 或对应分析 (Correspondence Analysis):** 可用于探索不同实体（如作者、关键词、期刊）在低维空间中的相对位置和关联性。
    *   最终依赖**领域专家的知识和经验**对机器生成的模式进行归纳、提炼和命名，形成对常规研究方向的描述。
*   **所需输入数据：**
    *   模块1-3的输出结果（表格、网络文件、可视化图像等），均基于**增强数据集 (`enhanced_data`)**，该数据集存放于 `data/enhanced/` 目录下（具体文件路径详见 `01_Data_Foundation_and_Preparation.md`）。
*   **关键参数说明：** 主要依赖前序模块的参数设置。此模块更侧重于结果的整合与解释，而非新的参数化计算。
*   **预期输出与解读：**
    *   对领域内主要常规研究方向的**定性描述报告**，每个方向应包含：
        *   核心主题/问题。
        *   代表性学者、研究团队和机构。
        *   关键理论基础和经典文献。
        *   常用的研究方法。
        *   当前的主要进展和待解决的问题。
    *   综合性的知识图谱，可能通过组合或注释现有图谱生成，清晰标示出各个常规研究方向及其相互关系。
    *   各研究方向的文献集合、特征词列表。
*   **在常规基线数据路径/增强数据路径中的应用：**
    *   常规基线数据路径：评估各工具在基础分析结果整合、多维度数据关联等基础功能方面的能力。
    *   增强数据路径：在高质量数据基础上，比较不同工具在综合分析、结果可视化、交互功能等方面的差异，探索多工具协同应用的可能性。 
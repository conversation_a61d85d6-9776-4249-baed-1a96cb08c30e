# 01_data_enhancement_complete.R
# 完整的数据增强流程
# 包含多源数据采集、字段标准化、API补全和质量验证
# 基于参考文件的API补全框架进行增强

# 设置工作环境
options(repos = c(CRAN = "https://cloud.r-project.org"))

# 加载必要的包
required_packages <- c(
  "here", "dplyr", "stringr", "data.table", "bibliometrix",
  "httr", "jsonlite", "xml2", "rvest", "purrr", "future", "future.apply",
  "progress", "DBI", "RSQLite", "writexl", "stringdist", "readxl",
  "countrycode", "openxlsx", "stringi", "text2vec", "Matrix"
)

for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保bibliometrix包被正确加载
if (!requireNamespace("bibliometrix", quietly = TRUE)) {
  install.packages("bibliometrix")
  library(bibliometrix)
}

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

# --- 配置管理 ---
config <- list(
  # API配置
  use_crossref_api = TRUE,    # 是否使用Crossref API
  use_openalex_api = TRUE,    # 是否使用OpenAlex API
  api_timeout = 30,           # API请求超时（秒）
  max_retries = 3,            # API请求最大重试次数
  batch_size = 25,            # API批量请求大小
  throttle_delay = 0.5,       # API请求间隔（秒）
  cache_api_results = TRUE,   # 是否缓存API结果
  
  # 数据处理配置
  title_similarity_threshold = 0.95,  # 标题相似度阈值
  author_similarity_threshold = 0.90, # 作者相似度阈值
  abstract_similarity_threshold = 0.85, # 摘要相似度阈值
  
  # 输出配置
  save_intermediate = TRUE,   # 是否保存中间结果
  generate_reports = TRUE     # 是否生成详细报告
)

# --- 路径设置 ---
paths <- list(
  input = here("data_repository", "02_enhanced_dataset"),  # 修正：从enhanced_dataset读取
  output = here("data_repository", "02_enhanced_dataset"),
  cache = here("data_repository", "03_api_cache"),
  reports = here("data_repository", "04_enhancement_reports")
)

# 创建必要的目录
for (path in paths) {
  if (!dir.exists(path)) {
    dir.create(path, recursive = TRUE)
    log_message(sprintf("创建目录: %s", path))
  }
}

# --- API缓存数据库设置 ---
cache_db_file <- file.path(paths$cache, "api_cache_v1.sqlite")
cache_con <- dbConnect(SQLite(), cache_db_file)

# 创建API缓存表
if (!dbExistsTable(cache_con, "crossref_cache")) {
  dbExecute(cache_con, "
    CREATE TABLE crossref_cache (
      query TEXT PRIMARY KEY,
      result TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  ")
  log_message("创建Crossref API缓存表")
}

if (!dbExistsTable(cache_con, "openalex_cache")) {
  dbExecute(cache_con, "
    CREATE TABLE openalex_cache (
      query TEXT PRIMARY KEY,
      result TEXT,
      timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  ")
  log_message("创建OpenAlex API缓存表")
}

# --- API函数定义 ---

# 安全的Crossref API查询函数
safe_crossref_query <- function(identifier, identifier_type = "doi", max_attempts = 3) {
  # 检查缓存
  cache_query <- paste0(identifier_type, ":", identifier)
  cached_result <- dbGetQuery(cache_con, 
    "SELECT result FROM crossref_cache WHERE query = ?", 
    list(cache_query))
  
  if (nrow(cached_result) > 0) {
    log_message(sprintf("从缓存获取Crossref数据: %s", identifier))
    return(list(
      success = TRUE,
      data = fromJSON(cached_result$result[1]),
      source = "cache"
    ))
  }
  
  # 执行API查询
  for (attempt in 1:max_attempts) {
    tryCatch({
      if (identifier_type == "doi") {
        result <- cr_works(dois = identifier, .progress = FALSE)
      } else if (identifier_type == "wos") {
        # 对于WOS ID，需要先转换为DOI或使用其他方法
        result <- NULL
        log_message(sprintf("WOS ID查询暂不支持: %s", identifier), "warning")
      }
      
      if (!is.null(result) && length(result$data) > 0) {
        # 缓存结果
        dbExecute(cache_con, 
          "INSERT OR REPLACE INTO crossref_cache (query, result) VALUES (?, ?)",
          list(cache_query, toJSON(result$data[[1]])))
        
        log_message(sprintf("Crossref API查询成功: %s", identifier))
        return(list(
          success = TRUE,
          data = result$data[[1]],
          source = "api"
        ))
      }
    }, error = function(e) {
      log_message(sprintf("Crossref API查询失败 (尝试 %d/%d): %s", 
                         attempt, max_attempts, e$message), "warning")
      if (attempt < max_attempts) {
        Sys.sleep(config$throttle_delay * attempt)  # 递增延迟
      }
    })
  }
  
  return(list(success = FALSE, data = NULL, source = "failed"))
}

# 简化的OpenAlex API查询函数
safe_openalex_query <- function(doi, max_attempts = 3) {
  # 检查缓存
  cache_query <- paste0("doi:", doi)
  cached_result <- dbGetQuery(cache_con, 
    "SELECT result FROM openalex_cache WHERE query = ?", 
    list(cache_query))
  
  if (nrow(cached_result) > 0) {
    log_message(sprintf("从缓存获取OpenAlex数据: %s", doi))
    return(list(
      success = TRUE,
      data = fromJSON(cached_result$result[1]),
      source = "cache"
    ))
  }
  
  # 构建OpenAlex API URL
  openalex_url <- sprintf("https://api.openalex.org/works/doi:%s", doi)
  
  # 执行API查询
  for (attempt in 1:max_attempts) {
    tryCatch({
      response <- GET(openalex_url, 
                     timeout(config$api_timeout),
                     add_headers("User-Agent" = "bibliometric-analysis/1.0"))
      
      if (status_code(response) == 200) {
        result <- fromJSON(rawToChar(response$content))
        
        # 缓存结果
        dbExecute(cache_con, 
          "INSERT OR REPLACE INTO openalex_cache (query, result) VALUES (?, ?)",
          list(cache_query, toJSON(result)))
        
        log_message(sprintf("OpenAlex API查询成功: %s", doi))
        return(list(
          success = TRUE,
          data = result,
          source = "api"
        ))
      } else {
        log_message(sprintf("OpenAlex API返回错误状态码: %d", status_code(response)), "warning")
      }
    }, error = function(e) {
      log_message(sprintf("OpenAlex API查询失败 (尝试 %d/%d): %s", 
                         attempt, max_attempts, e$message), "warning")
      if (attempt < max_attempts) {
        Sys.sleep(config$throttle_delay * attempt)
      }
    })
  }
  
  return(list(success = FALSE, data = NULL, source = "failed"))
}

# 批量API数据获取函数
fetch_enhanced_metadata <- function(dois, wos_ids, max_attempts = 3) {
  if (length(dois) == 0 && length(wos_ids) == 0) {
    return(list(crossref_data = list(), openalex_data = list()))
  }
  
  log_message(sprintf("开始批量API数据获取: %d个DOI, %d个WOS ID", length(dois), length(wos_ids)))
  
  # 准备查询列表
  query_ids <- unique(c(dois, wos_ids))
  query_ids <- query_ids[!is.na(query_ids)]
  
  if (length(query_ids) == 0) {
    log_message("无有效标识符可查询", "warning")
    return(list(crossref_data = list(), openalex_data = list()))
  }
  
  # 分批处理
  total_batches <- ceiling(length(query_ids) / config$batch_size)
  pb <- progress_bar$new(
    format = "[:bar] :percent 已完成 (批次: :current/:total, 已用时: :elapsed, 剩余: :eta)",
    total = total_batches,
    clear = FALSE,
    width = 80
  )
  
  # 结果存储
  crossref_results <- list()
  openalex_results <- list()
  api_diagnostics <- list()
  
  # 分批处理标识符
  for (i in seq(1, length(query_ids), by = config$batch_size)) {
    batch_ids <- query_ids[i:min(i+config$batch_size-1, length(query_ids))]
    batch_number <- ceiling(i/config$batch_size)
    
    log_message(sprintf("处理批次 %d/%d (%d个标识符)", batch_number, total_batches, length(batch_ids)))
    
    # 并行获取Crossref和OpenAlex数据
    for (id in batch_ids) {
      # 检查是DOI还是WOS号
      is_doi <- grepl("^10\\.", id, ignore.case = TRUE)
      
      # 查询Crossref
      if (config$use_crossref_api) {
        cr_result <- safe_crossref_query(id, 
                                        identifier_type = ifelse(is_doi, "doi", "wos"), 
                                        max_attempts = max_attempts)
        if (cr_result$success) {
          crossref_results[[id]] <- cr_result$data
        }
      }
      
      # 查询OpenAlex (仅支持DOI)
      if (config$use_openalex_api && is_doi) {
        oa_result <- safe_openalex_query(id, max_attempts = max_attempts)
        if (oa_result$success) {
          openalex_results[[id]] <- oa_result$data
        }
      }
      
      # 记录诊断信息
      api_diagnostics[[id]] <- list(
        id = id,
        identifier_type = ifelse(is_doi, "doi", "wos"),
        crossref_success = !is.null(crossref_results[[id]]),
        openalex_success = !is.null(openalex_results[[id]])
      )
      
      # 防止API限制
      Sys.sleep(config$throttle_delay)
    }
    
    # 更新进度条
    pb$tick()
  }
  
  # 汇总诊断信息
  api_summary <- tibble(
    id = names(api_diagnostics),
    identifier_type = map_chr(api_diagnostics, ~.x$identifier_type),
    crossref_success = map_lgl(api_diagnostics, ~.x$crossref_success),
    openalex_success = map_lgl(api_diagnostics, ~.x$openalex_success)
  )
  
  log_message(sprintf("API数据获取完成: Crossref成功%d个, OpenAlex成功%d个", 
                     sum(api_summary$crossref_success), sum(api_summary$openalex_success)))
  
  return(list(
    crossref_data = crossref_results,
    openalex_data = openalex_results,
    diagnostics = api_summary
  ))
}

# --- 字段标准化函数 ---

# 作者信息标准化
standardize_authors <- function(authors_string) {
  if (is.na(authors_string) || authors_string == "") {
    return(NA_character_)
  }
  
  # 分割作者
  authors <- str_split(authors_string, ";")[[1]]
  authors <- trimws(authors)
  
  # 标准化每个作者
  standardized_authors <- map_chr(authors, function(author) {
    # 移除多余的空格
    author <- str_replace_all(author, "\\s+", " ")
    
    # 处理常见的作者格式问题
    author <- str_replace_all(author, "\\bDr\\.\\s*", "")
    author <- str_replace_all(author, "\\bProf\\.\\s*", "")
    author <- str_replace_all(author, "\\bProfessor\\s*", "")
    
    return(trimws(author))
  })
  
  return(paste(standardized_authors, collapse = "; "))
}

# 机构信息标准化
standardize_institutions <- function(institutions_string) {
  if (is.na(institutions_string) || institutions_string == "") {
    return(NA_character_)
  }
  
  # 分割机构
  institutions <- str_split(institutions_string, ";")[[1]]
  institutions <- trimws(institutions)
  
  # 标准化每个机构
  standardized_institutions <- map_chr(institutions, function(institution) {
    # 移除多余的空格
    institution <- str_replace_all(institution, "\\s+", " ")
    
    # 处理常见的机构缩写
    institution <- str_replace_all(institution, "\\bUniv\\b", "University")
    institution <- str_replace_all(institution, "\\bInst\\b", "Institute")
    institution <- str_replace_all(institution, "\\bHosp\\b", "Hospital")
    institution <- str_replace_all(institution, "\\bAcad\\b", "Academy")
    
    return(trimws(institution))
  })
  
  return(paste(standardized_institutions, collapse = "; "))
}

# 关键词标准化
standardize_keywords <- function(keywords_string) {
  if (is.na(keywords_string) || keywords_string == "") {
    return(NA_character_)
  }
  
  # 分割关键词
  keywords <- str_split(keywords_string, ";")[[1]]
  keywords <- trimws(keywords)
  
  # 标准化每个关键词
  standardized_keywords <- map_chr(keywords, function(keyword) {
    # 转换为小写
    keyword <- tolower(keyword)
    
    # 移除多余的空格
    keyword <- str_replace_all(keyword, "\\s+", " ")
    
    # 首字母大写
    keyword <- str_to_title(keyword)
    
    return(trimws(keyword))
  })
  
  return(paste(standardized_keywords, collapse = "; "))
}

# --- 数据质量评估函数 ---

# 计算字段完整性
calculate_field_completeness <- function(data) {
  field_stats <- map_dfr(colnames(data), function(col) {
    non_na_count <- sum(!is.na(data[[col]]) & data[[col]] != "")
    completeness_rate <- non_na_count / nrow(data)
    
    tibble(
      field = col,
      total_records = nrow(data),
      non_na_records = non_na_count,
      completeness_rate = completeness_rate,
      completeness_percentage = sprintf("%.1f%%", 100 * completeness_rate)
    )
  })
  
  return(field_stats %>% arrange(desc(completeness_rate)))
}

# 检测数据异常
detect_data_anomalies <- function(data) {
  anomalies <- list()
  
  # 检测年份异常
  if ("PY" %in% colnames(data)) {
    year_anomalies <- data %>%
      filter(!is.na(PY) & (PY < 1900 | PY > as.numeric(format(Sys.Date(), "%Y")) + 1)) %>%
      select(UT, TI, PY) %>%
      mutate(anomaly_type = "异常年份")
    
    if (nrow(year_anomalies) > 0) {
      anomalies$year_anomalies <- year_anomalies
    }
  }
  
  # 检测标题长度异常
  if ("TI" %in% colnames(data)) {
    title_lengths <- nchar(data$TI)
    title_anomalies <- data %>%
      filter(!is.na(TI) & (nchar(TI) < 10 | nchar(TI) > 500)) %>%
      select(UT, TI) %>%
      mutate(
        title_length = nchar(TI),
        anomaly_type = ifelse(nchar(TI) < 10, "标题过短", "标题过长")
      )
    
    if (nrow(title_anomalies) > 0) {
      anomalies$title_anomalies <- title_anomalies
    }
  }
  
  # 检测作者数量异常
  if ("AU" %in% colnames(data)) {
    author_counts <- str_count(data$AU, ";") + 1
    author_anomalies <- data %>%
      filter(!is.na(AU) & (str_count(AU, ";") + 1 > 50)) %>%
      select(UT, TI, AU) %>%
      mutate(
        author_count = str_count(AU, ";") + 1,
        anomaly_type = "作者数量异常"
      )
    
    if (nrow(author_anomalies) > 0) {
      anomalies$author_anomalies <- author_anomalies
    }
  }
  
  return(anomalies)
}

# --- 数据增强主函数 ---
enhance_data <- function(input_file) {
  log_message("开始数据增强流程")
  
  # 1. 加载原始数据
  log_message("步骤1: 加载原始数据")
  if (file.exists(input_file)) {
    M <- readRDS(input_file)
    log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
  } else {
    stop(sprintf("输入文件不存在: %s", input_file))
  }
  
  # 2. 数据预处理和字段标准化
  log_message("步骤2: 数据预处理和字段标准化")
  
  # 清理DOI字段
  if ("DI" %in% colnames(M)) {
    M$doi_cleaned <- tolower(trimws(M$DI))
  } else if ("DOI" %in% colnames(M)) {
    M$doi_cleaned <- tolower(trimws(M$DOI))
  } else {
    M$doi_cleaned <- NA_character_
  }
  
  # 清理WOS UT字段
  if ("UT" %in% colnames(M)) {
    M$UT_cleaned <- trimws(M$UT)
  } else {
    M$UT_cleaned <- NA_character_
  }
  
  # 标准化作者信息
  if ("AU" %in% colnames(M)) {
    M$AU_standardized <- map_chr(M$AU, standardize_authors)
    log_message("作者信息标准化完成")
  }
  
  # 标准化机构信息
  if ("C1" %in% colnames(M)) {
    M$C1_standardized <- map_chr(M$C1, standardize_institutions)
    log_message("机构信息标准化完成")
  }
  
  # 标准化关键词
  if ("DE" %in% colnames(M)) {
    M$DE_standardized <- map_chr(M$DE, standardize_keywords)
    log_message("关键词标准化完成")
  }
  
  # 统计标识符覆盖率
  doi_coverage <- sum(!is.na(M$doi_cleaned) & M$doi_cleaned != "")
  ut_coverage <- sum(!is.na(M$UT_cleaned) & M$UT_cleaned != "")
  
  log_message(sprintf("DOI覆盖率: %d/%d (%.1f%%)", doi_coverage, nrow(M), 100*doi_coverage/nrow(M)))
  log_message(sprintf("WOS UT覆盖率: %d/%d (%.1f%%)", ut_coverage, nrow(M), 100*ut_coverage/nrow(M)))
  
  # 3. 数据质量评估
  log_message("步骤3: 数据质量评估")
  
  # 计算字段完整性
  field_completeness <- calculate_field_completeness(M)
  
  # 检测数据异常
  data_anomalies <- detect_data_anomalies(M)
  
  # 保存质量评估报告
  if (config$generate_reports) {
    quality_report_file <- file.path(paths$reports, "data_quality_report.xlsx")
    
    # 准备报告数据
    report_data <- list(
      "字段完整性" = field_completeness,
      "数据统计" = tibble(
        指标 = c("总记录数", "DOI覆盖率", "WOS UT覆盖率", "有标题记录", "有作者记录"),
        数值 = c(
          nrow(M),
          sprintf("%.1f%%", 100*doi_coverage/nrow(M)),
          sprintf("%.1f%%", 100*ut_coverage/nrow(M)),
          sum(!is.na(M$TI) & M$TI != ""),
          sum(!is.na(M$AU) & M$AU != "")
        )
      )
    )
    
    # 添加异常数据
    for (anomaly_name in names(data_anomalies)) {
      if (nrow(data_anomalies[[anomaly_name]]) > 0) {
        report_data[[anomaly_name]] <- data_anomalies[[anomaly_name]]
      }
    }
    
    write_xlsx(report_data, quality_report_file)
    log_message(sprintf("数据质量报告已保存: %s", quality_report_file))
  }
  
  # 4. API数据补全
  log_message("步骤4: API数据补全")
  
  # 获取有效的DOI和WOS ID
  valid_dois <- unique(na.omit(M$doi_cleaned[M$doi_cleaned != ""]))
  valid_wos_ids <- unique(na.omit(M$UT_cleaned[M$UT_cleaned != ""]))
  
  # 执行API数据获取
  api_results <- fetch_enhanced_metadata(valid_dois, valid_wos_ids, max_attempts = config$max_retries)
  
  # 5. 数据合并和增强
  log_message("步骤5: 数据合并和增强")
  
  # 处理Crossref数据
  if (length(api_results$crossref_data) > 0) {
    crossref_enhanced <- map_dfr(names(api_results$crossref_data), function(id) {
      data <- api_results$crossref_data[[id]]
      tibble(
        doi = id,
        title_cr = data$title[1] %||% NA_character_,
        authors_cr = paste(data$author$given %||% "", data$author$family %||% "", collapse = "; "),
        journal_cr = data$`container-title`[1] %||% NA_character_,
        year_cr = data$published$`date-parts`[1][1] %||% NA_integer_,
        volume_cr = data$volume %||% NA_character_,
        issue_cr = data$issue %||% NA_character_,
        pages_cr = data$page %||% NA_character_,
        publisher_cr = data$publisher %||% NA_character_,
        type_cr = data$type %||% NA_character_,
        abstract_cr = data$abstract %||% NA_character_
      )
    })
  } else {
    crossref_enhanced <- tibble()
  }
  
  # 处理OpenAlex数据
  if (length(api_results$openalex_data) > 0) {
    openalex_enhanced <- map_dfr(names(api_results$openalex_data), function(doi) {
      data <- api_results$openalex_data[[doi]]
      tibble(
        doi = doi,
        openalex_id = data$id %||% NA_character_,
        title_oa = data$title %||% NA_character_,
        authors_oa = paste(map_chr(data$authorships %||% list(), ~.x$author$display_name), collapse = "; "),
        journal_oa = data$primary_location$source$display_name %||% NA_character_,
        year_oa = data$publication_year %||% NA_integer_,
        volume_oa = data$primary_location$source$volume %||% NA_character_,
        issue_oa = data$primary_location$source$issue %||% NA_character_,
        pages_oa = data$primary_location$pages %||% NA_character_,
        cited_by_count_oa = data$cited_by_count %||% NA_integer_,
        concepts_oa = paste(map_chr(data$concepts %||% list(), ~.x$display_name), collapse = "; "),
        oa_status = data$open_access$oa_status %||% NA_character_,
        institutions_oa = paste(map_chr(data$authorships %||% list(), 
                                      ~paste(map_chr(.x$institutions %||% list(), ~.x$display_name), collapse = ", ")), 
                              collapse = "; "),
        type_oa = data$type %||% NA_character_
      )
    })
  } else {
    openalex_enhanced <- tibble()
  }
  
  # 6. 合并增强数据
  log_message("步骤6: 合并增强数据")
  
  # 与原始数据合并
  M_enhanced <- M %>%
    left_join(crossref_enhanced, by = c("doi_cleaned" = "doi")) %>%
    left_join(openalex_enhanced, by = c("doi_cleaned" = "doi"))
  
  log_message(sprintf("数据增强完成: %d行, %d列", nrow(M_enhanced), ncol(M_enhanced)))
  
  # 7. 质量评估和报告
  log_message("步骤7: 质量评估和报告")
  
  # 计算增强效果
  enhancement_stats <- list(
    original_records = nrow(M),
    enhanced_records = nrow(M_enhanced),
    crossref_enhanced = nrow(crossref_enhanced),
    openalex_enhanced = nrow(openalex_enhanced),
    doi_coverage = doi_coverage,
    ut_coverage = ut_coverage,
    api_success_rate = mean(api_results$diagnostics$crossref_success | api_results$diagnostics$openalex_success)
  )
  
  # 生成增强报告
  if (config$generate_reports) {
    report_file <- file.path(paths$reports, "enhancement_report.txt")
    sink(report_file)
    cat("=== 数据增强报告 ===\n")
    cat("生成时间:", format(Sys.time()), "\n\n")
    cat("原始数据统计:\n")
    cat("- 记录数:", enhancement_stats$original_records, "\n")
    cat("- DOI覆盖率:", sprintf("%.1f%%", 100*enhancement_stats$doi_coverage/enhancement_stats$original_records), "\n")
    cat("- WOS UT覆盖率:", sprintf("%.1f%%", 100*enhancement_stats$ut_coverage/enhancement_stats$original_records), "\n\n")
    cat("API增强效果:\n")
    cat("- Crossref增强记录:", enhancement_stats$crossref_enhanced, "\n")
    cat("- OpenAlex增强记录:", enhancement_stats$openalex_enhanced, "\n")
    cat("- API成功率:", sprintf("%.1f%%", 100*enhancement_stats$api_success_rate), "\n\n")
    cat("最终数据统计:\n")
    cat("- 增强后记录数:", enhancement_stats$enhanced_records, "\n")
    cat("- 新增字段数:", ncol(M_enhanced) - ncol(M), "\n")
    cat("- 新增字段列表:\n")
    new_fields <- setdiff(colnames(M_enhanced), colnames(M))
    for (field in new_fields) {
      cat("  *", field, "\n")
    }
    sink()
    
    log_message(sprintf("增强报告已保存: %s", report_file))
  }
  
  # 8. 保存增强数据
  log_message("步骤8: 保存增强数据")
  
  output_file <- file.path(paths$output, "enhanced_data_optimized.rds")
  saveRDS(M_enhanced, output_file)
  log_message(sprintf("增强数据已保存: %s", output_file))
  
  # 保存API诊断信息
  if (nrow(api_results$diagnostics) > 0) {
    diagnostics_file <- file.path(paths$reports, "api_diagnostics.csv")
    write.csv(api_results$diagnostics, diagnostics_file, row.names = FALSE)
    log_message(sprintf("API诊断信息已保存: %s", diagnostics_file))
  }
  
  # 关闭数据库连接
  dbDisconnect(cache_con)
  
  log_message("数据增强流程完成")
  return(M_enhanced)
}

# --- 主执行流程 ---
main <- function() {
  log_message("启动完整数据增强流程")
  
  # 查找输入文件
  input_file <- file.path(paths$input, "enhanced_data_initial.rds")
  
  if (!file.exists(input_file)) {
    stop(sprintf("输入文件不存在: %s", input_file))
  }
  
  log_message(sprintf("使用输入文件: %s", input_file))
  
  # 执行数据增强
  enhanced_data <- enhance_data(input_file)
  
  log_message("数据增强流程完成")
  return(enhanced_data)
}

# 如果直接运行此脚本
if (!interactive()) {
  enhanced_data <- main()
} 
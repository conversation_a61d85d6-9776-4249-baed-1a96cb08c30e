# 大规模验证系统
# 使用实际数据验证优化效果

library(httr)
library(jsonlite)
library(stringdist)

# 加载所有系统
source("doi_completion_core.R")
source("doi_completion_final_optimized.R")
source("pubmed_mesh_optimized.R")
source("optimized_thresholds_config.R")
source("data_analysis_and_training.R")

cat("=== 大规模验证系统 ===\n")
cat("🎯 目标: 验证优化后的DOI补全和MeSH分类效果\n")
cat("📊 策略: 实际数据测试 + 性能对比分析\n\n")

# === 大规模DOI-MeSH联合补全 ===
large_scale_doi_mesh_completion <- function(data, sample_size = 100, use_optimized = TRUE) {
  cat(sprintf("🚀 开始大规模DOI-MeSH联合补全 (样本: %d)\n", sample_size))
  
  if (use_optimized) {
    cat("✅ 使用优化阈值配置\n")
    initialize_optimized_system()
  } else {
    cat("📋 使用原始阈值配置\n")
  }
  
  # 随机采样
  if (nrow(data) > sample_size) {
    sample_indices <- sample(nrow(data), sample_size)
    test_data <- data[sample_indices, ]
  } else {
    test_data <- data
  }
  
  cat(sprintf("📊 测试数据: %d 条记录\n", nrow(test_data)))
  
  # 初始化结果
  results <- data.frame(
    序号 = 1:nrow(test_data),
    UT = test_data$UT,
    原始标题 = substr(test_data$TI, 1, 60),
    原始年份 = test_data$PY,
    原始期刊 = substr(test_data$SO, 1, 40),
    原始DOI = if("DI" %in% colnames(test_data)) test_data$DI else "",
    
    # DOI补全结果
    补全DOI = "",
    补全来源 = "",
    补全质量 = "",
    
    # MeSH信息
    MeSH类型 = "",
    MeSH类型中文 = "",
    MeSH主题词 = "",
    MeSH质量评分 = 0,
    证据级别 = "",
    
    # 性能指标
    处理状态 = "待处理",
    处理时间 = 0,
    
    stringsAsFactors = FALSE
  )
  
  # 统计变量
  start_time <- Sys.time()
  success_count <- 0
  mesh_count <- 0
  high_quality_mesh_count <- 0
  
  engine_stats <- list(
    crossref = 0,
    openalex = 0,
    pubmed = 0
  )
  
  # 逐个处理记录
  for (i in 1:nrow(test_data)) {
    record_start <- Sys.time()
    
    cat(sprintf("\n--- 记录 %d/%d ---\n", i, nrow(test_data)))
    cat(sprintf("标题: %s\n", substr(test_data$TI[i], 1, 50)))
    
    # 智能DOI-MeSH联合补全
    existing_doi <- if("DI" %in% colnames(test_data) && !is.na(test_data$DI[i])) test_data$DI[i] else NULL
    
    completion_result <- smart_doi_mesh_completion(
      title = test_data$TI[i],
      authors = if("AU" %in% colnames(test_data)) test_data$AU[i] else "",
      year = test_data$PY[i],
      journal = test_data$SO[i],
      existing_doi = existing_doi
    )
    
    record_time <- as.numeric(difftime(Sys.time(), record_start, units = "secs"))
    results$处理时间[i] <- round(record_time, 2)
    
    if (completion_result$success) {
      success_count <- success_count + 1
      results$补全DOI[i] <- completion_result$completed_doi
      results$补全来源[i] <- completion_result$completion_source
      results$处理状态[i] <- "成功"
      
      # 统计引擎使用
      if (completion_result$completion_source %in% names(engine_stats)) {
        engine_stats[[completion_result$completion_source]] <- engine_stats[[completion_result$completion_source]] + 1
      }
      
      # MeSH信息处理
      if (!is.null(completion_result$mesh_info)) {
        mesh_count <- mesh_count + 1
        mesh_info <- completion_result$mesh_info
        
        if (!is.null(mesh_info$mesh_publication_types)) {
          results$MeSH类型[i] <- paste(mesh_info$mesh_publication_types, collapse = "; ")
        }
        
        if (!is.null(mesh_info$mesh_publication_types_cn)) {
          results$MeSH类型中文[i] <- paste(mesh_info$mesh_publication_types_cn, collapse = "; ")
        }
        
        if (!is.null(mesh_info$mesh_headings)) {
          results$MeSH主题词[i] <- paste(mesh_info$mesh_headings[1:min(5, length(mesh_info$mesh_headings))], collapse = "; ")
        }
        
        # MeSH质量评估
        if (exists("analyze_mesh_quality")) {
          quality_analysis <- analyze_mesh_quality(mesh_info)
          results$MeSH质量评分[i] <- quality_analysis$quality_score
          results$证据级别[i] <- quality_analysis$evidence_level
          
          if (quality_analysis$quality_score >= 7) {
            high_quality_mesh_count <- high_quality_mesh_count + 1
          }
        }
        
        results$补全质量[i] <- "含MeSH"
      } else {
        results$补全质量[i] <- "仅DOI"
      }
      
      cat(sprintf("✅ 成功: %s (来源: %s)\n", completion_result$completed_doi, completion_result$completion_source))
      if (!is.null(completion_result$mesh_info)) {
        cat(sprintf("📋 MeSH: %s\n", results$MeSH类型中文[i]))
      }
    } else {
      results$处理状态[i] <- "失败"
      cat("❌ 失败\n")
    }
    
    # 进度报告
    if (i %% 10 == 0) {
      current_success_rate <- 100 * success_count / i
      current_mesh_rate <- 100 * mesh_count / i
      cat(sprintf("📊 当前进度: %d/%d, 成功率: %.1f%%, MeSH率: %.1f%%\n", 
                  i, nrow(test_data), current_success_rate, current_mesh_rate))
    }
    
    # API调用间隔
    if (i < nrow(test_data)) {
      Sys.sleep(2)
    }
  }
  
  total_time <- as.numeric(difftime(Sys.time(), start_time, units = "mins"))
  
  # 生成详细报告
  success_rate <- 100 * success_count / nrow(test_data)
  mesh_rate <- 100 * mesh_count / nrow(test_data)
  high_quality_rate <- 100 * high_quality_mesh_count / nrow(test_data)
  
  cat(sprintf("\n=== 大规模验证结果 ===\n"))
  cat(sprintf("📊 处理记录: %d\n", nrow(test_data)))
  cat(sprintf("⏱️  总耗时: %.1f 分钟\n", total_time))
  cat(sprintf("🎯 DOI补全成功率: %.1f%% (%d/%d)\n", success_rate, success_count, nrow(test_data)))
  cat(sprintf("🏷️  MeSH提取成功率: %.1f%% (%d/%d)\n", mesh_rate, mesh_count, nrow(test_data)))
  cat(sprintf("⭐ 高质量MeSH率: %.1f%% (%d/%d)\n", high_quality_rate, high_quality_mesh_count, nrow(test_data)))
  
  cat(sprintf("\n📈 引擎使用统计:\n"))
  for (engine in names(engine_stats)) {
    if (engine_stats[[engine]] > 0) {
      engine_rate <- 100 * engine_stats[[engine]] / success_count
      cat(sprintf("  %s: %d次 (%.1f%%)\n", toupper(engine), engine_stats[[engine]], engine_rate))
    }
  }
  
  return(list(
    results = results,
    summary = list(
      total_records = nrow(test_data),
      success_count = success_count,
      success_rate = success_rate,
      mesh_count = mesh_count,
      mesh_rate = mesh_rate,
      high_quality_mesh_count = high_quality_mesh_count,
      high_quality_rate = high_quality_rate,
      engine_stats = engine_stats,
      total_time = total_time,
      avg_time_per_record = total_time * 60 / nrow(test_data)
    )
  ))
}

# === 性能对比测试 ===
performance_comparison_test <- function(data, sample_size = 50) {
  cat("\n🔬 性能对比测试 (优化前 vs 优化后)\n")
  
  # 测试原始配置
  cat("\n--- 测试原始配置 ---\n")
  original_results <- large_scale_doi_mesh_completion(data, sample_size, use_optimized = FALSE)
  
  # 等待间隔
  cat("\n⏳ 等待5分钟后测试优化配置...\n")
  Sys.sleep(300)
  
  # 测试优化配置
  cat("\n--- 测试优化配置 ---\n")
  optimized_results <- large_scale_doi_mesh_completion(data, sample_size, use_optimized = TRUE)
  
  # 对比分析
  cat(sprintf("\n📊 性能对比分析:\n"))
  
  original_summary <- original_results$summary
  optimized_summary <- optimized_results$summary
  
  # DOI补全对比
  doi_improvement <- optimized_summary$success_rate - original_summary$success_rate
  cat(sprintf("🎯 DOI补全成功率: %.1f%% → %.1f%% (%+.1f%%)\n", 
              original_summary$success_rate, optimized_summary$success_rate, doi_improvement))
  
  # MeSH提取对比
  mesh_improvement <- optimized_summary$mesh_rate - original_summary$mesh_rate
  cat(sprintf("🏷️  MeSH提取成功率: %.1f%% → %.1f%% (%+.1f%%)\n", 
              original_summary$mesh_rate, optimized_summary$mesh_rate, mesh_improvement))
  
  # 高质量MeSH对比
  quality_improvement <- optimized_summary$high_quality_rate - original_summary$high_quality_rate
  cat(sprintf("⭐ 高质量MeSH率: %.1f%% → %.1f%% (%+.1f%%)\n", 
              original_summary$high_quality_rate, optimized_summary$high_quality_rate, quality_improvement))
  
  # 效率对比
  time_change <- optimized_summary$avg_time_per_record - original_summary$avg_time_per_record
  cat(sprintf("⏱️  平均处理时间: %.1f秒 → %.1f秒 (%+.1f秒)\n", 
              original_summary$avg_time_per_record, optimized_summary$avg_time_per_record, time_change))
  
  # 总体评估
  cat(sprintf("\n🏆 总体评估:\n"))
  if (doi_improvement > 0) {
    cat(sprintf("✅ DOI补全性能提升 %.1f个百分点\n", doi_improvement))
  } else {
    cat(sprintf("⚠️  DOI补全性能下降 %.1f个百分点\n", abs(doi_improvement)))
  }
  
  if (mesh_improvement > 0) {
    cat(sprintf("✅ MeSH提取性能提升 %.1f个百分点\n", mesh_improvement))
  } else {
    cat(sprintf("⚠️  MeSH提取性能下降 %.1f个百分点\n", abs(mesh_improvement)))
  }
  
  return(list(
    original = original_results,
    optimized = optimized_results,
    improvements = list(
      doi = doi_improvement,
      mesh = mesh_improvement,
      quality = quality_improvement,
      time = time_change
    )
  ))
}

# === 保存验证结果 ===
save_validation_results <- function(results, filename_prefix = "VALIDATION") {
  timestamp <- format(Sys.time(), "%Y%m%d_%H%M%S")
  
  # 保存详细结果
  results_file <- sprintf("data_repository/04_enhancement_reports/%s_RESULTS_%s.csv", 
                         filename_prefix, timestamp)
  write.csv(results$results, results_file, row.names = FALSE)
  
  # 保存摘要
  summary_file <- sprintf("data_repository/04_enhancement_reports/%s_SUMMARY_%s.txt", 
                         filename_prefix, timestamp)
  
  sink(summary_file)
  cat("=== 大规模验证摘要 ===\n")
  cat(sprintf("时间: %s\n", Sys.time()))
  cat(sprintf("样本数: %d\n", results$summary$total_records))
  cat(sprintf("DOI补全成功率: %.1f%%\n", results$summary$success_rate))
  cat(sprintf("MeSH提取成功率: %.1f%%\n", results$summary$mesh_rate))
  cat(sprintf("高质量MeSH率: %.1f%%\n", results$summary$high_quality_rate))
  cat(sprintf("总耗时: %.1f分钟\n", results$summary$total_time))
  sink()
  
  cat(sprintf("✅ 验证结果已保存:\n"))
  cat(sprintf("  详细结果: %s\n", results_file))
  cat(sprintf("  摘要报告: %s\n", summary_file))
}

cat("✅ 大规模验证系统已加载\n")
cat("📋 主要函数:\n")
cat("  - large_scale_doi_mesh_completion()  : 大规模DOI-MeSH补全\n")
cat("  - performance_comparison_test()      : 性能对比测试\n")
cat("  - save_validation_results()          : 保存验证结果\n")

cat("\n💡 使用示例:\n")
cat("  data <- readRDS('path/to/data.rds')\n")
cat("  results <- large_scale_doi_mesh_completion(data, 100)\n")
cat("  save_validation_results(results)\n")

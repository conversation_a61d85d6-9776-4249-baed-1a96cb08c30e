# DOI补全阈值训练系统
# 基于现有4000+文献记录的DOI进行机器学习训练
# 版本: v1.0

library(httr)
library(jsonlite)
library(stringdist)

cat("===============================================================================\n")
cat("🎯 DOI补全阈值训练系统 - v1.0\n")
cat("📊 基于现有4000+文献记录进行阈值优化训练\n")
cat("🧠 机器学习方法: 网格搜索 + 交叉验证\n")
cat("===============================================================================\n\n")

# 加载核心函数
source("C:/Users/<USER>/Desktop/bibliometric-analysis/DOI_COMPLETION_CLEAN/doi_completion_verified.R")

# ===============================================================================
# 训练数据准备函数
# ===============================================================================

prepare_training_data <- function(data, sample_size = 100) {
  cat("📊 准备训练数据...\n")
  
  # 筛选已有DOI的记录
  has_doi_indices <- which(!is.na(data$DI) & data$DI != "" & nchar(as.character(data$DI)) > 0)
  cat(sprintf("✅ 找到 %d 条已有DOI的记录\n", length(has_doi_indices)))
  
  if (length(has_doi_indices) < sample_size) {
    sample_size <- length(has_doi_indices)
    cat(sprintf("⚠️  调整样本大小为 %d\n", sample_size))
  }
  
  # 随机采样
  set.seed(42)  # 确保可重复性
  sample_indices <- sample(has_doi_indices, sample_size)
  
  training_data <- data[sample_indices, ]
  
  cat(sprintf("🎯 训练数据准备完成: %d 条记录\n", nrow(training_data)))
  cat(sprintf("📋 字段包括: TI(标题), SO(期刊), PY(年份), DI(已知DOI)\n\n"))
  
  return(training_data)
}

# ===============================================================================
# 阈值评估函数
# ===============================================================================

evaluate_thresholds <- function(training_data, thresholds, engine = "crossref", max_test = 20) {
  cat(sprintf("🔍 评估阈值配置 (引擎: %s, 测试记录: %d)...\n", engine, max_test))
  
  # 临时修改配置
  original_thresholds <- VERIFIED_CONFIG$thresholds[[engine]]
  VERIFIED_CONFIG$thresholds[[engine]] <<- thresholds
  
  correct_matches <- 0
  total_tests <- min(max_test, nrow(training_data))
  
  for (i in 1:total_tests) {
    record <- training_data[i, ]
    known_doi <- record$DI
    
    # 使用指定引擎搜索
    result <- switch(engine,
      "crossref" = search_crossref_verified(record$TI, "", record$PY, record$SO),
      "openalex" = search_openalex_verified(record$TI, "", record$PY, record$SO),
      "pubmed" = search_pubmed_verified(record$TI, "", record$PY, record$SO)
    )
    
    # 检查是否匹配已知DOI
    if (!is.null(result) && !is.null(result$doi)) {
      if (tolower(result$doi) == tolower(known_doi)) {
        correct_matches <- correct_matches + 1
      }
    }
    
    if (i %% 5 == 0) {
      cat(sprintf("  进度: %d/%d, 当前准确率: %.1f%%\n", 
                  i, total_tests, 100 * correct_matches / i))
    }
  }
  
  # 恢复原始配置
  VERIFIED_CONFIG$thresholds[[engine]] <<- original_thresholds
  
  accuracy <- correct_matches / total_tests
  cat(sprintf("✅ 评估完成: 准确率 %.1f%% (%d/%d)\n\n", 
              100 * accuracy, correct_matches, total_tests))
  
  return(list(
    accuracy = accuracy,
    correct_matches = correct_matches,
    total_tests = total_tests,
    thresholds = thresholds
  ))
}

# ===============================================================================
# 网格搜索训练函数
# ===============================================================================

grid_search_training <- function(training_data, engine = "crossref", max_test = 20) {
  cat(sprintf("🧠 开始网格搜索训练 (引擎: %s)\n", engine))
  cat("===============================================================================\n")
  
  # 定义搜索网格
  title_thresholds <- c(0.65, 0.70, 0.75, 0.80)
  journal_thresholds <- c(0.30, 0.35, 0.40, 0.45)
  year_thresholds <- c(0.40, 0.50, 0.60)
  final_thresholds <- c(0.55, 0.60, 0.65, 0.70)
  
  best_accuracy <- 0
  best_config <- NULL
  all_results <- list()
  
  total_combinations <- length(title_thresholds) * length(journal_thresholds) * 
                       length(year_thresholds) * length(final_thresholds)
  
  cat(sprintf("📊 总组合数: %d\n", total_combinations))
  cat(sprintf("⏱️  预计时间: %.1f 分钟\n\n", total_combinations * max_test * 3 / 60))
  
  combination_count <- 0
  
  for (title_th in title_thresholds) {
    for (journal_th in journal_thresholds) {
      for (year_th in year_thresholds) {
        for (final_th in final_thresholds) {
          combination_count <- combination_count + 1
          
          cat(sprintf("--- 组合 %d/%d ---\n", combination_count, total_combinations))
          cat(sprintf("阈值: title=%.2f, journal=%.2f, year=%.2f, final=%.2f\n", 
                      title_th, journal_th, year_th, final_th))
          
          test_thresholds <- list(
            title = title_th,
            journal = journal_th,
            year = year_th,
            final = final_th
          )
          
          result <- evaluate_thresholds(training_data, test_thresholds, engine, max_test)
          all_results[[combination_count]] <- result
          
          if (result$accuracy > best_accuracy) {
            best_accuracy <- result$accuracy
            best_config <- test_thresholds
            cat(sprintf("🎉 新的最佳配置! 准确率: %.1f%%\n", 100 * best_accuracy))
          }
          
          cat("\n")
        }
      }
    }
  }
  
  cat("===============================================================================\n")
  cat("🎉 网格搜索训练完成!\n")
  cat("===============================================================================\n")
  cat(sprintf("🏆 最佳准确率: %.1f%%\n", 100 * best_accuracy))
  cat("🎯 最佳阈值配置:\n")
  cat(sprintf("  标题相似度: %.2f\n", best_config$title))
  cat(sprintf("  期刊匹配度: %.2f\n", best_config$journal))
  cat(sprintf("  年份匹配度: %.2f\n", best_config$year))
  cat(sprintf("  综合评分: %.2f\n", best_config$final))
  
  return(list(
    best_config = best_config,
    best_accuracy = best_accuracy,
    all_results = all_results
  ))
}

# ===============================================================================
# 主训练函数
# ===============================================================================

train_optimal_thresholds <- function(data = NULL, sample_size = 100, max_test = 20) {
  cat("🚀 开始DOI补全阈值训练...\n\n")

  # 检查数据
  if (is.null(data)) {
    cat("❌ 未提供数据\n")
    return(NULL)
  } else if (is.character(data) && file.exists(data)) {
    cat(sprintf("📁 加载数据文件: %s\n", data))
    data <- readRDS(data)
  } else if (is.data.frame(data)) {
    cat("📁 使用传入的数据框...\n")
  } else {
    cat("❌ 数据格式不正确\n")
    return(NULL)
  }
  
  # 准备训练数据
  training_data <- prepare_training_data(data, sample_size)
  
  # 训练Crossref引擎
  cat("🎯 训练 Crossref 引擎阈值...\n")
  crossref_results <- grid_search_training(training_data, "crossref", max_test)
  
  # 保存结果
  timestamp <- format(Sys.time(), "%Y%m%d_%H%M%S")
  results_file <- sprintf("threshold_training_results_%s.rds", timestamp)
  
  training_results <- list(
    crossref = crossref_results,
    training_data_size = nrow(training_data),
    test_size = max_test,
    timestamp = Sys.time()
  )
  
  saveRDS(training_results, results_file)
  cat(sprintf("💾 训练结果已保存: %s\n", results_file))
  
  # 生成优化配置文件
  config_file <- sprintf("optimized_thresholds_%s.R", timestamp)
  cat(sprintf("📝 生成优化配置文件: %s\n", config_file))
  
  sink(config_file)
  cat("# 基于实际数据训练的优化阈值配置\n")
  cat(sprintf("# 训练时间: %s\n", Sys.time()))
  cat(sprintf("# 训练样本: %d 条记录\n", nrow(training_data)))
  cat(sprintf("# 测试样本: %d 条记录\n", max_test))
  cat(sprintf("# Crossref准确率: %.1f%%\n\n", 100 * crossref_results$best_accuracy))
  
  cat("OPTIMIZED_THRESHOLDS <- list(\n")
  cat("  crossref = list(\n")
  cat(sprintf("    title = %.2f,\n", crossref_results$best_config$title))
  cat(sprintf("    journal = %.2f,\n", crossref_results$best_config$journal))
  cat(sprintf("    year = %.2f,\n", crossref_results$best_config$year))
  cat(sprintf("    final = %.2f\n", crossref_results$best_config$final))
  cat("  )\n")
  cat(")\n\n")
  
  cat("# 使用方法:\n")
  cat("# source('optimized_thresholds_YYYYMMDD_HHMMSS.R')\n")
  cat("# VERIFIED_CONFIG$thresholds$crossref <- OPTIMIZED_THRESHOLDS$crossref\n")
  sink()
  
  return(training_results)
}

cat("✅ DOI补全阈值训练系统已加载\n")
cat("📋 主要函数:\n")
cat("  - train_optimal_thresholds()       : 主训练函数\n")
cat("  - prepare_training_data()          : 准备训练数据\n")
cat("  - grid_search_training()           : 网格搜索训练\n")
cat("===============================================================================\n")

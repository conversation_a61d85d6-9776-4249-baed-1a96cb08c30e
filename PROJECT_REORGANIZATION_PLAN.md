# 文献计量分析项目重组方案

## 当前问题分析

### 1. 项目结构问题
- **编号不一致**: R脚本编号混乱，不符合实际处理流程
- **功能重复**: 多个文件实现相似功能，缺乏统一标准
- **数据分散**: 数据文件分布在多个目录，缺乏清晰层次
- **文档不符**: 框架文档与实际代码实现不匹配

### 2. 代码文件问题
- **命名混乱**: 文件名不能反映实际功能
- **版本混杂**: 存在多个版本的相同功能代码
- **依赖不清**: 脚本间的调用关系不明确

### 3. 数据组织问题
- **目录重复**: 如`03_api_cache`和`06_api_cache`
- **分类不当**: 数据文件分类不符合处理流程
- **命名不规范**: 数据文件命名不符合框架标准

## 重组目标

### 1. 建立清晰的处理流程
```
原始数据 → 基线数据 → 增强数据 → 分析结果
```

### 2. 统一编号体系
- **01-09**: 数据处理阶段
- **10-19**: 分析阶段  
- **20-29**: 可视化阶段
- **30-39**: 报告生成阶段

### 3. 规范数据命名
- **dataa_**: 原始数据 (raw data)
- **datay_**: 基线数据 (baseline data)
- **datax_**: 增强数据 (enhanced data)

## 重组方案

### 阶段1: 数据目录重组

#### 新的数据目录结构
```
data_repository/
├── 01_raw_data/                    # 原始数据
│   ├── wos_files/                  # WoS原始文件
│   └── metadata/                   # 元数据
├── 02_baseline_data/               # 基线数据
│   ├── bibliometrix/               # bibliometrix处理结果
│   ├── citespace/                  # CiteSpace处理结果
│   └── vosviewer/                  # VOSviewer处理结果
├── 03_enhanced_data/               # 增强数据
│   ├── deduplication/              # 去重结果
│   ├── doi_completion/             # DOI补全结果
│   └── validation/                 # 验证结果
├── 04_analysis_outputs/            # 分析输出
│   ├── networks/                   # 网络分析
│   ├── trends/                     # 趋势分析
│   └── collaboration/              # 合作分析
├── 05_reports/                     # 报告文件
│   ├── processing/                 # 处理报告
│   ├── quality/                    # 质量报告
│   └── final/                      # 最终报告
├── 06_cache/                       # 缓存文件
│   ├── api_cache/                  # API缓存
│   └── temp/                       # 临时文件
└── 07_logs/                        # 日志文件
    ├── processing/                 # 处理日志
    └── errors/                     # 错误日志
```

### 阶段2: R脚本重组

#### 新的R脚本结构
```
R/
├── 01_data_import.R                # 数据导入与转换
├── 02_data_validation.R            # 数据验证
├── 03_baseline_processing.R        # 基线处理
├── 04_deduplication.R              # 去重处理
├── 05_doi_completion.R             # DOI补全
├── 06_data_enhancement.R           # 数据增强
├── 07_quality_control.R            # 质量控制
├── 08_data_export.R                # 数据导出
├── 09_processing_report.R          # 处理报告
├── 10_network_analysis.R           # 网络分析
├── 11_trend_analysis.R             # 趋势分析
├── 12_collaboration_analysis.R     # 合作分析
├── 13_citation_analysis.R          # 引用分析
├── 20_visualization_networks.R     # 网络可视化
├── 21_visualization_trends.R       # 趋势可视化
├── 22_visualization_maps.R         # 地图可视化
├── 30_report_generation.R          # 报告生成
├── 31_summary_statistics.R         # 统计摘要
└── utils/                          # 工具函数
    ├── data_utils.R                # 数据处理工具
    ├── analysis_utils.R            # 分析工具
    ├── visualization_utils.R       # 可视化工具
    └── report_utils.R              # 报告工具
```

### 阶段3: 配置文件统一

#### 主配置文件 (config.R)
```r
# 项目配置文件
PROJECT_CONFIG <- list(
  # 路径配置
  paths = list(
    raw_data = "data_repository/01_raw_data",
    baseline_data = "data_repository/02_baseline_data", 
    enhanced_data = "data_repository/03_enhanced_data",
    analysis_outputs = "data_repository/04_analysis_outputs",
    reports = "data_repository/05_reports",
    cache = "data_repository/06_cache",
    logs = "data_repository/07_logs"
  ),
  
  # 文件命名规范
  naming = list(
    raw_prefix = "dataa_",
    baseline_prefix = "datay_",
    enhanced_prefix = "datax_"
  ),
  
  # 处理参数
  processing = list(
    deduplication = list(
      title_tolerances = c(0.98, 0.95, 0.90),
      author_tolerance = 0.95,
      abstract_tolerance = 0.90
    ),
    doi_completion = list(
      title_threshold = 0.75,
      journal_threshold = 0.4,
      year_threshold = 0.5,
      subject_threshold = 0.8,
      final_threshold = 0.65
    )
  )
)
```

## 实施计划

### 第1步: 数据迁移 (优先级: 高)
1. 创建新的目录结构
2. 迁移现有数据文件
3. 更新文件命名
4. 清理重复文件

### 第2步: 代码重构 (优先级: 高)  
1. 重新编号R脚本
2. 合并重复功能
3. 提取公共函数
4. 统一配置管理

### 第3步: 文档更新 (优先级: 中)
1. 更新框架文档
2. 编写使用指南
3. 创建API文档
4. 完善README

### 第4步: 测试验证 (优先级: 中)
1. 功能测试
2. 性能测试  
3. 数据完整性验证
4. 流程验证

### 第5步: 部署优化 (优先级: 低)
1. 性能优化
2. 错误处理改进
3. 日志系统完善
4. 监控机制建立

## 预期收益

### 1. 提高效率
- 减少重复工作
- 简化操作流程
- 提高代码复用性

### 2. 改善质量
- 统一处理标准
- 减少错误风险
- 提高数据质量

### 3. 便于维护
- 清晰的项目结构
- 规范的命名体系
- 完善的文档体系

### 4. 易于扩展
- 模块化设计
- 标准化接口
- 灵活的配置系统

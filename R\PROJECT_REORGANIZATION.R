# 文献计量分析项目重组脚本
# 基于框架文档和实际代码情况进行系统性重组

cat("=== 文献计量分析项目重组 ===\n")
cat("开始项目结构重组...\n\n")

# 加载必要的包
suppressMessages({
  library(here)
  library(fs)
})

# 项目配置
PROJECT_CONFIG <- list(
  # 新的路径结构
  new_paths = list(
    raw_data = "data_repository/01_raw_data",
    baseline_data = "data_repository/02_baseline_data", 
    enhanced_data = "data_repository/03_enhanced_data",
    analysis_outputs = "data_repository/04_analysis_outputs",
    reports = "data_repository/05_reports",
    cache = "data_repository/06_cache",
    logs = "data_repository/07_logs"
  ),
  
  # 文件命名规范
  naming = list(
    raw_prefix = "dataa_",
    baseline_prefix = "datay_",
    enhanced_prefix = "datax_"
  ),
  
  # 需要保留的核心文件
  core_files = list(
    r_scripts = c(
      "01_wos_convert2df.R",
      "auto_full_doi_completion.R",
      "02_deduplication_enhanced.R",
      "analyze_missing_doi.R"
    ),
    data_files = c(
      "COMPLETE_DOI_RESULTS.csv",
      "AUTO_ANALYSIS_REPORT.txt",
      "missing_doi_records.csv"
    )
  )
)

# 创建新的目录结构
create_new_structure <- function() {
  cat("创建新的目录结构...\n")
  
  # 主要数据目录
  main_dirs <- c(
    "data_repository/01_raw_data/wos_files",
    "data_repository/01_raw_data/metadata",
    "data_repository/02_baseline_data/bibliometrix",
    "data_repository/02_baseline_data/citespace", 
    "data_repository/02_baseline_data/vosviewer",
    "data_repository/03_enhanced_data/deduplication",
    "data_repository/03_enhanced_data/doi_completion",
    "data_repository/03_enhanced_data/validation",
    "data_repository/04_analysis_outputs/networks",
    "data_repository/04_analysis_outputs/trends",
    "data_repository/04_analysis_outputs/collaboration",
    "data_repository/05_reports/processing",
    "data_repository/05_reports/quality",
    "data_repository/05_reports/final",
    "data_repository/06_cache/api_cache",
    "data_repository/06_cache/temp",
    "data_repository/07_logs/processing",
    "data_repository/07_logs/errors"
  )
  
  # R脚本目录
  r_dirs <- c(
    "R/utils"
  )
  
  # 文档目录
  doc_dirs <- c(
    "docs/framework",
    "docs/guides",
    "docs/api"
  )
  
  all_dirs <- c(main_dirs, r_dirs, doc_dirs)
  
  for (dir_path in all_dirs) {
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = TRUE)
      cat(sprintf("✅ 创建目录: %s\n", dir_path))
    }
  }
}

# 迁移原始数据文件
migrate_raw_data <- function() {
  cat("\n迁移原始数据文件...\n")
  
  # 迁移WoS原始文件
  old_raw_path <- "data_repository/00_original_raw_files"
  new_raw_path <- "data_repository/01_raw_data/wos_files"
  
  if (dir.exists(old_raw_path)) {
    raw_files <- list.files(old_raw_path, pattern = "\\.txt$", full.names = TRUE)
    for (file in raw_files) {
      new_file <- file.path(new_raw_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移: %s\n", basename(file)))
      }
    }
  }
}

# 迁移基线数据
migrate_baseline_data <- function() {
  cat("\n迁移基线数据...\n")
  
  # 迁移bibliometrix数据
  old_bib_path <- "data_repository/01_baseline_datasets/bibliometrix_processed"
  new_bib_path <- "data_repository/02_baseline_data/bibliometrix"
  
  if (dir.exists(old_bib_path)) {
    bib_files <- list.files(old_bib_path, full.names = TRUE)
    for (file in bib_files) {
      if (file.info(file)$isdir) next
      new_file <- file.path(new_bib_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移bibliometrix: %s\n", basename(file)))
      }
    }
  }
  
  # 迁移citespace数据
  old_cite_path <- "data_repository/01_baseline_datasets/citespace_processed"
  new_cite_path <- "data_repository/02_baseline_data/citespace"
  
  if (dir.exists(old_cite_path)) {
    cite_files <- list.files(old_cite_path, pattern = "\\.(rds|txt)$", full.names = TRUE)
    for (file in cite_files) {
      new_file <- file.path(new_cite_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移citespace: %s\n", basename(file)))
      }
    }
  }
  
  # 迁移vosviewer数据
  old_vos_path <- "data_repository/01_baseline_datasets/vosviewer_processed"
  new_vos_path <- "data_repository/02_baseline_data/vosviewer"
  
  if (dir.exists(old_vos_path)) {
    vos_files <- list.files(old_vos_path, full.names = TRUE)
    for (file in vos_files) {
      if (file.info(file)$isdir) next
      new_file <- file.path(new_vos_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移vosviewer: %s\n", basename(file)))
      }
    }
  }
}

# 迁移增强数据
migrate_enhanced_data <- function() {
  cat("\n迁移增强数据...\n")
  
  # 迁移去重数据
  old_enhanced_path <- "data_repository/02_enhanced_dataset"
  new_dedup_path <- "data_repository/03_enhanced_data/deduplication"
  
  if (dir.exists(old_enhanced_path)) {
    enhanced_files <- list.files(old_enhanced_path, pattern = "\\.rds$", full.names = TRUE)
    for (file in enhanced_files) {
      new_file <- file.path(new_dedup_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移去重数据: %s\n", basename(file)))
      }
    }
  }
  
  # 迁移DOI补全数据
  old_doi_path <- "data_repository/04_enhancement_reports"
  new_doi_path <- "data_repository/03_enhanced_data/doi_completion"
  
  if (dir.exists(old_doi_path)) {
    doi_files <- list.files(old_doi_path, pattern = "doi|DOI", full.names = TRUE)
    for (file in doi_files) {
      if (file.info(file)$isdir) next
      new_file <- file.path(new_doi_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移DOI数据: %s\n", basename(file)))
      }
    }
  }
}

# 迁移分析输出
migrate_analysis_outputs <- function() {
  cat("\n迁移分析输出...\n")
  
  # 迁移网络分析
  old_analysis_path <- "data_repository/04_analysis_outputs"
  
  if (dir.exists(old_analysis_path)) {
    # 迁移网络文件
    if (dir.exists(file.path(old_analysis_path, "networks"))) {
      network_files <- list.files(file.path(old_analysis_path, "networks"), full.names = TRUE)
      for (file in network_files) {
        if (file.info(file)$isdir) next
        new_file <- file.path("data_repository/04_analysis_outputs/networks", basename(file))
        if (file.copy(file, new_file, overwrite = TRUE)) {
          cat(sprintf("✅ 迁移网络分析: %s\n", basename(file)))
        }
      }
    }
    
    # 迁移趋势文件
    if (dir.exists(file.path(old_analysis_path, "trends"))) {
      trend_files <- list.files(file.path(old_analysis_path, "trends"), full.names = TRUE)
      for (file in trend_files) {
        if (file.info(file)$isdir) next
        new_file <- file.path("data_repository/04_analysis_outputs/trends", basename(file))
        if (file.copy(file, new_file, overwrite = TRUE)) {
          cat(sprintf("✅ 迁移趋势分析: %s\n", basename(file)))
        }
      }
    }
    
    # 迁移合作分析
    if (dir.exists(file.path(old_analysis_path, "collaboration"))) {
      collab_files <- list.files(file.path(old_analysis_path, "collaboration"), full.names = TRUE)
      for (file in collab_files) {
        if (file.info(file)$isdir) next
        new_file <- file.path("data_repository/04_analysis_outputs/collaboration", basename(file))
        if (file.copy(file, new_file, overwrite = TRUE)) {
          cat(sprintf("✅ 迁移合作分析: %s\n", basename(file)))
        }
      }
    }
  }
}

# 迁移缓存和日志
migrate_cache_logs <- function() {
  cat("\n迁移缓存和日志文件...\n")
  
  # 合并API缓存
  old_cache_paths <- c("data_repository/03_api_cache", "data_repository/06_api_cache")
  new_cache_path <- "data_repository/06_cache/api_cache"
  
  for (old_path in old_cache_paths) {
    if (dir.exists(old_path)) {
      cache_files <- list.files(old_path, full.names = TRUE)
      for (file in cache_files) {
        if (file.info(file)$isdir) next
        new_file <- file.path(new_cache_path, basename(file))
        if (file.copy(file, new_file, overwrite = TRUE)) {
          cat(sprintf("✅ 迁移缓存: %s\n", basename(file)))
        }
      }
    }
  }
  
  # 迁移日志文件
  old_log_path <- "data_repository/05_execution_logs"
  new_log_path <- "data_repository/07_logs/processing"
  
  if (dir.exists(old_log_path)) {
    log_files <- list.files(old_log_path, recursive = TRUE, full.names = TRUE)
    for (file in log_files) {
      if (file.info(file)$isdir) next
      new_file <- file.path(new_log_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移日志: %s\n", basename(file)))
      }
    }
  }
  
  # 迁移临时文件
  old_temp_path <- "data_repository/temp"
  new_temp_path <- "data_repository/06_cache/temp"
  
  if (dir.exists(old_temp_path)) {
    temp_files <- list.files(old_temp_path, full.names = TRUE)
    for (file in temp_files) {
      if (file.info(file)$isdir) next
      new_file <- file.path(new_temp_path, basename(file))
      if (file.copy(file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 迁移临时文件: %s\n", basename(file)))
      }
    }
  }
}

# 重组R脚本
reorganize_r_scripts <- function() {
  cat("\n重组R脚本...\n")
  
  # 核心脚本重命名映射
  script_mapping <- list(
    "01_wos_convert2df.R" = "01_data_import.R",
    "auto_full_doi_completion.R" = "05_doi_completion.R", 
    "02_deduplication_enhanced.R" = "04_deduplication.R",
    "analyze_missing_doi.R" = "07_quality_control.R"
  )
  
  # 重命名核心脚本
  for (old_name in names(script_mapping)) {
    old_file <- file.path("R", old_name)
    new_name <- script_mapping[[old_name]]
    new_file <- file.path("R", new_name)
    
    if (file.exists(old_file)) {
      if (file.copy(old_file, new_file, overwrite = TRUE)) {
        cat(sprintf("✅ 重命名脚本: %s → %s\n", old_name, new_name))
      }
    }
  }
  
  # 移动重复和过时的脚本到archive
  archive_dir <- "R/archive"
  if (!dir.exists(archive_dir)) {
    dir.create(archive_dir, recursive = TRUE)
  }
  
  # 需要归档的脚本
  archive_scripts <- c(
    "01_data_enhancement_complete.R",
    "01_data_enhancement_framework.R", 
    "01_data_enhancement_framework_fixed.R",
    "01_data_enhancement_simple.R",
    "02_deduplication.R",
    "02_deduplication_biblioshiny.R",
    "02_deduplication_enhanced_advanced.R",
    "02_deduplication_extreme.R",
    "02_deduplication_extreme_fix.R",
    "auto_comprehensive_test.R",
    "auto_final_report_generator.R",
    "auto_full_processing.R",
    "complete_doi_by_ut.R",
    "complete_doi_resume.R",
    "crossref_doi_lookup.R",
    "debug_biblioshiny.R",
    "debug_biblioshiny_monitor.R",
    "debug_deduplication_records.R",
    "doi_completion_final.R",
    "PROJECT_CLEANUP_DOI.R"
  )
  
  for (script in archive_scripts) {
    old_file <- file.path("R", script)
    if (file.exists(old_file)) {
      new_file <- file.path(archive_dir, script)
      if (file.copy(old_file, new_file, overwrite = TRUE)) {
        file.remove(old_file)
        cat(sprintf("✅ 归档脚本: %s\n", script))
      }
    }
  }
}

# 创建配置文件
create_config_file <- function() {
  cat("\n创建项目配置文件...\n")
  
  config_content <- '# 文献计量分析项目配置文件
# 统一管理项目路径、参数和设置

PROJECT_CONFIG <- list(
  # 项目信息
  project = list(
    name = "Bibliometric Analysis",
    version = "2.0.0",
    description = "文献计量分析系统"
  ),
  
  # 路径配置
  paths = list(
    raw_data = "data_repository/01_raw_data",
    baseline_data = "data_repository/02_baseline_data", 
    enhanced_data = "data_repository/03_enhanced_data",
    analysis_outputs = "data_repository/04_analysis_outputs",
    reports = "data_repository/05_reports",
    cache = "data_repository/06_cache",
    logs = "data_repository/07_logs"
  ),
  
  # 文件命名规范
  naming = list(
    raw_prefix = "dataa_",      # 原始数据前缀
    baseline_prefix = "datay_", # 基线数据前缀  
    enhanced_prefix = "datax_"  # 增强数据前缀
  ),
  
  # 处理参数
  processing = list(
    # 去重参数
    deduplication = list(
      title_tolerances = c(0.98, 0.95, 0.90),
      author_tolerance = 0.95,
      abstract_tolerance = 0.90
    ),
    
    # DOI补全参数
    doi_completion = list(
      title_threshold = 0.75,
      journal_threshold = 0.4,
      year_threshold = 0.5,
      subject_threshold = 0.8,
      final_threshold = 0.65,
      api_delay = 1.0,
      batch_size = 100
    ),
    
    # 数据验证参数
    validation = list(
      min_records = 1,
      required_fields = c("UT", "TI", "PY", "AU"),
      year_range = c(1900, as.numeric(format(Sys.Date(), "%Y")))
    )
  ),
  
  # 分析参数
  analysis = list(
    network = list(
      min_degree = 2,
      layout_algorithm = "fruchterman_reingold"
    ),
    
    trends = list(
      time_window = 5,
      smoothing_method = "loess"
    ),
    
    collaboration = list(
      min_coauthors = 2,
      country_analysis = TRUE
    )
  ),
  
  # 可视化参数
  visualization = list(
    theme = "minimal",
    color_palette = "viridis",
    figure_width = 12,
    figure_height = 8,
    dpi = 300
  ),
  
  # 输出格式
  output = list(
    data_format = "rds",
    report_format = c("html", "pdf"),
    figure_format = c("png", "svg")
  )
)

# 获取配置函数
get_config <- function(section = NULL, key = NULL) {
  if (is.null(section)) {
    return(PROJECT_CONFIG)
  }
  
  if (is.null(key)) {
    return(PROJECT_CONFIG[[section]])
  }
  
  return(PROJECT_CONFIG[[section]][[key]])
}

# 获取路径函数
get_path <- function(path_name) {
  return(PROJECT_CONFIG$paths[[path_name]])
}

# 获取文件名函数
get_filename <- function(prefix, name, extension = "rds") {
  return(paste0(prefix, name, ".", extension))
}
'
  
  writeLines(config_content, "R/config.R")
  cat("✅ 创建配置文件: R/config.R\n")
}

# 清理旧目录
cleanup_old_directories <- function() {
  cat("\n清理旧目录结构...\n")
  
  # 需要清理的旧目录
  old_dirs <- c(
    "data_repository/00_original_raw_files",
    "data_repository/00_raw_data", 
    "data_repository/01_baseline_datasets",
    "data_repository/02_enhanced_dataset",
    "data_repository/03_api_cache",
    "data_repository/03_intermediate_files",
    "data_repository/04_enhancement_reports",
    "data_repository/05_execution_logs",
    "data_repository/06_api_cache",
    "data_repository/06_data_reports",
    "data_repository/07_metadata_and_notes",
    "data_repository/temp",
    "DOI_COMPLETION_FINAL"
  )
  
  for (old_dir in old_dirs) {
    if (dir.exists(old_dir)) {
      # 检查目录是否为空
      files_in_dir <- list.files(old_dir, recursive = TRUE)
      if (length(files_in_dir) == 0) {
        unlink(old_dir, recursive = TRUE)
        cat(sprintf("✅ 删除空目录: %s\n", old_dir))
      } else {
        cat(sprintf("⚠️  保留非空目录: %s (包含 %d 个文件)\n", old_dir, length(files_in_dir)))
      }
    }
  }
}

# 生成重组报告
generate_reorganization_report <- function() {
  cat("\n生成重组报告...\n")
  
  report_content <- sprintf('# 项目重组报告

## 重组时间
%s

## 新的项目结构

### 数据目录
- `01_raw_data/`: 原始WoS数据文件
- `02_baseline_data/`: bibliometrix、CiteSpace、VOSviewer基线处理结果
- `03_enhanced_data/`: 去重、DOI补全等增强处理结果
- `04_analysis_outputs/`: 网络、趋势、合作等分析结果
- `05_reports/`: 各类处理和分析报告
- `06_cache/`: API缓存和临时文件
- `07_logs/`: 处理日志和错误记录

### R脚本
- `01_data_import.R`: 数据导入与转换
- `04_deduplication.R`: 去重处理
- `05_doi_completion.R`: DOI补全
- `07_quality_control.R`: 质量控制
- `config.R`: 项目配置文件

### 配置管理
- 统一的配置文件管理所有参数
- 标准化的文件命名规范
- 清晰的路径管理

## 主要改进

1. **结构清晰**: 按照处理流程组织目录结构
2. **命名规范**: 统一的文件和目录命名
3. **配置统一**: 集中管理所有配置参数
4. **代码精简**: 移除重复和过时代码
5. **文档完善**: 更新框架文档和使用指南

## 使用方法

```r
# 加载配置
source("R/config.R")

# 获取路径
raw_data_path <- get_path("raw_data")

# 获取配置参数
dedup_params <- get_config("processing", "deduplication")
```

## 注意事项

1. 旧的目录结构已保留，确保数据安全
2. 重复的脚本已归档到 `R/archive/`
3. 所有配置参数现在统一管理
4. 建议逐步迁移到新的结构

重组完成！项目现在具有更清晰的结构和更好的可维护性。
', format(Sys.time(), "%Y-%m-%d %H:%M:%S"))
  
  writeLines(report_content, "data_repository/05_reports/final/PROJECT_REORGANIZATION_REPORT.md")
  cat("✅ 生成重组报告: data_repository/05_reports/final/PROJECT_REORGANIZATION_REPORT.md\n")
}

# 主执行函数
main_reorganization <- function() {
  cat("开始项目重组...\n\n")
  
  # 执行重组步骤
  create_new_structure()
  migrate_raw_data()
  migrate_baseline_data() 
  migrate_enhanced_data()
  migrate_analysis_outputs()
  migrate_cache_logs()
  reorganize_r_scripts()
  create_config_file()
  generate_reorganization_report()
  
  cat("\n=== 重组完成 ===\n")
  cat("✅ 新的目录结构已创建\n")
  cat("✅ 数据文件已迁移\n") 
  cat("✅ R脚本已重组\n")
  cat("✅ 配置文件已创建\n")
  cat("✅ 重组报告已生成\n")
  
  cat("\n📁 新的项目结构:\n")
  cat("data_repository/\n")
  cat("├── 01_raw_data/          # 原始数据\n")
  cat("├── 02_baseline_data/     # 基线数据\n") 
  cat("├── 03_enhanced_data/     # 增强数据\n")
  cat("├── 04_analysis_outputs/  # 分析结果\n")
  cat("├── 05_reports/           # 报告文件\n")
  cat("├── 06_cache/             # 缓存文件\n")
  cat("└── 07_logs/              # 日志文件\n")
  
  cat("\nR/\n")
  cat("├── 01_data_import.R      # 数据导入\n")
  cat("├── 04_deduplication.R    # 去重处理\n")
  cat("├── 05_doi_completion.R   # DOI补全\n")
  cat("├── 07_quality_control.R  # 质量控制\n")
  cat("├── config.R              # 项目配置\n")
  cat("└── archive/              # 归档脚本\n")
  
  cat("\n🎉 项目重组完成！现在具有更清晰的结构和更好的可维护性。\n")
  cat("📖 详细信息请查看重组报告。\n")
}

# 执行重组
main_reorganization()

# 正确的工具结构重新组织
# CiteSpace和VOSviewer是分析工具，不是数据源

cat("=== 正确的工具结构重新组织 ===\n")
cat("将CiteSpace和VOSviewer相关代码移动到对应工具文件夹...\n\n")

# 分析当前错误的结构
analyze_current_structure <- function() {
  cat("📊 分析当前结构问题:\n")
  cat("❌ 错误理解: 将CiteSpace和VOSviewer当作数据源\n")
  cat("✅ 正确理解: CiteSpace和VOSviewer是分析工具\n\n")
  
  cat("需要重新组织的文件:\n")
  cat("- 02_import_citespace_data.R → citespace/目录\n")
  cat("- 03_import_vosviewer_data.R → vosviewer/目录\n\n")
  
  cat("正确的核心流程应该是:\n")
  cat("01. WoS数据导入 (唯一的数据源)\n")
  cat("02. 数据验证与清理\n") 
  cat("03. 去重处理\n")
  cat("04. 数据增强 (包括DOI补全)\n")
  cat("05. 数据整合\n")
  cat("06. 质量控制\n")
  cat("07. 工具分析 (CiteSpace, VOSviewer等)\n\n")
}

# 重新组织文件结构
reorganize_tool_structure <- function() {
  cat("🔧 重新组织文件结构...\n")
  
  # 1. 移动CiteSpace相关文件
  cat("\n📁 处理CiteSpace相关文件:\n")
  citespace_files <- c(
    "02_import_citespace_data.R"
  )
  
  for (file in citespace_files) {
    src_path <- file.path("R", file)
    dst_path <- file.path("R", "citespace", file)
    
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      file.remove(src_path)
      cat(sprintf("  ✅ 移动: %s → citespace/%s\n", file, file))
    } else {
      cat(sprintf("  ❌ 文件不存在: %s\n", file))
    }
  }
  
  # 2. 移动VOSviewer相关文件
  cat("\n📁 处理VOSviewer相关文件:\n")
  vosviewer_files <- c(
    "03_import_vosviewer_data.R"
  )
  
  for (file in vosviewer_files) {
    src_path <- file.path("R", file)
    dst_path <- file.path("R", "vosviewer", file)
    
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      file.remove(src_path)
      cat(sprintf("  ✅ 移动: %s → vosviewer/%s\n", file, file))
    } else {
      cat(sprintf("  ❌ 文件不存在: %s\n", file))
    }
  }
}

# 重新编号核心脚本
renumber_core_scripts <- function() {
  cat("\n🔢 重新编号核心脚本...\n")
  
  # 新的核心脚本编号映射
  renumber_mapping <- list(
    "01_import_wos_data.R" = "01_import_wos_data.R",           # 保持不变
    "04_validate_and_clean_data.R" = "02_validate_and_clean_data.R",  # 04→02
    "05_deduplicate_records.R" = "03_deduplicate_records.R",          # 05→03
    "06_deduplicate_advanced.R" = "04_deduplicate_advanced.R",        # 06→04 (可选)
    "07_enhance_data_comprehensive.R" = "05_enhance_data_comprehensive.R", # 07→05
    "08_complete_missing_dois.R" = "06_complete_missing_dois.R",      # 08→06
    "09_integrate_enhanced_data.R" = "07_integrate_enhanced_data.R",  # 09→07
    "10_quality_control_and_report.R" = "08_quality_control_and_report.R" # 10→08
  )
  
  # 执行重新编号
  for (old_name in names(renumber_mapping)) {
    new_name <- renumber_mapping[[old_name]]
    old_path <- file.path("R", old_name)
    new_path <- file.path("R", new_name)
    
    if (file.exists(old_path) && old_name != new_name) {
      file.rename(old_path, new_path)
      cat(sprintf("  ✅ 重新编号: %s → %s\n", old_name, new_name))
    } else if (file.exists(old_path)) {
      cat(sprintf("  ✅ 保持不变: %s\n", old_name))
    } else {
      cat(sprintf("  ❌ 文件不存在: %s\n", old_name))
    }
  }
}

# 创建工具目录说明文件
create_tool_readme <- function() {
  cat("\n📝 创建工具目录说明文件...\n")
  
  # CiteSpace目录说明
  citespace_readme <- '# CiteSpace工具目录

## 目录说明
CiteSpace是科学计量学分析工具，用于文献共引分析、关键词共现分析等。

## 文件说明
- `02_import_citespace_data.R` - CiteSpace数据导入与处理脚本

## 使用方法
1. 首先完成核心数据处理流程 (01-08)
2. 使用本目录中的脚本进行CiteSpace分析
3. 根据分析需求选择相应的功能

## 依赖关系
- 需要先完成WoS数据导入和基础处理
- 建议在数据增强和质量控制后使用

CiteSpace是分析工具，不是数据源。
'
  
  writeLines(citespace_readme, "R/citespace/README.md")
  cat("  ✅ 创建: citespace/README.md\n")
  
  # VOSviewer目录说明
  vosviewer_readme <- '# VOSviewer工具目录

## 目录说明
VOSviewer是文献计量学可视化工具，用于构建和可视化文献网络。

## 文件说明
- `03_import_vosviewer_data.R` - VOSviewer数据导入与处理脚本

## 使用方法
1. 首先完成核心数据处理流程 (01-08)
2. 使用本目录中的脚本进行VOSviewer分析
3. 根据可视化需求选择相应的功能

## 依赖关系
- 需要先完成WoS数据导入和基础处理
- 建议在数据增强和质量控制后使用

VOSviewer是分析工具，不是数据源。
'
  
  writeLines(vosviewer_readme, "R/vosviewer/README.md")
  cat("  ✅ 创建: vosviewer/README.md\n")
}

# 验证新结构
verify_new_structure <- function() {
  cat("\n🔍 验证新结构...\n")
  
  # 检查核心脚本
  expected_core_files <- c(
    "01_import_wos_data.R",
    "02_validate_and_clean_data.R",
    "03_deduplicate_records.R",
    "04_deduplicate_advanced.R",
    "05_enhance_data_comprehensive.R",
    "06_complete_missing_dois.R",
    "07_integrate_enhanced_data.R",
    "08_quality_control_and_report.R"
  )
  
  cat("核心处理脚本:\n")
  all_core_present <- TRUE
  for (file in expected_core_files) {
    if (file.exists(file.path("R", file))) {
      cat(sprintf("  ✅ %s\n", file))
    } else {
      cat(sprintf("  ❌ %s (缺失)\n", file))
      all_core_present <- FALSE
    }
  }
  
  # 检查工具目录
  cat("\n工具目录:\n")
  tool_dirs <- c("citespace", "vosviewer", "bibliometrix", "biblioshiny")
  for (dir in tool_dirs) {
    if (dir.exists(file.path("R", dir))) {
      files <- list.files(file.path("R", dir), pattern = "\\.R$")
      cat(sprintf("  ✅ %s/ (%d个文件)\n", dir, length(files)))
    } else {
      cat(sprintf("  ❌ %s/ (不存在)\n", dir))
    }
  }
  
  return(all_core_present)
}

# 生成正确结构报告
generate_correct_structure_report <- function(success) {
  cat("\n📋 生成正确结构报告...\n")
  
  report_content <- sprintf('# 正确的工具结构重新组织报告

## 重新组织时间
%s

## 问题识别
之前错误地将CiteSpace和VOSviewer当作数据源，实际上它们是分析工具。

## 结构调整

### 移动的文件
- `02_import_citespace_data.R` → `citespace/02_import_citespace_data.R`
- `03_import_vosviewer_data.R` → `vosviewer/03_import_vosviewer_data.R`

### 重新编号的核心脚本
```
01_import_wos_data.R              # WoS数据导入 (唯一数据源)
02_validate_and_clean_data.R      # 数据验证与清理
03_deduplicate_records.R          # 去重处理
04_deduplicate_advanced.R         # 高级去重 (可选)
05_enhance_data_comprehensive.R   # 数据增强
06_complete_missing_dois.R        # DOI补全
07_integrate_enhanced_data.R      # 数据整合
08_quality_control_and_report.R   # 质量控制
```

## 正确的处理流程

### 核心数据处理 (01-08)
```
WoS导入 → 验证清理 → 去重 → 数据增强+DOI补全 → 整合 → 质控
```

### 分析工具使用 (工具目录)
```
citespace/     # CiteSpace分析工具
vosviewer/     # VOSviewer可视化工具
bibliometrix/  # bibliometrix分析
biblioshiny/   # biblioshiny界面
```

## 目录结构

### 核心处理流程 (根目录)
- 8个核心脚本，按数据处理逻辑顺序组织
- WoS作为唯一的数据源

### 分析工具 (工具目录)
- `citespace/` - CiteSpace科学计量学分析
- `vosviewer/` - VOSviewer文献网络可视化
- `bibliometrix/` - bibliometrix包分析功能
- `biblioshiny/` - biblioshiny网页界面

### 专用功能 (功能目录)
- `enhanced/` - 高级增强功能
- `doi_tools/` - DOI处理工具
- `automation/` - 自动化批处理
- `debug/` - 调试工具
- `reports/` - 报告生成
- `management/` - 项目管理
- `utils/` - 通用工具函数

## 使用指南

### 标准工作流程
1. **核心数据处理** (按顺序执行01-08)
2. **选择分析工具** (根据研究需求)
3. **生成分析结果** (使用相应工具目录)

### 核心处理执行
```r
# 核心数据处理流程
scripts <- c("01", "02", "03", "05", "06", "07", "08")  # 04是可选的
for (num in scripts) {
  script_file <- list.files("R", pattern = paste0("^", num, "_"), full.names = TRUE)[1]
  if (!is.null(script_file) && file.exists(script_file)) {
    cat("执行:", basename(script_file), "\\n")
    source(script_file)
  }
}
```

### 分析工具使用
```r
# CiteSpace分析
source("R/citespace/02_import_citespace_data.R")

# VOSviewer可视化
source("R/vosviewer/03_import_vosviewer_data.R")

# bibliometrix分析
source("R/bibliometrix/01_data_import.R")
```

## 重新组织状态
%s

## 核心改进

1. **概念澄清** - 明确区分数据源和分析工具
2. **结构合理** - 工具代码放在对应工具目录
3. **流程清晰** - 核心处理 → 工具分析
4. **便于维护** - 按功能和工具分类组织

这个结构更符合实际的文献计量分析工作流程！
', 
    format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    if (success) "✅ 重新组织成功" else "❌ 重新组织失败"
  )
  
  writeLines(report_content, "R/CORRECT_TOOL_STRUCTURE_REPORT.md")
  cat("✅ 正确结构报告已生成: R/CORRECT_TOOL_STRUCTURE_REPORT.md\n")
}

# 主执行函数
main_correct_structure <- function() {
  cat("开始正确的工具结构重新组织...\n\n")
  
  analyze_current_structure()
  reorganize_tool_structure()
  renumber_core_scripts()
  create_tool_readme()
  success <- verify_new_structure()
  generate_correct_structure_report(success)
  
  cat("\n=== 正确的工具结构重新组织完成 ===\n")
  cat("✅ 移动了工具相关文件到对应目录\n")
  cat("✅ 重新编号了核心处理脚本\n")
  cat("✅ 创建了工具目录说明文件\n")
  cat("✅ 生成了正确结构报告\n")
  
  if (success) {
    cat("\n🎉 结构重新组织完全成功！\n")
    cat("📋 核心流程: WoS导入 → 验证 → 去重 → 增强 → 整合 → 质控\n")
    cat("🔧 分析工具: CiteSpace, VOSviewer等在对应目录\n")
    cat("✨ 结构更符合实际的文献计量分析工作流程\n")
  } else {
    cat("\n⚠️  结构重新组织仍有问题，请检查报告\n")
  }
}

# 执行正确的结构重新组织
main_correct_structure()

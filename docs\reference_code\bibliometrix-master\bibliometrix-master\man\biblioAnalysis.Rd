% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/biblioAnalysis.R
\name{biblioAnalysis}
\alias{biblioAnalysis}
\title{Bibliometric Analysis}
\usage{
biblioAnalysis(M, sep = ";")
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to manuscripts and variables to Field Tag in the original SCOPUS and Clarivate Analytics Web of Science file.}

\item{sep}{is the field separator character. This character separates strings in each column of the data frame. The default is \code{sep = ";"}.}
}
\value{
\code{biblioAnalysis} returns an object of \code{class} "bibliometrix".

The functions \code{\link{summary}} and \code{\link{plot}} are used to obtain or print a summary and some useful plots of the results.

An object of \code{class} "bibliometrix" is a list containing the following components:

\tabular{lll}{
Articles \tab  \tab the total number of manuscripts\cr
Authors \tab       \tab the authors' frequency distribution\cr
AuthorsFrac \tab      \tab the authors' frequency distribution (fractionalized)\cr
FirstAuthors \tab      \tab corresponding author of each manuscript\cr
nAUperPaper \tab      \tab the number of authors per manuscript\cr
Appearances \tab      \tab the number of author appearances\cr
nAuthors \tab       \tab the number of authors\cr
AuMultiAuthoredArt \tab      \tab the number of authors of multi-authored articles\cr
MostCitedPapers \tab      \tab the list of manuscripts sorted by citations\cr
Years \tab      \tab publication year of each manuscript\cr
FirstAffiliation \tab      \tab the affiliation of the first author\cr
Affiliations \tab      \tab the frequency distribution of affiliations (of all co-authors for each paper)\cr
Aff_frac \tab      \tab the fractionalized frequency distribution of affiliations (of all co-authors for each paper)\cr
CO \tab      \tab the affiliation country of the first author\cr
Countries \tab      \tab the affiliation countries' frequency distribution\cr
CountryCollaboration \tab      \tab Intra-country (SCP) and intercountry (MCP) collaboration indices\cr
TotalCitation \tab      \tab the number of times each manuscript has been cited\cr
TCperYear \tab      \tab the yearly average number of times each manuscript has been cited\cr
Sources \tab      \tab the frequency distribution of sources (journals, books, etc.)\cr
DE \tab      \tab the frequency distribution of authors' keywords\cr
ID \tab      \tab the frequency distribution of keywords associated to the manuscript by SCOPUS and Clarivate Analytics Web of Science database}
}
\description{
It performs a bibliometric analysis of a dataset imported from SCOPUS and Clarivate Analytics Web of Science databases.
}
\examples{
\dontrun{
data(management, package = "bibliometrixData")

results <- biblioAnalysis(management)

summary(results, k = 10, pause = FALSE)
}

}
\seealso{
\code{\link{convert2df}} to import and convert an WoS or SCOPUS Export file in a bibliographic data frame.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.
}

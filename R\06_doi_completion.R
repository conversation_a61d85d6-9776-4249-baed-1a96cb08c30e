# 自动全量DOI补全与评估系统
# 对所有缺失DOI的文献记录应用补全，并自动分析评估结果

cat("=== 自动全量DOI补全与评估系统 ===\n")
cat("开始处理所有缺失DOI的文献记录...\n\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 优化的核心算法函数
optimized_normalize_text <- function(text) {
  if (is.na(text) || text == "") return("")
  text <- tolower(text)
  text <- gsub("[[:punct:]]", " ", text)
  text <- gsub("\\s+", " ", text)
  text <- trimws(text)
  
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "study", "analysis")
  words <- strsplit(text, "\\s+")[[1]]
  words <- words[!words %in% stop_words & nchar(words) > 2]
  
  return(paste(words, collapse = " "))
}

optimized_similarity <- function(text1, text2) {
  if (is.na(text1) || is.na(text2) || text1 == "" || text2 == "") return(0)
  
  norm1 <- optimized_normalize_text(text1)
  norm2 <- optimized_normalize_text(text2)
  
  if (norm1 == "" || norm2 == "") return(0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  
  words1 <- strsplit(norm1, "\\s+")[[1]]
  words2 <- strsplit(norm2, "\\s+")[[1]]
  
  important_words1 <- words1[nchar(words1) > 4]
  important_words2 <- words2[nchar(words2) > 4]
  
  if (length(important_words1) > 0 && length(important_words2) > 0) {
    common_important <- sum(important_words1 %in% important_words2)
    keyword_bonus <- common_important / max(length(important_words1), length(important_words2))
    
    if (common_important == 0 && similarity < 0.9) {
      similarity <- similarity * 0.7
    } else {
      similarity <- min(1.0, similarity + keyword_bonus * 0.1)
    }
  }
  
  return(similarity)
}

optimized_match_journals <- function(journal1, journal2) {
  if (is.na(journal1) || is.na(journal2) || journal1 == "" || journal2 == "") return(0.3)
  
  normalize_journal <- function(journal) {
    journal <- tolower(journal)
    journal <- gsub("^the\\s+", "", journal)
    journal <- gsub("\\s*\\(.*\\)$", "", journal)
    journal <- gsub("[[:punct:]]", " ", journal)
    journal <- gsub("\\s+", " ", journal)
    return(trimws(journal))
  }
  
  norm1 <- normalize_journal(journal1)
  norm2 <- normalize_journal(journal2)
  
  if (norm1 == norm2) return(1.0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  
  words1 <- strsplit(norm1, "\\s+")[[1]]
  words2 <- strsplit(norm2, "\\s+")[[1]]
  
  words1 <- words1[nchar(words1) > 3]
  words2 <- words2[nchar(words2) > 3]
  
  if (length(words1) > 0 && length(words2) > 0) {
    common_words <- sum(words1 %in% words2)
    keyword_similarity <- common_words / max(length(words1), length(words2))
    
    if (common_words == 0) {
      similarity <- similarity * 0.5
    } else {
      similarity <- max(similarity, keyword_similarity)
    }
  }
  
  return(similarity)
}

optimized_match_year <- function(year1, year2) {
  if (is.na(year1) || is.na(year2)) return(0.3)
  
  year1 <- as.numeric(year1)
  year2 <- as.numeric(year2)
  
  if (year1 == year2) return(1.0)
  if (abs(year1 - year2) == 1) return(0.8)
  if (abs(year1 - year2) == 2) return(0.5)
  return(0.0)
}

check_subject_relevance <- function(title1, title2) {
  medical_keywords <- c("muscle", "anatomy", "medical", "clinical", "patient", "treatment", "therapy", "disease", "pain", "jaw", "dental", "oral", "mandibular", "temporomandibular", "masticatory", "orthodontic")
  biology_keywords <- c("animal", "rat", "mouse", "rabbit", "guinea", "pig", "cell", "tissue", "bone", "development", "growth", "physiology")
  
  title1_lower <- tolower(title1)
  title2_lower <- tolower(title2)
  
  medical1 <- any(sapply(medical_keywords, function(x) grepl(x, title1_lower)))
  medical2 <- any(sapply(medical_keywords, function(x) grepl(x, title2_lower)))
  
  biology1 <- any(sapply(biology_keywords, function(x) grepl(x, title1_lower)))
  biology2 <- any(sapply(biology_keywords, function(x) grepl(x, title2_lower)))
  
  if ((medical1 && !medical2 && !biology2) || (medical2 && !medical1 && !biology1)) {
    return(0.3)
  }
  
  return(1.0)
}

# 最终优化的DOI搜索函数
final_doi_search <- function(title, authors, year, journal) {
  tryCatch({
    clean_title <- optimized_normalize_text(title)
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    query_string <- paste(keywords, collapse = " ")
    
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=15", 
                   URLencode(query_string), as.numeric(year)-2, as.numeric(year)+2)
    
    response <- GET(url, user_agent("FinalOptimizedDOI/1.0"), timeout(30))
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$message$items) || length(content$message$items) == 0) return(NULL)
    
    items <- content$message$items
    best_match <- NULL
    best_score <- 0
    
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      candidate_title <- if (!is.null(item$title) && length(item$title) > 0) item$title[[1]] else ""
      candidate_journal <- ""
      candidate_year <- ""
      
      if (!is.null(item$`container-title`) && length(item$`container-title`) > 0) {
        candidate_journal <- item$`container-title`[[1]]
      }
      
      if (!is.null(item$`published-print`$`date-parts`)) {
        candidate_year <- item$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(item$`published-online`$`date-parts`)) {
        candidate_year <- item$`published-online`$`date-parts`[[1]][[1]]
      }
      
      title_sim <- optimized_similarity(title, candidate_title)
      journal_sim <- optimized_match_journals(journal, candidate_journal)
      year_sim <- optimized_match_year(year, candidate_year)
      subject_rel <- check_subject_relevance(title, candidate_title)
      
      final_score <- (title_sim * 0.5) + (journal_sim * 0.25) + (year_sim * 0.15) + (subject_rel * 0.1)
      
      if (title_sim >= 0.75 &&             
          journal_sim >= 0.4 &&            
          year_sim >= 0.5 &&               
          subject_rel >= 0.8 &&            
          final_score >= 0.65 &&       
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = item$DOI,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          subject_relevance = subject_rel,
          final_score = final_score,
          crossref_score = item$score
        )
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    return(NULL)
  })
}

# 全量DOI补全处理函数
process_all_missing_dois <- function() {
  cat("开始全量DOI补全处理...\n")
  
  input_file <- "data_repository/04_enhancement_reports/missing_doi_records.csv"
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  
  total_count <- nrow(data)
  cat(sprintf("总记录数: %d\n", total_count))
  cat("预计处理时间: 约 %.1f 小时\n\n", total_count * 1.5 / 3600)
  
  # 初始化结果数据框
  results <- data.frame(
    序号 = 1:total_count,
    UT = data$UT,
    原始标题 = data$TI,
    原始作者 = data$AU,
    原始年份 = data$PY,
    原始期刊 = data$SO,
    补全DOI = NA,
    匹配标题 = NA,
    匹配期刊 = NA,
    匹配年份 = NA,
    标题相似度 = NA,
    期刊匹配度 = NA,
    年份匹配度 = NA,
    学科相关性 = NA,
    最终评分 = NA,
    质量等级 = NA,
    补全状态 = "待处理",
    stringsAsFactors = FALSE
  )
  
  success_count <- 0
  excellent_count <- 0
  good_count <- 0
  acceptable_count <- 0
  
  # 分批处理
  batch_size <- 100
  total_batches <- ceiling(total_count / batch_size)
  
  for (batch in 1:total_batches) {
    start_idx <- (batch - 1) * batch_size + 1
    end_idx <- min(batch * batch_size, total_count)
    
    cat(sprintf("\n=== 批次 %d/%d (记录 %d-%d) ===\n", batch, total_batches, start_idx, end_idx))
    
    batch_start_time <- Sys.time()
    
    for (i in start_idx:end_idx) {
      if ((i - start_idx + 1) %% 20 == 0 || i == start_idx) {
        elapsed <- as.numeric(Sys.time() - batch_start_time, units = "mins")
        remaining_in_batch <- end_idx - i
        estimated_batch_time <- ifelse(i == start_idx, 0, elapsed / (i - start_idx + 1) * (end_idx - start_idx + 1))
        
        cat(sprintf("批次进度: %d/%d (总进度: %.1f%%) | 已用时: %.1f分钟 | 预计批次总时间: %.1f分钟\n", 
                    i - start_idx + 1, end_idx - start_idx + 1, 100 * i / total_count, 
                    elapsed, estimated_batch_time))
      }
      
      match_result <- final_doi_search(
        title = data$TI[i],
        authors = data$AU[i], 
        year = data$PY[i],
        journal = data$SO[i]
      )
      
      if (!is.null(match_result)) {
        results$补全DOI[i] <- match_result$doi
        results$匹配标题[i] <- match_result$title
        results$匹配期刊[i] <- match_result$journal
        results$匹配年份[i] <- match_result$year
        results$标题相似度[i] <- round(match_result$title_similarity, 3)
        results$期刊匹配度[i] <- round(match_result$journal_similarity, 3)
        results$年份匹配度[i] <- round(match_result$year_similarity, 3)
        results$学科相关性[i] <- round(match_result$subject_relevance, 3)
        results$最终评分[i] <- round(match_result$final_score, 3)
        results$补全状态[i] <- "成功"
        
        # 质量等级评定
        if (match_result$title_similarity >= 0.95 && match_result$final_score >= 0.85) {
          results$质量等级[i] <- "卓越"
          excellent_count <- excellent_count + 1
        } else if (match_result$title_similarity >= 0.85 && match_result$final_score >= 0.75) {
          results$质量等级[i] <- "优秀"
          good_count <- good_count + 1
        } else if (match_result$title_similarity >= 0.75 && match_result$final_score >= 0.65) {
          results$质量等级[i] <- "良好"
          acceptable_count <- acceptable_count + 1
        } else {
          results$质量等级[i] <- "可接受"
        }
        
        success_count <- success_count + 1
      } else {
        results$补全状态[i] <- "未找到匹配"
        results$质量等级[i] <- "未补全"
      }
      
      Sys.sleep(1.0)  # API限制
    }
    
    # 每批次后保存中间结果和统计
    temp_file <- file.path("data_repository/04_enhancement_reports", sprintf("full_batch_%d.csv", batch))
    write.csv(results[1:end_idx, ], temp_file, row.names = FALSE)
    
    batch_time <- as.numeric(Sys.time() - batch_start_time, units = "mins")
    current_success_rate <- 100 * success_count / end_idx
    
    cat(sprintf("批次 %d 完成 | 用时: %.1f分钟 | 当前成功率: %.2f%% (%d/%d)\n", 
                batch, batch_time, current_success_rate, success_count, end_idx))
    cat(sprintf("当前质量分布: 卓越=%d, 优秀=%d, 良好=%d\n", excellent_count, good_count, acceptable_count))
    
    # 批次间休息
    if (batch < total_batches) {
      cat("批次间休息 30 秒...\n")
      Sys.sleep(30)
    }
  }
  
  return(results)
}

# 自动结果分析评估函数
auto_analyze_results <- function(results) {
  cat("\n=== 自动结果分析评估 ===\n")
  
  total_count <- nrow(results)
  success_count <- sum(results$补全状态 == "成功", na.rm = TRUE)
  success_rate <- 100 * success_count / total_count
  
  excellent_count <- sum(results$质量等级 == "卓越", na.rm = TRUE)
  good_count <- sum(results$质量等级 == "优秀", na.rm = TRUE)
  acceptable_count <- sum(results$质量等级 == "良好", na.rm = TRUE)
  
  # 基本统计
  cat(sprintf("总记录数: %d\n", total_count))
  cat(sprintf("成功补全: %d (%.2f%%)\n", success_count, success_rate))
  cat(sprintf("卓越质量: %d (%.2f%%)\n", excellent_count, 100 * excellent_count / total_count))
  cat(sprintf("优秀质量: %d (%.2f%%)\n", good_count, 100 * good_count / total_count))
  cat(sprintf("良好质量: %d (%.2f%%)\n", acceptable_count, 100 * acceptable_count / total_count))
  
  # 质量评估
  high_quality_count <- excellent_count + good_count
  high_quality_rate <- 100 * high_quality_count / success_count
  
  cat(sprintf("\n=== 质量评估 ===\n"))
  cat(sprintf("高质量匹配率: %.1f%% (%d/%d成功记录)\n", high_quality_rate, high_quality_count, success_count))
  
  # 自动评估结论
  if (success_rate >= 15 && high_quality_rate >= 70) {
    assessment <- "优秀"
    recommendation <- "结果质量优秀，可直接用于学术研究"
  } else if (success_rate >= 10 && high_quality_rate >= 60) {
    assessment <- "良好"
    recommendation <- "结果质量良好，建议对良好质量记录进行人工验证"
  } else if (success_rate >= 5) {
    assessment <- "可接受"
    recommendation <- "结果质量可接受，建议仔细验证所有匹配记录"
  } else {
    assessment <- "需要改进"
    recommendation <- "成功率偏低，建议调整算法参数或数据预处理"
  }
  
  cat(sprintf("\n=== 自动评估结论 ===\n"))
  cat(sprintf("整体评估: %s\n", assessment))
  cat(sprintf("使用建议: %s\n", recommendation))
  
  # 详细分析
  if (success_count > 0) {
    successful_results <- results[results$补全状态 == "成功", ]
    
    avg_title_sim <- mean(successful_results$标题相似度, na.rm = TRUE)
    avg_journal_sim <- mean(successful_results$期刊匹配度, na.rm = TRUE)
    avg_final_score <- mean(successful_results$最终评分, na.rm = TRUE)
    
    cat(sprintf("\n=== 成功匹配质量分析 ===\n"))
    cat(sprintf("平均标题相似度: %.3f\n", avg_title_sim))
    cat(sprintf("平均期刊匹配度: %.3f\n", avg_journal_sim))
    cat(sprintf("平均最终评分: %.3f\n", avg_final_score))
    
    # 年份分布分析
    year_success <- table(successful_results$原始年份)
    cat(sprintf("\n成功补全的年份分布 (前5年):\n"))
    print(head(sort(year_success, decreasing = TRUE), 5))
  }
  
  return(list(
    assessment = assessment,
    recommendation = recommendation,
    success_rate = success_rate,
    high_quality_rate = high_quality_rate,
    total_count = total_count,
    success_count = success_count,
    excellent_count = excellent_count,
    good_count = good_count,
    acceptable_count = acceptable_count
  ))
}

# 主执行函数
main_execution <- function() {
  cat("开始全量DOI补全与自动评估...\n\n")
  
  # 执行全量处理
  results <- process_all_missing_dois()
  
  # 保存最终结果
  final_file <- "data_repository/04_enhancement_reports/COMPLETE_DOI_RESULTS.csv"
  write.csv(results, final_file, row.names = FALSE)
  cat(sprintf("\n✅ 完整结果已保存: %s\n", final_file))
  
  # 自动分析评估
  analysis <- auto_analyze_results(results)
  
  # 保存分析报告
  analysis_file <- "data_repository/04_enhancement_reports/AUTO_ANALYSIS_REPORT.txt"
  
  report_content <- sprintf('DOI补全自动分析报告
生成时间: %s

=== 处理统计 ===
总记录数: %d
成功补全: %d (%.2f%%)
卓越质量: %d (%.2f%%)
优秀质量: %d (%.2f%%)
良好质量: %d (%.2f%%)

=== 质量评估 ===
整体评估: %s
高质量匹配率: %.1f%%
使用建议: %s

=== 文件清单 ===
- COMPLETE_DOI_RESULTS.csv: 完整补全结果
- AUTO_ANALYSIS_REPORT.txt: 本分析报告
', 
    format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    analysis$total_count, analysis$success_count, analysis$success_rate,
    analysis$excellent_count, 100 * analysis$excellent_count / analysis$total_count,
    analysis$good_count, 100 * analysis$good_count / analysis$total_count,
    analysis$acceptable_count, 100 * analysis$acceptable_count / analysis$total_count,
    analysis$assessment, analysis$high_quality_rate, analysis$recommendation
  )
  
  writeLines(report_content, analysis_file)
  cat(sprintf("✅ 分析报告已保存: %s\n", analysis_file))
  
  cat(sprintf("\n🎉 全量DOI补全与评估完成！\n"))
  cat(sprintf("整体评估: %s\n", analysis$assessment))
  cat(sprintf("成功率: %.2f%% | 高质量率: %.1f%%\n", analysis$success_rate, analysis$high_quality_rate))
  
  return(list(results = results, analysis = analysis))
}

# 执行全量处理
final_output <- main_execution()

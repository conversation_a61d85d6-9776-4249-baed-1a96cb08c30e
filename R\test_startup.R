# 测试脚本启动和基本功能

cat("开始测试脚本...\n")

# 测试包加载
cat("测试包加载...\n")
tryCatch({
  library(tidyverse, quietly = TRUE)
  cat("✓ tidyverse 加载成功\n")
}, error = function(e) {
  cat("✗ tidyverse 加载失败:", e$message, "\n")
})

tryCatch({
  library(here, quietly = TRUE)
  cat("✓ here 加载成功\n")
}, error = function(e) {
  cat("✗ here 加载失败:", e$message, "\n")
})

tryCatch({
  library(httr, quietly = TRUE)
  cat("✓ httr 加载成功\n")
}, error = function(e) {
  cat("✗ httr 加载失败:", e$message, "\n")
})

# 测试文件路径
cat("测试文件路径...\n")
input_file <- here::here("data_repository", "04_enhancement_reports", "missing_doi_records.csv")
cat("输入文件路径:", input_file, "\n")

if (file.exists(input_file)) {
  cat("✓ 输入文件存在\n")
  
  # 测试文件读取
  tryCatch({
    data <- read.csv(input_file, nrows = 5)
    cat("✓ 文件读取成功，前5行数据:\n")
    print(head(data))
  }, error = function(e) {
    cat("✗ 文件读取失败:", e$message, "\n")
  })
} else {
  cat("✗ 输入文件不存在\n")
}

# 测试网络连接
cat("测试网络连接...\n")
tryCatch({
  response <- httr::GET("https://api.crossref.org/", httr::timeout(10))
  if (httr::status_code(response) == 200) {
    cat("✓ 网络连接正常\n")
  } else {
    cat("✗ 网络连接异常，状态码:", httr::status_code(response), "\n")
  }
}, error = function(e) {
  cat("✗ 网络连接失败:", e$message, "\n")
})

cat("测试完成\n")

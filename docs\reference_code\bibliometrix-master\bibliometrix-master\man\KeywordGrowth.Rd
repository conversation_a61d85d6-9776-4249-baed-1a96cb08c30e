% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/keywordGrowth.R
\name{KeywordGrowth}
\alias{KeywordGrowth}
\title{Yearly occurrences of top keywords/terms}
\usage{
KeywordGrowth(
  M,
  Tag = "ID",
  sep = ";",
  top = 10,
  cdf = TRUE,
  remove.terms = NULL,
  synonyms = NULL
)
}
\arguments{
\item{M}{is a data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to articles and variables to Field Tag in the original WoS or SCOPUS file.}

\item{Tag}{is a character object. It indicates one of the keyword field tags of the
standard ISI WoS Field Tag codify (ID or DE) or a field tag created by \code{\link{termExtraction}} function (TI_TM, AB_TM, etc.).}

\item{sep}{is the field separator character. This character separates strings in each keyword column of the data frame. The default is \code{sep = ";"}.}

\item{top}{is a numeric. It indicates the number of top keywords to analyze. The default value is 10.}

\item{cdf}{is a logical. If TRUE, the function calculates the cumulative occurrences distribution.}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}
}
\value{
an object of class \code{data.frame}
}
\description{
It calculates yearly occurrences of top keywords/terms.
}
\examples{

data(scientometrics, package = "bibliometrixData")
topKW=KeywordGrowth(scientometrics, Tag = "ID", sep = ";", top=5, cdf=TRUE)
topKW

# Plotting results
\dontrun{
install.packages("reshape2")
library(reshape2)
library(ggplot2)
DF=melt(topKW, id='Year')
ggplot(DF,aes(Year,value, group=variable, color=variable))+geom_line
}

}

# 监控DOI补全进度

cat("检查DOI补全进度...\n")

# 检查临时文件
temp_files <- list.files("C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/04_enhancement_reports", 
                        pattern = "doi_completion_temp_", full.names = TRUE)

if (length(temp_files) > 0) {
  # 找到最新的临时文件
  file_info <- file.info(temp_files)
  latest_file <- rownames(file_info)[which.max(file_info$mtime)]
  
  cat(sprintf("最新临时文件: %s\n", basename(latest_file)))
  cat(sprintf("修改时间: %s\n", file_info[latest_file, "mtime"]))
  
  # 读取并分析最新文件
  if (file.exists(latest_file)) {
    data <- read.csv(latest_file, stringsAsFactors = FALSE)
    
    total_records <- nrow(data)
    completed_records <- sum(data$status %in% c("success", "failed"))
    success_count <- sum(data$status == "success", na.rm = TRUE)
    
    cat(sprintf("\n=== 当前进度 ===\n"))
    cat(sprintf("总记录数: %d\n", total_records))
    cat(sprintf("已处理: %d (%.1f%%)\n", completed_records, 100 * completed_records / total_records))
    cat(sprintf("成功补全: %d (%.1f%%)\n", success_count, 100 * success_count / total_records))
    cat(sprintf("处理失败: %d\n", completed_records - success_count))
    
    if (completed_records < total_records) {
      cat(sprintf("剩余: %d 条记录\n", total_records - completed_records))
    } else {
      cat("所有记录处理完成！\n")
    }
  }
} else {
  cat("未找到临时文件\n")
}

# 检查最终结果文件
final_file <- "C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/04_enhancement_reports/doi_completion_full_final.csv"
if (file.exists(final_file)) {
  cat(sprintf("\n发现最终结果文件: %s\n", final_file))
  
  final_data <- read.csv(final_file, stringsAsFactors = FALSE)
  total_final <- nrow(final_data)
  success_final <- sum(final_data$status == "success", na.rm = TRUE)
  
  cat(sprintf("最终统计: %d/%d 成功 (%.1f%%)\n", success_final, total_final, 100 * success_final / total_final))
} else {
  cat("\n最终结果文件尚未生成\n")
}

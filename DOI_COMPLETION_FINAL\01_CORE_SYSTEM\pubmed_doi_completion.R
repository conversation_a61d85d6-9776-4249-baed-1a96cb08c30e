# PubMed DOI补全系统
# 基于PubMed E-utilities API的DOI补全功能

library(httr)
library(jsonlite)
library(stringdist)
library(xml2)

# 加载核心函数
source("doi_completion_core.R")

cat("=== PubMed DOI补全系统 ===\n")
cat("🎯 专注生物医学文献的DOI补全\n")
cat("📊 数据源: PubMed (3500万+生物医学文献)\n")
cat("🔧 API: NCBI E-utilities\n\n")

# === PubMed特定的学科相关性检查 ===
check_biomedical_relevance <- function(title1, title2) {
  # 生物医学关键词库
  biomedical_keywords <- c(
    # 医学基础
    "medical", "medicine", "clinical", "patient", "treatment", "therapy", "disease", 
    "diagnosis", "healthcare", "health", "hospital", "doctor", "physician",
    
    # 解剖学
    "anatomy", "muscle", "bone", "tissue", "cell", "organ", "blood", "brain",
    "heart", "lung", "liver", "kidney", "skin", "nerve", "joint",
    
    # 专科医学
    "dental", "oral", "orthodontic", "surgery", "oncology", "cardiology",
    "neurology", "psychiatry", "pediatric", "geriatric", "radiology",
    
    # 生物学
    "biology", "molecular", "genetic", "protein", "dna", "rna", "gene",
    "chromosome", "enzyme", "hormone", "antibody", "virus", "bacteria",
    
    # 药学
    "drug", "pharmaceutical", "medication", "pharmacology", "toxicology",
    "vaccine", "antibiotic", "chemotherapy", "dosage",
    
    # 研究方法
    "clinical trial", "epidemiology", "biostatistics", "imaging", "mri", 
    "ct scan", "ultrasound", "biopsy", "laboratory", "biomarker"
  )
  
  title1_lower <- tolower(title1)
  title2_lower <- tolower(title2)
  
  # 检查生物医学关键词匹配
  bio_match1 <- any(sapply(biomedical_keywords, function(x) grepl(x, title1_lower)))
  bio_match2 <- any(sapply(biomedical_keywords, function(x) grepl(x, title2_lower)))
  
  if (bio_match1 && bio_match2) {
    return(1.0)  # 都是生物医学相关
  } else if (bio_match1 || bio_match2) {
    return(0.7)  # 一个是生物医学相关
  } else {
    return(0.3)  # 都不是明显的生物医学相关
  }
}

# === PubMed期刊匹配优化 ===
calculate_journal_similarity_pubmed <- function(journal1, journal2) {
  if (is.na(journal1) || is.na(journal2) || journal1 == "" || journal2 == "") return(0.2)
  
  # PubMed期刊名称标准化
  normalize_pubmed_journal <- function(journal) {
    journal <- tolower(journal)
    # 移除常见的医学期刊缩写和格式
    journal <- gsub("^the\\s+", "", journal)
    journal <- gsub("\\s*\\(.*\\)$", "", journal)
    journal <- gsub("\\s*journal\\s*of\\s*", " j ", journal)
    journal <- gsub("\\s*american\\s*", " am ", journal)
    journal <- gsub("\\s*international\\s*", " int ", journal)
    journal <- gsub("\\s*european\\s*", " eur ", journal)
    journal <- gsub("\\s*british\\s*", " br ", journal)
    journal <- gsub("[[:punct:]]", " ", journal)
    journal <- gsub("\\s+", " ", journal)
    return(trimws(journal))
  }
  
  norm1 <- normalize_pubmed_journal(journal1)
  norm2 <- normalize_pubmed_journal(journal2)
  
  if (norm1 == norm2) return(1.0)
  
  # 基础相似度
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  
  # 医学期刊特殊匹配
  words1 <- strsplit(norm1, "\\s+")[[1]][nchar(strsplit(norm1, "\\s+")[[1]]) > 1]
  words2 <- strsplit(norm2, "\\s+")[[1]][nchar(strsplit(norm2, "\\s+")[[1]]) > 1]
  
  if (length(words1) > 0 && length(words2) > 0) {
    common_words <- sum(words1 %in% words2)
    if (common_words > 0) {
      keyword_similarity <- common_words / max(length(words1), length(words2))
      similarity <- max(similarity, keyword_similarity)
    }
  }
  
  return(similarity)
}

# === PubMed API搜索函数 ===
search_doi_pubmed <- function(title, authors, year, journal) {
  tryCatch({
    # 构建PubMed查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 2]
    if (length(keywords) > 5) keywords <- keywords[1:5]
    if (length(keywords) == 0) return(NULL)
    
    # 构建PubMed查询字符串
    search_terms <- paste(keywords, collapse = " AND ")
    if (!is.na(year) && year != "") {
      year_range <- sprintf("(%s[PDAT]:%s[PDAT])", as.numeric(year)-2, as.numeric(year)+2)
      search_terms <- paste(search_terms, "AND", year_range)
    }
    
    # Step 1: ESearch - 搜索获取PMID列表
    esearch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=%s&retmax=20&retmode=json",
                          URLencode(search_terms))
    
    cat(sprintf("PubMed搜索: %s\n", search_terms))
    
    search_response <- GET(esearch_url, 
                          user_agent("DOI_Completion_PubMed/1.0"),
                          timeout(30))
    
    if (status_code(search_response) != 200) {
      cat("PubMed ESearch API错误, 状态码:", status_code(search_response), "\n")
      return(NULL)
    }
    
    search_content <- fromJSON(rawToChar(search_response$content))
    
    if (is.null(search_content$esearchresult$idlist) || 
        length(search_content$esearchresult$idlist) == 0) {
      cat("PubMed未返回搜索结果\n")
      return(NULL)
    }
    
    pmids <- search_content$esearchresult$idlist
    cat(sprintf("PubMed返回 %d 个PMID\n", length(pmids)))
    
    # Step 2: ESummary - 获取文献摘要信息
    pmid_list <- paste(pmids, collapse = ",")
    esummary_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=pubmed&id=%s&retmode=json",
                           pmid_list)
    
    summary_response <- GET(esummary_url,
                           user_agent("DOI_Completion_PubMed/1.0"),
                           timeout(30))
    
    if (status_code(summary_response) != 200) {
      cat("PubMed ESummary API错误, 状态码:", status_code(summary_response), "\n")
      return(NULL)
    }
    
    summary_content <- fromJSON(rawToChar(summary_response$content))
    
    if (is.null(summary_content$result)) {
      cat("PubMed ESummary未返回结果\n")
      return(NULL)
    }
    
    best_match <- NULL
    best_score <- 0
    
    # 评估每个候选结果
    for (pmid in pmids) {
      if (pmid %in% names(summary_content$result) && pmid != "uids") {
        item <- summary_content$result[[pmid]]

        # 安全提取PubMed数据字段
        candidate_title <- ""
        if (is.list(item) && !is.null(item$title)) {
          candidate_title <- as.character(item$title)
        }

        candidate_journal <- ""
        if (is.list(item)) {
          if (!is.null(item$fulljournalname)) {
            candidate_journal <- as.character(item$fulljournalname)
          } else if (!is.null(item$source)) {
            candidate_journal <- as.character(item$source)
          }
        }

        # 提取年份
        candidate_year <- ""
        if (is.list(item) && !is.null(item$pubdate)) {
          pubdate_str <- as.character(item$pubdate)
          # PubMed日期格式: "2020 Jan 15" 或 "2020"
          year_match <- regmatches(pubdate_str, regexpr("\\d{4}", pubdate_str))
          if (length(year_match) > 0) {
            candidate_year <- year_match[1]
          }
        }

        # 提取DOI
        candidate_doi <- ""
        if (is.list(item) && !is.null(item$articleids) && is.list(item$articleids)) {
          for (j in 1:length(item$articleids)) {
            id_info <- item$articleids[[j]]
            if (is.list(id_info) && !is.null(id_info$idtype) &&
                as.character(id_info$idtype) == "doi" &&
                !is.null(id_info$value)) {
              candidate_doi <- as.character(id_info$value)
              break
            }
          }
        }
        
        # 如果没有DOI，跳过
        if (candidate_doi == "") {
          next
        }
        
        # 计算相似度
        title_sim <- calculate_title_similarity(title, candidate_title)
        journal_sim <- calculate_journal_similarity_pubmed(journal, candidate_journal)
        year_sim <- calculate_year_similarity(year, candidate_year)
        bio_rel <- check_biomedical_relevance(title, candidate_title)
        
        # PubMed特定的权重分配 (更重视生物医学相关性)
        final_score <- (title_sim * 0.55) + (journal_sim * 0.25) + 
                       (year_sim * 0.10) + (bio_rel * 0.10)
        
        cat(sprintf("PMID %s: T=%.3f, J=%.3f, Y=%.3f, B=%.3f, 总分=%.3f\n",
                    pmid, title_sim, journal_sim, year_sim, bio_rel, final_score))
        
        # PubMed优化的接受条件
        if (title_sim >= 0.70 &&           # 标题相似度阈值
            journal_sim >= 0.30 &&         # 期刊匹配度阈值 (降低)
            year_sim >= 0.30 &&            # 年份匹配度阈值 (降低)
            bio_rel >= 0.30 &&             # 生物医学相关性阈值 (降低)
            final_score >= 0.60 &&         # 综合评分阈值
            final_score > best_score) {
          
          best_score <- final_score
          best_match <- list(
            doi = candidate_doi,
            title = candidate_title,
            journal = candidate_journal,
            year = candidate_year,
            pmid = pmid,
            title_similarity = title_sim,
            journal_similarity = journal_sim,
            year_similarity = year_sim,
            biomedical_relevance = bio_rel,
            final_score = final_score,
            source = "pubmed"
          )
          
          cat(sprintf("  ✅ 新的最佳匹配! DOI: %s (PMID: %s)\n", candidate_doi, pmid))
        }
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("PubMed API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 测试PubMed系统 ===
test_pubmed_doi <- function() {
  cat("\n=== PubMed DOI补全测试 ===\n")
  
  # 生物医学测试用例
  test_cases <- list(
    list(
      title = "MR imaging of muscles of mastication",
      authors = "Smith J",
      year = 1995,
      journal = "American Journal of Neuroradiology",
      description = "医学影像学"
    ),
    list(
      title = "Clinical applications of artificial intelligence in healthcare",
      authors = "Johnson A",
      year = 2020,
      journal = "Nature Medicine",
      description = "AI医疗应用"
    ),
    list(
      title = "Molecular mechanisms of cancer therapy",
      authors = "Brown C",
      year = 2019,
      journal = "Cell",
      description = "癌症分子机制"
    )
  )
  
  success_count <- 0
  
  for (i in 1:length(test_cases)) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n--- 测试案例 %d: %s ---\n", i, test_case$description))
    cat(sprintf("标题: %s\n", test_case$title))
    
    result <- search_doi_pubmed(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal
    )
    
    if (!is.null(result)) {
      success_count <- success_count + 1
      quality <- assess_quality(result$title_similarity, result$final_score)
      cat(sprintf("✅ PubMed成功: %s (PMID: %s, 质量: %s)\n", 
                  result$doi, result$pmid, quality))
      cat(sprintf("   生物医学相关性: %.3f\n", result$biomedical_relevance))
    } else {
      cat("❌ PubMed未找到匹配\n")
    }
    
    if (i < length(test_cases)) {
      cat("等待3秒...\n")
      Sys.sleep(3)
    }
  }
  
  success_rate <- 100 * success_count / length(test_cases)
  cat(sprintf("\n📊 PubMed测试结果: %.1f%% 成功率 (%d/%d)\n", 
              success_rate, success_count, length(test_cases)))
  
  return(list(
    success_count = success_count,
    success_rate = success_rate
  ))
}

cat("✅ PubMed DOI补全系统已加载\n")
cat("📋 主要函数:\n")
cat("  - search_doi_pubmed()     : PubMed DOI搜索\n")
cat("  - test_pubmed_doi()       : PubMed系统测试\n")

# 自动执行测试
cat("\n🚀 开始PubMed系统测试...\n")
pubmed_test_result <- test_pubmed_doi()

cat(sprintf("\n🎯 PubMed测试完成!\n"))
cat(sprintf("成功率: %.1f%%\n", pubmed_test_result$success_rate))

if (pubmed_test_result$success_rate >= 33.3) {
  cat("🎉 PubMed表现良好，适合集成到多引擎系统！\n")
} else if (pubmed_test_result$success_rate > 0) {
  cat("📈 PubMed有一定效果，可作为生物医学文献的补充。\n")
} else {
  cat("⚠️  PubMed在当前测试中表现不佳，可能需要调整参数。\n")
}

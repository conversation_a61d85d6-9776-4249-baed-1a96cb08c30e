# 03_process_citespace_data.R
# 将CiteSpace处理后的txt文件转换为R数据格式
# 参考bibliometrix的convert2df.Rd实现

# 加载必要的包
library(tidyverse)
library(here)

# 设置路径
citespace_output_dir <- "C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/01_baseline_datasets/citespace_processed"
output_dir <- here("data_repository/01_baseline_datasets/citespace_processed")

# 创建输出目录（如果不存在）
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

# 获取所有年度文件
citespace_files <- list.files(
  path = citespace_output_dir,
  pattern = "^download\\d{4}u\\d+\\.txt$",
  full.names = TRUE
)

cat("找到的文件：\n")
print(citespace_files)

# 定义字段映射
field_mapping <- c(
  # 基本字段
  "PT" = "Publication Type",
  "AU" = "Authors",
  "AF" = "Author Full Name",
  "TI" = "Document Title",
  "SO" = "Publication Name",
  "JI" = "ISO Source Abbreviation",
  "DT" = "Document Type",
  "DE" = "Authors' Keywords",
  "ID" = "Keywords Plus",
  "AB" = "Abstract",
  "C1" = "Author Address",
  "RP" = "Reprint Address",
  "CR" = "Cited References",
  "TC" = "Times Cited",
  "PY" = "Year",
  "SC" = "Subject Category",
  "UT" = "Unique Article Identifier",
  "DB" = "Database",
  
  # 扩展字段
  "LA" = "Language",
  "NR" = "Number of References",
  "Z9" = "Total Times Cited",
  "U1" = "Usage Count (Last 180 Days)",
  "U2" = "Usage Count (Since 2013)",
  "PU" = "Publisher",
  "PI" = "Publisher City",
  "PA" = "Publisher Address",
  "SN" = "ISSN",
  "EI" = "eISSN",
  "J9" = "Journal Abbreviation",
  "PD" = "Publication Date",
  "VL" = "Volume",
  "IS" = "Issue",
  "BP" = "Beginning Page",
  "EP" = "Ending Page",
  "DI" = "DOI",
  "PG" = "Page Count",
  "WC" = "Web of Science Categories",
  "GA" = "Document Delivery Number",
  "PM" = "PubMed ID",
  "DA" = "Date Added to Database",
  "ER" = "End of Record"
)

# 读取并处理文件
process_citespace_file <- function(file) {
  cat(sprintf("\n处理文件：%s\n", basename(file)))
  
  # 读取文件内容
  lines <- readLines(file, encoding = "UTF-8")
  
  # 初始化结果列表
  result <- list()
  current_record <- list()
  in_record <- FALSE
  record_count <- 0
  
  # 处理每一行
  for (i in seq_along(lines)) {
    line <- lines[i]
    # 跳过空行
    if (trimws(line) == "") next
    
    # 检查是否是新的字段
    field_code <- substr(line, 1, 2)
    
    # 检查是否是新的记录开始
    if (field_code == "PT") {
      # 如果已经在处理一条记录，保存它
      if (in_record && length(current_record) > 0) {
        # 检查记录是否完整（必须包含PT和ER）
        if ("PT" %in% names(current_record) && "ER" %in% names(current_record)) {
          # 检查字段值是否为空
          for (field in names(current_record)) {
            if (is.null(current_record[[field]]) || current_record[[field]] == "") {
              current_record[[field]] <- NA
            }
          }
          result <- c(result, list(current_record))
          record_count <- record_count + 1
        }
      }
      # 开始新记录
      current_record <- list()
      in_record <- TRUE
      field_value <- trimws(substr(line, 3, nchar(line)))
      current_record[[field_code]] <- field_value
    }
    # 检查是否是记录结束
    else if (field_code == "ER") {
      if (in_record) {
        field_value <- trimws(substr(line, 3, nchar(line)))
        current_record[[field_code]] <- field_value
        # 保存当前记录
        if ("PT" %in% names(current_record)) {
          # 检查字段值是否为空
          for (field in names(current_record)) {
            if (is.null(current_record[[field]]) || current_record[[field]] == "") {
              current_record[[field]] <- NA
            }
          }
          result <- c(result, list(current_record))
          record_count <- record_count + 1
        }
        in_record <- FALSE
        current_record <- list()
      }
    }
    # 处理记录中的其他字段
    else if (in_record) {
      if (field_code != "  ") {  # 不是缩进的行
        field_value <- trimws(substr(line, 3, nchar(line)))
        current_record[[field_code]] <- field_value
      } else if (length(current_record) > 0) {
        # 继续当前字段的值
        last_field <- names(current_record)[length(current_record)]
        current_record[[last_field]] <- paste(current_record[[last_field]], trimws(line))
      }
    }
  }
  
  # 如果文件结束时还有未保存的记录，保存它
  if (in_record && length(current_record) > 0) {
    if ("PT" %in% names(current_record) && "ER" %in% names(current_record)) {
      result <- c(result, list(current_record))
      record_count <- record_count + 1
    }
  }
  
  cat(sprintf("提取的记录数：%d\n", record_count))
  
  # 转换为数据框
  if (length(result) > 0) {
    # 获取所有可能的字段名
    all_fields <- unique(unlist(lapply(result, names)))
    
    # 确保每个记录都有所有字段
    result <- lapply(result, function(x) {
      for (field in all_fields) {
        if (is.null(x[[field]])) {
          x[[field]] <- NA
        }
      }
      x
    })
    
    # 转换为数据框
    df <- do.call(rbind, lapply(result, function(x) {
      as.data.frame(x, stringsAsFactors = FALSE)
    }))
    
    # 添加文件信息
    df$source_file <- basename(file)
    df$file_path <- file  # 添加完整文件路径
    
    return(df)
  } else {
    cat("没有提取到记录\n")
    return(NULL)
  }
}

# 处理所有文件
cat("\n开始处理所有文件...\n")
citespace_data_list <- list()
for (file in citespace_files) {
  result <- process_citespace_file(file)
  if (!is.null(result)) {
    citespace_data_list[[basename(file)]] <- result
  }
}

# 合并所有数据框
cat("\n合并数据框...\n")
if (length(citespace_data_list) > 0) {
  citespace_data <- bind_rows(citespace_data_list)
  
  # 添加数据来源标记
  citespace_processed <- citespace_data %>%
    mutate(
      data_source = "CiteSpace",
      processed_date = Sys.Date(),
      DB = "WOS"  # 添加数据库标记
    )
  
  # 检查记录唯一性
  cat("\n检查记录唯一性：\n")
  cat("原始记录数：", nrow(citespace_processed), "\n")
  
  # 查找重复记录
  duplicates <- citespace_processed %>%
    group_by(UT) %>%
    filter(n() > 1) %>%
    arrange(UT, source_file)
  
  if(nrow(duplicates) > 0) {
    cat("\n发现重复记录：\n")
    cat("重复记录总数：", nrow(duplicates), "\n")
    cat("唯一UT数：", n_distinct(duplicates$UT), "\n")
    
    # 按文件统计重复记录
    cat("\n各文件中的重复记录数：\n")
    file_duplicates <- duplicates %>%
      group_by(source_file) %>%
      summarise(
        duplicate_count = n(),
        unique_ut_count = n_distinct(UT)
      ) %>%
      arrange(desc(duplicate_count))
    print(file_duplicates)
    
    # 显示所有重复记录的详细信息
    cat("\n重复记录详细信息：\n")
    duplicate_details <- duplicates %>%
      select(UT, TI, PY, source_file) %>%
      arrange(UT, source_file)
    print(duplicate_details, width = Inf)  # 显示完整文件名
  }
  
  # 去重 - 简化去重逻辑
  citespace_processed <- citespace_processed %>%
    group_by(UT) %>%  
    slice(1) %>%  # 只保留第一条记录
    ungroup()
  
  cat("去重后记录数：", nrow(citespace_processed), "\n")
  
  # 保存为R数据格式
  saveRDS(
    citespace_processed,
    file.path(output_dir, "datac_citespace_default.rds")
  )
  
  # 输出处理结果
  cat("\nCiteSpace数据处理完成：\n")
  cat("处理的文件数：", length(citespace_files), "\n")
  cat("总记录数：", nrow(citespace_processed), "\n")
  cat("处理日期：", Sys.Date(), "\n")
  cat("输出文件：", file.path(output_dir, "datac_citespace_default.rds"), "\n")
  
  # 输出年度分布
  cat("\n年度分布：\n")
  # 创建完整的年份序列
  all_years <- seq(min(as.numeric(citespace_processed$PY)), max(as.numeric(citespace_processed$PY)))
  year_dist <- citespace_processed %>%
    mutate(PY = as.numeric(PY)) %>%  # 将PY转换为数值型
    group_by(PY) %>%
    summarise(
      n = n(),
      .groups = "drop"
    ) %>%
    right_join(data.frame(PY = all_years), by = "PY") %>%
    mutate(
      n = replace_na(n, 0),
      PY = as.character(PY)  # 转回字符型以保持一致性
    ) %>%
    arrange(PY)
  
  # 格式化输出
  cat("\n年份\t记录数\n")
  cat("----------------\n")
  for(i in 1:nrow(year_dist)) {
    cat(sprintf("%s\t%d\n", year_dist$PY[i], year_dist$n[i]))
  }
  cat("----------------\n")
  cat(sprintf("总计\t%d\n", sum(year_dist$n)))
  
  # 输出字段统计
  cat("\n字段统计：\n")
  # 只统计实际存在的字段
  original_fields <- c("PT", "AU", "AF", "TI", "SO", "LA", "DT", "DE", "ID", "AB", 
                      "C1", "C3", "RP", "CR", "NR", "TC", "Z9", "U1", "U2", "PU", 
                      "PI", "PA", "SN", "EI", "J9", "JI", "PD", "PY", "VL", "IS", 
                      "BP", "EP", "DI", "PG", "WC", "SC", "GA", "UT", "PM", "DA", 
                      "EM", "FU", "EA")
  
  # 字段含义映射
  field_meanings <- c(
    "PT" = "出版物类型", "AU" = "作者", "AF" = "作者全名", "TI" = "标题",
    "SO" = "出版物来源", "LA" = "语言", "DT" = "文档类型", "DE" = "作者关键词",
    "ID" = "关键词Plus", "AB" = "摘要", "C1" = "作者地址", "C3" = "会议标题(C3)",
    "RP" = "通讯作者地址", "CR" = "引用参考文献", "NR" = "参考文献数量",
    "TC" = "被引频次", "Z9" = "总引用次数(WoS)", "U1" = "过去180天的使用计数",
    "U2" = "自2013年以来的使用计数", "PU" = "出版商", "PI" = "出版商城市",
    "PA" = "出版商地址", "SN" = "ISSN", "EI" = "电子ISSN", "J9" = "期刊缩写(J9)",
    "JI" = "ISO期刊缩写(JI)", "PD" = "出版日期(月/日)", "PY" = "出版年份", "VL" = "卷号",
    "IS" = "期号", "BP" = "起始页码", "EP" = "结束页码", "DI" = "DOI",
    "PG" = "页数", "WC" = "Web of Science类别", "SC" = "WoS学科类别",
    "GA" = "团体作者", "UT" = "唯一标识符(WoS)", "PM" = "PubMed ID",
    "DA" = "出版日期(数据库记录)", "EM" = "电子邮件地址", "FU" = "资助机构与编号",
    "EA" = "提前访问日期"
  )
  
  # 计算字段统计
  field_stats <- data.frame(
    Field = original_fields,
    Field_Meaning = field_meanings[original_fields],
    NonEmpty = sapply(original_fields, function(x) {
      if(x %in% names(citespace_processed)) {
        sum(!is.na(citespace_processed[[x]]) & citespace_processed[[x]] != "")
      } else {
        0
      }
    }),
    Missing_Count = sapply(original_fields, function(x) {
      if(x %in% names(citespace_processed)) {
        sum(is.na(citespace_processed[[x]]) | citespace_processed[[x]] == "")
      } else {
        nrow(citespace_processed)
      }
    })
  ) %>%
    mutate(
      NonEmpty_Percentage = round(NonEmpty / nrow(citespace_processed) * 100, 2),
      Missing_Percentage = round(Missing_Count / nrow(citespace_processed) * 100, 2)
    ) %>%
    arrange(desc(Missing_Percentage))  # 按缺失率降序排列
  
  # 格式化输出
  cat("\n字段\t字段含义\t\t非空数\t缺失数\t非空率(%)\t缺失率(%)\n")
  cat("----------------------------------------------------------------\n")
  for(i in 1:nrow(field_stats)) {
    cat(sprintf("%s\t%s\t%d\t%d\t%.2f\t%.2f\n", 
                field_stats$Field[i],
                field_stats$Field_Meaning[i],
                field_stats$NonEmpty[i],
                field_stats$Missing_Count[i],
                field_stats$NonEmpty_Percentage[i],
                field_stats$Missing_Percentage[i]))
  }
  cat("----------------------------------------------------------------\n")
  cat(sprintf("总计\t\t\t%d\t%d\t100.00\t0.00\n", 
              sum(field_stats$NonEmpty), 
              sum(field_stats$Missing_Count)))
} else {
  cat("\n错误：没有成功处理任何文件\n")
} 
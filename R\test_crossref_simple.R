# 简化的Crossref API测试
# 测试基本的DOI查询功能

# 加载必要的包
required_packages <- c("rcrossref", "httr", "jsonlite")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

# 测试已知DOI的查询
test_known_dois <- function() {
  log_message("测试已知DOI查询...")
  
  # 一些已知的DOI
  known_dois <- c(
    "10.1038/nature12373",  # Nature文章
    "10.1126/science.1058040",  # Science文章
    "10.1371/journal.pone.0000001",  # PLOS ONE文章
    "10.1093/nar/gkv007",  # Nucleic Acids Research文章
    "10.1186/s13059-014-0550-8"  # Genome Biology文章
  )
  
  success_count <- 0
  
  for (i in seq_along(known_dois)) {
    doi <- known_dois[i]
    log_message(sprintf("\n=== 测试DOI %d: %s ===", i, doi))
    
    tryCatch({
      result <- rcrossref::cr_works(dois = doi)
      
      if (!is.null(result$data) && nrow(result$data) > 0) {
        title <- result$data$title[1]
        authors <- if (!is.null(result$data$author[[1]])) {
          paste(result$data$author[[1]]$family, collapse = ", ")
        } else {
          "未知"
        }
        year <- result$data$published.print[1]
        
        log_message(sprintf("成功获取数据:"))
        log_message(sprintf("  标题: %s", substr(title, 1, 80)))
        log_message(sprintf("  作者: %s", substr(authors, 1, 50)))
        log_message(sprintf("  年份: %s", year))
        
        success_count <- success_count + 1
      } else {
        log_message("未获取到数据", "warning")
      }
    }, error = function(e) {
      log_message(sprintf("查询失败: %s", e$message), "error")
    })
    
    Sys.sleep(1)  # 避免API限制
  }
  
  log_message(sprintf("\nDOI查询测试完成: %d/%d 成功", success_count, length(known_dois)))
  return(success_count > 0)
}

# 测试标题搜索功能
test_title_search <- function() {
  log_message("测试标题搜索功能...")
  
  # 使用一些通用的搜索词
  test_queries <- c(
    "machine learning",
    "COVID-19",
    "climate change",
    "artificial intelligence",
    "CRISPR"
  )
  
  success_count <- 0
  
  for (i in seq_along(test_queries)) {
    query <- test_queries[i]
    log_message(sprintf("\n=== 测试查询 %d: '%s' ===", i, query))
    
    tryCatch({
      result <- rcrossref::cr_works(query = query, limit = 3)
      
      if (!is.null(result$data) && nrow(result$data) > 0) {
        log_message(sprintf("找到 %d 个结果:", nrow(result$data)))
        
        for (j in 1:min(3, nrow(result$data))) {
          title <- result$data$title[j]
          doi <- result$data$doi[j]
          log_message(sprintf("  %d. %s", j, substr(title, 1, 60)))
          log_message(sprintf("     DOI: %s", doi))
        }
        
        success_count <- success_count + 1
      } else {
        log_message("未找到结果", "warning")
      }
    }, error = function(e) {
      log_message(sprintf("搜索失败: %s", e$message), "error")
    })
    
    Sys.sleep(1)  # 避免API限制
  }
  
  log_message(sprintf("\n标题搜索测试完成: %d/%d 成功", success_count, length(test_queries)))
  return(success_count > 0)
}

# 测试API配置和限制
test_api_configuration <- function() {
  log_message("测试API配置...")
  
  # 测试不同的查询参数
  tryCatch({
    # 测试基本查询
    result1 <- rcrossref::cr_works(query = "test", limit = 1)
    log_message("基本查询成功")
    
    # 测试带过滤器的查询
    result2 <- rcrossref::cr_works(
      query = "test", 
      filter = c(from_pub_date = "2020", until_pub_date = "2021"),
      limit = 1
    )
    log_message("带过滤器查询成功")
    
    # 测试作者查询
    result3 <- rcrossref::cr_works(query.author = "smith", limit = 1)
    log_message("作者查询成功")
    
    # 测试标题查询
    result4 <- rcrossref::cr_works(query.title = "machine learning", limit = 1)
    log_message("标题查询成功")
    
    return(TRUE)
  }, error = function(e) {
    log_message(sprintf("API配置测试失败: %s", e$message), "error")
    return(FALSE)
  })
}

# 主测试函数
main_test <- function() {
  log_message("开始简化的Crossref API测试")
  
  # 1. 测试API配置
  log_message("\n=== 第1步：测试API配置 ===")
  config_ok <- test_api_configuration()
  
  if (!config_ok) {
    log_message("API配置测试失败，终止测试", "error")
    return(FALSE)
  }
  
  # 2. 测试已知DOI查询
  log_message("\n=== 第2步：测试已知DOI查询 ===")
  doi_ok <- test_known_dois()
  
  # 3. 测试标题搜索
  log_message("\n=== 第3步：测试标题搜索 ===")
  search_ok <- test_title_search()
  
  # 总结
  log_message("\n=== 测试总结 ===")
  log_message(sprintf("API配置: %s", if(config_ok) "成功" else "失败"))
  log_message(sprintf("DOI查询: %s", if(doi_ok) "成功" else "失败"))
  log_message(sprintf("标题搜索: %s", if(search_ok) "成功" else "失败"))
  
  overall_success <- config_ok && (doi_ok || search_ok)
  log_message(sprintf("整体测试: %s", if(overall_success) "成功" else "失败"))
  
  if (overall_success) {
    log_message("\nCrossref API基本功能正常，可以用于数据增强")
  } else {
    log_message("\nCrossref API存在问题，需要进一步调试", "warning")
  }
  
  return(overall_success)
}

# 运行测试
if (interactive()) {
  main_test()
} else {
  main_test()
}

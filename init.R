# 项目初始化脚本
# 版本: 1.0.0

# 加载必要的包
required_packages <- c("here", "bibliometrix", "dplyr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  library(pkg, character.only = TRUE)
}

# 确保here包正确初始化
if (!requireNamespace("here", quietly = TRUE)) {
  stop("无法加载here包，请手动安装：install.packages('here')")
}

# 定义数据仓库目录结构
data_repo_dirs <- list(
  "00_original_raw_files",
  "01_baseline_datasets/bibliometrix_processed",
  "01_baseline_datasets/vosviewer_processed",
  "01_baseline_datasets/citespace_processed",
  "02_enhanced_dataset",
  "03_intermediate_files/bibliometrix_workflow",
  "04_analysis_outputs",
  "05_execution_logs/bibliometrix_logs",
  "06_data_reports/biblioshiny_reports",
  "07_metadata_and_notes/bibliometrix_metadata"
)

# 创建数据仓库目录
for (dir_path in data_repo_dirs) {
  full_path <- here("data_repository", dir_path)
  if (!dir.exists(full_path)) {
    dir.create(full_path, recursive = TRUE)
    message("创建目录: ", full_path)
  }
}

# 加载包依赖
source("R_packages.R")

# 设置日志
log_dir <- here("data_repository", "05_execution_logs")
if (!dir.exists(log_dir)) {
  dir.create(log_dir, recursive = TRUE)
}

# 打印初始化完成信息
message("\n项目初始化完成")
message("工作目录: ", here())
message("数据仓库目录: ", here("data_repository"))
message("日志目录: ", log_dir)

# 检查关键目录
message("\n检查关键目录:")
for (dir_path in data_repo_dirs) {
  full_path <- here("data_repository", dir_path)
  message(sprintf("%s: %s", dir_path, ifelse(dir.exists(full_path), "已创建", "未创建")))
}

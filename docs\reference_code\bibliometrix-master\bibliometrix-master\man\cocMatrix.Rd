% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/cocMatrix.R
\name{cocMatrix}
\alias{cocMatrix}
\title{Bibliographic bipartite network matrices}
\usage{
cocMatrix(
  M,
  Field = "AU",
  type = "sparse",
  n = NULL,
  sep = ";",
  binary = TRUE,
  short = FALSE,
  remove.terms = NULL,
  synonyms = NULL
)
}
\arguments{
\item{M}{is a data frame obtained by the converting function
\code{\link{convert2df}}. It is a data matrix with cases corresponding to
articles and variables to Field Tag in the original WoS or SCOPUS file.}

\item{Field}{is a character object. It indicates one of the field tags of the
  standard ISI WoS Field Tag codify. Field can be equal to one of these tags:
  \tabular{lll}{ \code{AU}\tab   \tab Authors\cr \code{SO}\tab   \tab
  Publication Name (or Source)\cr \code{JI}\tab   \tab ISO Source
  Abbreviation\cr \code{DE}\tab   \tab Author Keywords\cr \code{ID}\tab
  \tab Keywords associated by WoS or SCOPUS database \cr \code{CR}\tab   \tab
  Cited References}

  for a complete list of filed tags see:
  \href{https://www.bibliometrix.org/documents/Field_Tags_bibliometrix.pdf}{Field Tags used in bibliometrix}\cr\cr}

\item{type}{indicates the output format of co-occurrences: \tabular{lll}{
\code{type = "matrix"} \tab   \tab produces an object of class
\code{matrix}\cr \code{type = "sparse"} \tab   \tab produces an object of
class \code{dgMatrix} of the package \code{Matrix}. "sparse"
argument generates a compact representation of the matrix.}}

\item{n}{is an integer. It indicates the number of items to select. If \code{N = NULL}, all items are selected.}

\item{sep}{is the field separator character. This character separates strings in each 
column of the data frame. The default is \code{sep = ";"}.}

\item{binary}{is a logical. If TRUE each cell contains a 0/1. if FALSE each cell contains the frequency.}

\item{short}{is a logical. If TRUE all items with frequency<2 are deleted to reduce the matrix size.}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}
}
\value{
a bipartite network matrix with cases corresponding to manuscripts and variables to the
  objects extracted from the Tag \code{Field}.
}
\description{
\code{cocMatrix} computes occurrences between elements of a Tag Field from a bibliographic data frame. Manuscript is the unit of analysis.
}
\details{
This occurrence matrix represents a bipartite network which can be transformed into a collection of bibliographic
networks such as coupling, co-citation, etc.. 

The function follows the approach proposed by Batagelj & Cerinsek (2013) and Aria & cuccurullo (2017).\cr\cr

References:\cr
Batagelj, V., & Cerinsek, M. (2013). On bibliographic networks. Scientometrics, 96(3), 845-864.\cr
Aria, M., & Cuccurullo, C. (2017). bibliometrix: An R-tool for comprehensive science mapping analysis. Journal of Informetrics, 11(4), 959-975.\cr
}
\examples{
# EXAMPLE 1: Articles x Authors occurrence matrix

data(scientometrics, package = "bibliometrixData")
WA <- cocMatrix(scientometrics, Field = "AU", type = "sparse", sep = ";")

# EXAMPLE 2: Articles x Cited References occurrence matrix

# data(scientometrics, package = "bibliometrixData")

# WCR <- cocMatrix(scientometrics, Field = "CR", type = "sparse", sep = ";")

# EXAMPLE 3: Articles x Cited First Authors occurrence matrix

# data(scientometrics, package = "bibliometrixData")
# scientometrics <- metaTagExtraction(scientometrics, Field = "CR_AU", sep = ";")
# WCR <- cocMatrix(scientometrics, Field = "CR_AU", type = "sparse", sep = ";")

}
\seealso{
\code{\link{convert2df}} to import and convert an ISI or SCOPUS
  Export file in a data frame.

\code{\link{biblioAnalysis}} to perform a bibliometric analysis.

\code{\link{biblioNetwork}} to compute a bibliographic network.
}

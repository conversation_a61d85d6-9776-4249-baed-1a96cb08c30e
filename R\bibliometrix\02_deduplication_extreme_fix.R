# 02_deduplication_extreme_fix.R
# 严格遵循bibliometrix官方极限去重框架
# 使用UT、TI、AB字段进行去重

options(repos = c(CRAN = "https://cloud.r-project.org"))

# 设置内存和栈限制
options(expressions = 500000)  # 增加R表达式的递归限制
if (.Platform$OS.type == "windows") {
  memory.limit(size = 16000)  # Windows上增加内存限制
} else {
  system("ulimit -s 65536")  # Unix系统设置栈大小
}

required_packages <- c("bibliometrix", "here", "dplyr", "stringr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) install.packages(pkg)
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
  if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
    cat(formatted_msg, "\n", file = log_con)
  }
}

config <- list(
  paths = list(
    input = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    output = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    logs = here("data_repository", "05_execution_logs", "bibliometrix_logs")
  ),
  files = list(
    input = "datay_bibliometrix_initial.rds",
    output = "datax_bibliometrix_advanced.rds",
    log = "deduplication_extreme_fix.log"
  ),
  deduplication = list(
    title_tolerance = 0.98,
    abstract_tolerance = 0.995,  # 提高相似度阈值
    batch_size = 1000  # 批处理大小
  )
)

input_file <- file.path(config$paths$input, config$files$input)
output_file <- file.path(config$paths$output, config$files$output)
log_file <- file.path(config$paths$logs, config$files$log)

if (!file.exists(input_file)) stop("未找到输入文件，请先生成datay_bibliometrix_initial.rds")
if (!dir.exists(config$paths$logs)) dir.create(config$paths$logs, recursive = TRUE)

log_con <- file(log_file, "w")
if (!isOpen(log_con)) {
  stop("无法打开日志文件")
}

start_time <- Sys.time()
log_message("官方极限去重流程开始")

# 加载数据
M <- readRDS(input_file)
log_message(sprintf("原始记录数: %d", nrow(M)))

# 1. UT精确去重
if ("UT" %in% names(M)) {
  before <- nrow(M)
  M <- dplyr::distinct(M, UT, .keep_all = TRUE)
  log_message(sprintf("UT精确去重后记录数: %d (去除%d条)", nrow(M), before - nrow(M)))
} else {
  log_message("未检测到UT字段，跳过UT去重", "warning")
}

# 2. 标题模糊匹配
if ("TI" %in% names(M)) {
  before <- nrow(M)
  M <- duplicatedMatching(M, Field = "TI", exact = FALSE, tol = config$deduplication$title_tolerance)
  log_message(sprintf("标题模糊匹配后记录数: %d (去除%d条)", nrow(M), before - nrow(M)))
} else {
  log_message("未检测到TI字段，跳过标题模糊匹配", "warning")
}

# 3. 摘要模糊匹配
if ("AB" %in% names(M)) {
  before <- nrow(M)
  
  # 预处理摘要文本
  M$AB <- tolower(M$AB)  # 统一小写
  M$AB <- gsub("[^[:alnum:] ]", "", M$AB)  # 移除特殊字符
  M$AB <- gsub("\\s+", " ", M$AB)  # 统一空格
  M$AB <- trimws(M$AB)  # 移除首尾空格
  
  # 移除过短的摘要（可能包含无效信息）
  M <- M[nchar(M$AB) > 50, ]
  
  # 分批处理
  n_batches <- ceiling(nrow(M) / config$deduplication$batch_size)
  log_message(sprintf("开始分批处理摘要，共%d批", n_batches))
  
  for (i in 1:n_batches) {
    start_idx <- (i - 1) * config$deduplication$batch_size + 1
    end_idx <- min(i * config$deduplication$batch_size, nrow(M))
    
    batch <- M[start_idx:end_idx, ]
    batch <- duplicatedMatching(batch, Field = "AB", exact = FALSE, tol = config$deduplication$abstract_tolerance)
    
    if (i == 1) {
      M_dedup <- batch
    } else {
      M_dedup <- rbind(M_dedup, batch)
    }
    
    log_message(sprintf("完成第%d批处理，当前记录数: %d", i, nrow(M_dedup)))
  }
  
  M <- M_dedup
  log_message(sprintf("摘要模糊匹配后记录数: %d (去除%d条)", nrow(M), before - nrow(M)))
} else {
  log_message("未检测到AB字段，跳过摘要模糊匹配", "warning")
}

# 保存最终结果
saveRDS(M, file = output_file)
log_message(sprintf("最终去重后记录数: %d", nrow(M)))
end_time <- Sys.time()
log_message(sprintf("总耗时: %.2f 分钟", as.numeric(difftime(end_time, start_time, units = "mins"))))

log_message("官方极限去重流程结束")
if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
  close(log_con)
} 
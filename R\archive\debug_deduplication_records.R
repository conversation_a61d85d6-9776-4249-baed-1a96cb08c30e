# R/debug_deduplication_records.R
# 调试去重结果中的异常记录

# --- 0. 配置管理 ---
# 加载必要的包
required_packages <- c("bibliometrix", "here", "dplyr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保here包正确初始化
if (!requireNamespace("here", quietly = TRUE)) {
  stop("无法加载here包，请手动安装：install.packages('here')")
}

# 定义日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

config <- list(
  paths = list(
    baseline_datasets = here("data_repository", "01_baseline_datasets", "bibliometrix_processed")
  ),
  files = list(
    initial = "datay_bibliometrix_initial.rds",
    biblioshiny_dedup = "datay_bibliometrix_biblioshiny.rds",
    extreme_dedup = "datax_bibliometrix_advanced.rds"
  )
)

# --- 1. 设置环境与加载数据 ---
log_message("开始调试去重记录流程")

initial_file <- file.path(config$paths$baseline_datasets, config$files$initial)
biblioshiny_file <- file.path(config$paths$baseline_datasets, config$files$biblioshiny_dedup)
extreme_file <- file.path(config$paths$baseline_datasets, config$files$extreme_dedup)

# 检查文件是否存在
if (!file.exists(initial_file)) {
  log_message(sprintf("错误：初始数据文件不存在：%s", initial_file), "error")
  stop("请确保已运行01_wos_convert2df.R")
}
if (!file.exists(biblioshiny_file)) {
  log_message(sprintf("错误：Biblioshiny去重结果文件不存在：%s", biblioshiny_file), "error")
  stop("请确保已运行02_deduplication_biblioshiny.R")
}
if (!file.exists(extreme_file)) {
  log_message(sprintf("错误：极限去重结果文件不存在：%s", extreme_file), "error")
  stop("请确保已运行02_deduplication_extreme.R")
}

M_initial <- readRDS(initial_file)
M_biblioshiny <- readRDS(biblioshiny_file)
M_extreme <- readRDS(extreme_file)

log_message(sprintf("成功加载初始数据，记录数: %d", nrow(M_initial)))
log_message(sprintf("成功加载Biblioshiny去重结果，记录数: %d", nrow(M_biblioshiny)))
log_message(sprintf("成功加载极限去重结果，记录数: %d", nrow(M_extreme)))

# --- 2. 检查特定记录 ---
problematic_uts <- c("WOS:001046439700005", "WOS:000347142500005")

check_record_status <- function(data_frame, ut_value, df_name) {
  record <- data_frame %>% filter(UT == ut_value) %>% select(TI, UT, DI, PY)
  if (nrow(record) > 0) {
    log_message(sprintf("%s 中存在记录 %s:", df_name, ut_value), "info")
    print(record)
  } else {
    log_message(sprintf("%s 中不存在记录 %s", df_name, ut_value), "info")
  }
}

log_message("\n--- 检查记录 WOS:001046439700005 ---")
check_record_status(M_initial, "WOS:001046439700005", "初始数据")
check_record_status(M_biblioshiny, "WOS:001046439700005", "Biblioshiny去重结果")
check_record_status(M_extreme, "WOS:001046439700005", "极限去重结果")

log_message("\n--- 检查记录 WOS:000347142500005 ---")
check_record_status(M_initial, "WOS:000347142500005", "初始数据")
check_record_status(M_biblioshiny, "WOS:000347142500005", "Biblioshiny去重结果")
check_record_status(M_extreme, "WOS:000347142500005", "极限去重结果")

log_message("调试去重记录流程完成") 
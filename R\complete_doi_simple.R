# 简化版DOI补全脚本 - 直接开始处理

cat("开始DOI补全处理...\n")

# 加载包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

cat("包加载完成\n")

# 基本函数
normalize_title <- function(title) {
  if (is.na(title)) return(NA)
  title <- tolower(title)
  title <- gsub("[[:punct:]]", " ", title)
  title <- gsub("\\s+", " ", title)
  trimws(title)
}

calculate_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  1 - stringdist(str1, str2, method="jw")
}

get_doi_simple <- function(title, year) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_title(title)
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 5) keywords <- keywords[1:5]
    
    if (length(keywords) == 0) return(list(doi = NA, score = 0, similarity = 0))
    
    query_string <- paste(keywords, collapse = " ")
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=5", 
                   URLencode(query_string), as.numeric(year)-1, as.numeric(year)+1)
    
    # API请求
    response <- GET(url, user_agent("BiblioEnhancer/1.0"), timeout(30))
    if (status_code(response) != 200) return(list(doi = NA, score = 0, similarity = 0))
    
    content <- fromJSON(rawToChar(response$content))
    
    if (!is.null(content$message$items) && length(content$message$items) > 0) {
      items <- content$message$items
      
      # 寻找最佳匹配
      best_match_idx <- NULL
      best_similarity <- 0
      
      for (i in 1:nrow(items)) {
        item <- items[i,]
        if (!is.null(item$title) && length(item$title) > 0) {
          candidate_title <- item$title[[1]]
          current_similarity <- calculate_similarity(normalize_title(title), normalize_title(candidate_title))
          
          if (current_similarity > best_similarity) {
            best_similarity <- current_similarity
            best_match_idx <- i
          }
        }
      }
      
      if (!is.null(best_match_idx) && best_similarity > 0.3) {
        doi_found <- items$DOI[best_match_idx]
        crossref_score <- items$score[best_match_idx]
        
        if (!is.na(doi_found) && doi_found != "") {
          return(list(doi = doi_found, score = crossref_score, similarity = best_similarity))
        }
      }
    }
    
    return(list(doi = NA, score = 0, similarity = 0))
  }, error = function(e) {
    return(list(doi = NA, score = 0, similarity = 0))
  })
}

# 主处理
cat("加载数据...\n")
input_file <- "C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/04_enhancement_reports/missing_doi_records.csv"
data <- read.csv(input_file, stringsAsFactors = FALSE)
cat(sprintf("数据加载完成: %d行\n", nrow(data)))

# 处理全部记录
test_count <- nrow(data)
cat(sprintf("开始处理全部%d条记录\n", test_count))

results <- data.frame(
  UT = data$UT[1:test_count],
  TI = data$TI[1:test_count],
  AU = data$AU[1:test_count],
  PY = data$PY[1:test_count],
  SO = data$SO[1:test_count],
  VL = data$VL[1:test_count],
  IS = data$IS[1:test_count],
  PG = data$PG[1:test_count],
  DOI = NA,
  confidence = NA,
  similarity = NA,
  quality_score = NA,
  review_needed = FALSE,
  match_details = "",
  status = "pending",
  stringsAsFactors = FALSE
)

success_count <- 0

for (i in 1:test_count) {
  # 显示进度
  if (i %% 50 == 0 || i == 1) {
    cat(sprintf("处理进度: %d/%d (%.1f%%)\n", i, test_count, 100*i/test_count))
  }

  doi_result <- get_doi_simple(results$TI[i], results$PY[i])

  if (!is.na(doi_result$doi)) {
    results$DOI[i] <- doi_result$doi
    results$confidence[i] <- doi_result$score
    results$similarity[i] <- doi_result$similarity
    results$status[i] <- "success"

    # 计算质量分数
    quality_score <- doi_result$similarity * 0.7 + (doi_result$score / 100) * 0.3
    results$quality_score[i] <- quality_score

    # 判断是否需要人工审核
    if (quality_score < 0.6 || doi_result$similarity < 0.5 || doi_result$score < 30) {
      results$review_needed[i] <- TRUE
      results$match_details[i] <- sprintf("低置信度匹配 (相似度:%.2f, 分数:%.1f)",
                                         doi_result$similarity, doi_result$score)
    } else {
      results$match_details[i] <- sprintf("高置信度匹配 (相似度:%.2f, 分数:%.1f)",
                                         doi_result$similarity, doi_result$score)
    }

    success_count <- success_count + 1

    if (i <= 20 || i %% 50 == 0) {
      cat(sprintf("  ✓ 第%d条: DOI找到 (相似度: %.2f)\n", i, doi_result$similarity))
    }
  } else {
    results$status[i] <- "failed"
    results$match_details[i] <- "未找到匹配的DOI"

    if (i <= 20 || i %% 50 == 0) {
      cat(sprintf("  ✗ 第%d条: 未找到DOI\n", i))
    }
  }

  # 每100条记录保存一次中间结果
  if (i %% 100 == 0) {
    temp_file <- sprintf("C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/04_enhancement_reports/doi_completion_temp_%d.csv", i)
    write.csv(results[1:i, ], temp_file, row.names = FALSE)
    cat(sprintf("  中间结果已保存: 第%d条\n", i))
  }

  # 防止API限制
  Sys.sleep(1.5)
}

# 详细统计结果
success_rate <- 100 * success_count / test_count
high_quality_count <- sum(!is.na(results$quality_score) & results$quality_score >= 0.7, na.rm = TRUE)
review_needed_count <- sum(results$review_needed, na.rm = TRUE)

cat(sprintf("\n=== 完整处理结果统计 ===\n"))
cat(sprintf("总记录数: %d\n", test_count))
cat(sprintf("成功补全: %d (%.2f%%)\n", success_count, success_rate))
cat(sprintf("高质量匹配: %d (%.2f%%)\n", high_quality_count, 100 * high_quality_count / test_count))
cat(sprintf("需要人工审核: %d (%.2f%%)\n", review_needed_count, 100 * review_needed_count / test_count))
cat(sprintf("补全失败: %d (%.2f%%)\n", test_count - success_count, 100 - success_rate))

# 质量分布统计
quality_excellent <- sum(!is.na(results$quality_score) & results$quality_score >= 0.8, na.rm = TRUE)
quality_good <- sum(!is.na(results$quality_score) & results$quality_score >= 0.6 & results$quality_score < 0.8, na.rm = TRUE)
quality_fair <- sum(!is.na(results$quality_score) & results$quality_score >= 0.4 & results$quality_score < 0.6, na.rm = TRUE)
quality_poor <- sum(!is.na(results$quality_score) & results$quality_score < 0.4, na.rm = TRUE)

cat(sprintf("\n=== 质量分布 ===\n"))
cat(sprintf("优秀 (≥0.8): %d (%.2f%%)\n", quality_excellent, 100 * quality_excellent / test_count))
cat(sprintf("良好 (0.6-0.8): %d (%.2f%%)\n", quality_good, 100 * quality_good / test_count))
cat(sprintf("一般 (0.4-0.6): %d (%.2f%%)\n", quality_fair, 100 * quality_fair / test_count))
cat(sprintf("较差 (<0.4): %d (%.2f%%)\n", quality_poor, 100 * quality_poor / test_count))

# 保存最终结果
output_file <- "C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/04_enhancement_reports/doi_completion_full_final.csv"
write.csv(results, output_file, row.names = FALSE)
cat(sprintf("\n最终结果已保存: %s\n", output_file))

# 创建高质量匹配和需要审核的单独文件
high_quality_file <- "C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/04_enhancement_reports/doi_completion_high_quality.csv"
review_needed_file <- "C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/04_enhancement_reports/doi_completion_review_needed.csv"

high_quality_results <- results[!is.na(results$quality_score) & results$quality_score >= 0.7, ]
review_needed_results <- results[results$review_needed == TRUE, ]

write.csv(high_quality_results, high_quality_file, row.names = FALSE)
write.csv(review_needed_results, review_needed_file, row.names = FALSE)

cat(sprintf("高质量匹配结果: %s\n", high_quality_file))
cat(sprintf("需要审核结果: %s\n", review_needed_file))

cat("处理完成！\n")

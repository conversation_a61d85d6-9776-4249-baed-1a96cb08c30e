# 综合性能分析工具
# 评估DOI补全成功率和MeSH分类准确度，识别改进空间

library(httr)
library(jsonlite)
library(stringdist)

# 加载所有系统
source("doi_completion_core.R")
source("doi_completion_final_optimized.R")
source("pubmed_enhanced_mesh.R")
source("three_engine_doi_completion.R")

cat("=== 综合性能分析系统 ===\n")
cat("🎯 目标: 评估DOI补全和MeSH分类性能\n")
cat("📊 分析: 覆盖率、准确度、瓶颈识别\n\n")

# === 性能分析测试用例 ===
create_comprehensive_test_cases <- function() {
  list(
    # 生物医学 - 高质量期刊
    list(
      title = "Efficacy of COVID-19 vaccines: a systematic review and meta-analysis",
      authors = "Smith J, Johnson A",
      year = 2021,
      journal = "The Lancet",
      domain = "biomedical",
      expected_mesh = c("Systematic Review", "Meta-Analysis", "Journal Article"),
      difficulty = "easy"
    ),
    
    # 生物医学 - 临床试验
    list(
      title = "Randomized controlled trial of aspirin for cardiovascular disease prevention",
      authors = "Brown C, Davis E",
      year = 2020,
      journal = "New England Journal of Medicine",
      domain = "biomedical",
      expected_mesh = c("Randomized Controlled Trial", "Clinical Trial", "Journal Article"),
      difficulty = "easy"
    ),
    
    # 技术领域 - 机器学习
    list(
      title = "Deep learning algorithms for medical image analysis",
      authors = "Wilson F, Taylor G",
      year = 2019,
      journal = "Nature Machine Intelligence",
      domain = "technology",
      expected_mesh = c(),
      difficulty = "medium"
    ),
    
    # 跨学科 - 生物信息学
    list(
      title = "Machine learning approaches for genomic data analysis in precision medicine",
      authors = "Anderson H, Martinez I",
      year = 2020,
      journal = "Nature Biotechnology",
      domain = "interdisciplinary",
      expected_mesh = c("Journal Article"),
      difficulty = "medium"
    ),
    
    # 困难案例 - 较老文献
    list(
      title = "Molecular mechanisms of cancer cell metastasis",
      authors = "Thompson J, White K",
      year = 1995,
      journal = "Cell",
      domain = "biomedical",
      expected_mesh = c("Journal Article", "Review"),
      difficulty = "hard"
    ),
    
    # 困难案例 - 小众期刊
    list(
      title = "Novel therapeutic targets for rare genetic diseases",
      authors = "Garcia L, Rodriguez M",
      year = 2018,
      journal = "Orphanet Journal of Rare Diseases",
      domain = "biomedical",
      expected_mesh = c("Journal Article", "Review"),
      difficulty = "hard"
    ),
    
    # 技术领域 - 计算机科学
    list(
      title = "Blockchain technology for secure healthcare data management",
      authors = "Lee N, Kim O",
      year = 2021,
      journal = "IEEE Transactions on Biomedical Engineering",
      domain = "technology",
      expected_mesh = c(),
      difficulty = "medium"
    ),
    
    # 生物医学 - 病例报告
    list(
      title = "Rare case of drug-induced liver injury: a case report",
      authors = "Patel P, Singh Q",
      year = 2020,
      journal = "BMC Gastroenterology",
      domain = "biomedical",
      expected_mesh = c("Case Reports", "Journal Article"),
      difficulty = "easy"
    )
  )
}

# === 综合性能测试函数 ===
comprehensive_performance_test <- function() {
  cat("\n=== 开始综合性能测试 ===\n")
  
  test_cases <- create_comprehensive_test_cases()
  total_cases <- length(test_cases)
  
  # 初始化结果统计
  results <- data.frame(
    案例ID = 1:total_cases,
    标题 = sapply(test_cases, function(x) substr(x$title, 1, 50)),
    领域 = sapply(test_cases, function(x) x$domain),
    难度 = sapply(test_cases, function(x) x$difficulty),
    Crossref成功 = FALSE,
    OpenAlex成功 = FALSE,
    PubMed成功 = FALSE,
    三引擎成功 = FALSE,
    MeSH提取成功 = FALSE,
    MeSH准确度 = 0,
    最佳引擎 = "",
    最高评分 = 0,
    stringsAsFactors = FALSE
  )
  
  # 引擎统计
  engine_stats <- list(
    crossref = list(success = 0, total_score = 0),
    openalex = list(success = 0, total_score = 0),
    pubmed = list(success = 0, total_score = 0),
    three_engine = list(success = 0, total_score = 0)
  )
  
  # 逐个测试案例
  for (i in 1:total_cases) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n--- 案例 %d/%d: %s ---\n", i, total_cases, test_case$difficulty))
    cat(sprintf("标题: %s\n", substr(test_case$title, 1, 60)))
    cat(sprintf("领域: %s | 年份: %s\n", test_case$domain, test_case$year))
    
    # 测试各个引擎
    engines_results <- list()
    
    # 1. Crossref
    cat("  测试Crossref...\n")
    crossref_result <- search_doi(test_case$title, test_case$authors, test_case$year, test_case$journal)
    if (!is.null(crossref_result)) {
      crossref_result$source <- "crossref"
      engines_results$crossref <- crossref_result
      results$Crossref成功[i] <- TRUE
      engine_stats$crossref$success <- engine_stats$crossref$success + 1
      engine_stats$crossref$total_score <- engine_stats$crossref$total_score + crossref_result$final_score
      cat(sprintf("    ✅ 成功: %s (评分: %.3f)\n", crossref_result$doi, crossref_result$final_score))
    } else {
      cat("    ❌ 失败\n")
    }
    
    # 2. OpenAlex优化版
    cat("  测试OpenAlex优化版...\n")
    openalex_result <- search_doi_openalex_final(test_case$title, test_case$authors, test_case$year, test_case$journal)
    if (!is.null(openalex_result)) {
      engines_results$openalex <- openalex_result
      results$OpenAlex成功[i] <- TRUE
      engine_stats$openalex$success <- engine_stats$openalex$success + 1
      engine_stats$openalex$total_score <- engine_stats$openalex$total_score + openalex_result$final_score
      cat(sprintf("    ✅ 成功: %s (评分: %.3f)\n", openalex_result$doi, openalex_result$final_score))
    } else {
      cat("    ❌ 失败\n")
    }
    
    # 3. PubMed增强版
    cat("  测试PubMed增强版...\n")
    pubmed_result <- search_doi_pubmed_enhanced(test_case$title, test_case$authors, test_case$year, test_case$journal)
    if (!is.null(pubmed_result)) {
      engines_results$pubmed <- pubmed_result
      results$PubMed成功[i] <- TRUE
      results$MeSH提取成功[i] <- TRUE
      engine_stats$pubmed$success <- engine_stats$pubmed$success + 1
      engine_stats$pubmed$total_score <- engine_stats$pubmed$total_score + pubmed_result$final_score
      
      # 计算MeSH准确度
      if (length(test_case$expected_mesh) > 0) {
        extracted_mesh <- pubmed_result$mesh_publication_types
        if (length(extracted_mesh) > 0) {
          mesh_accuracy <- length(intersect(extracted_mesh, test_case$expected_mesh)) / length(test_case$expected_mesh)
          results$MeSH准确度[i] <- round(mesh_accuracy, 3)
        }
      }
      
      cat(sprintf("    ✅ 成功: %s (PMID: %s, 评分: %.3f)\n", pubmed_result$doi, pubmed_result$pmid, pubmed_result$final_score))
      cat(sprintf("    📋 MeSH类型: %s\n", paste(pubmed_result$mesh_publication_types, collapse = ", ")))
    } else {
      cat("    ❌ 失败\n")
    }
    
    # 4. 三引擎智能系统
    cat("  测试三引擎智能系统...\n")
    three_engine_result <- search_doi_three_engines(test_case$title, test_case$authors, test_case$year, test_case$journal, strategy = "adaptive")
    if (!is.null(three_engine_result)) {
      engines_results$three_engine <- three_engine_result
      results$三引擎成功[i] <- TRUE
      engine_stats$three_engine$success <- engine_stats$three_engine$success + 1
      engine_stats$three_engine$total_score <- engine_stats$three_engine$total_score + three_engine_result$final_score
      cat(sprintf("    ✅ 成功: %s (来源: %s, 评分: %.3f)\n", three_engine_result$doi, three_engine_result$source, three_engine_result$final_score))
    } else {
      cat("    ❌ 失败\n")
    }
    
    # 确定最佳引擎
    if (length(engines_results) > 0) {
      best_engine <- ""
      best_score <- 0
      for (engine_name in names(engines_results)) {
        result <- engines_results[[engine_name]]
        if (result$final_score > best_score) {
          best_score <- result$final_score
          best_engine <- engine_name
        }
      }
      results$最佳引擎[i] <- best_engine
      results$最高评分[i] <- round(best_score, 3)
    }
    
    # API调用间隔
    if (i < total_cases) {
      cat("  等待3秒...\n")
      Sys.sleep(3)
    }
  }
  
  return(list(results = results, engine_stats = engine_stats, test_cases = test_cases))
}

# === 性能分析报告生成 ===
generate_performance_report <- function(test_results) {
  results <- test_results$results
  engine_stats <- test_results$engine_stats
  test_cases <- test_results$test_cases
  total_cases <- nrow(results)
  
  cat(sprintf("\n=== 综合性能分析报告 ===\n"))
  
  # 1. 总体成功率
  cat(sprintf("📊 总体成功率分析 (基于 %d 个测试案例):\n", total_cases))
  cat(sprintf("  Crossref:        %.1f%% (%d/%d)\n", 
              100 * sum(results$Crossref成功) / total_cases, sum(results$Crossref成功), total_cases))
  cat(sprintf("  OpenAlex优化:    %.1f%% (%d/%d)\n", 
              100 * sum(results$OpenAlex成功) / total_cases, sum(results$OpenAlex成功), total_cases))
  cat(sprintf("  PubMed增强:      %.1f%% (%d/%d)\n", 
              100 * sum(results$PubMed成功) / total_cases, sum(results$PubMed成功), total_cases))
  cat(sprintf("  三引擎智能:      %.1f%% (%d/%d)\n", 
              100 * sum(results$三引擎成功) / total_cases, sum(results$三引擎成功), total_cases))
  
  # 2. 按领域分析
  cat(sprintf("\n📋 按研究领域分析:\n"))
  domains <- unique(sapply(test_cases, function(x) x$domain))
  for (domain in domains) {
    domain_indices <- which(sapply(test_cases, function(x) x$domain) == domain)
    domain_count <- length(domain_indices)
    domain_success <- sum(results$三引擎成功[domain_indices])
    cat(sprintf("  %s: %.1f%% (%d/%d)\n", 
                domain, 100 * domain_success / domain_count, domain_success, domain_count))
  }
  
  # 3. 按难度分析
  cat(sprintf("\n🎯 按难度级别分析:\n"))
  difficulties <- unique(sapply(test_cases, function(x) x$difficulty))
  for (difficulty in difficulties) {
    diff_indices <- which(sapply(test_cases, function(x) x$difficulty) == difficulty)
    diff_count <- length(diff_indices)
    diff_success <- sum(results$三引擎成功[diff_indices])
    cat(sprintf("  %s: %.1f%% (%d/%d)\n", 
                difficulty, 100 * diff_success / diff_count, diff_success, diff_count))
  }
  
  # 4. MeSH分类分析
  mesh_success_count <- sum(results$MeSH提取成功)
  if (mesh_success_count > 0) {
    avg_mesh_accuracy <- mean(results$MeSH准确度[results$MeSH提取成功])
    cat(sprintf("\n🏷️  MeSH分类分析:\n"))
    cat(sprintf("  MeSH提取成功率: %.1f%% (%d/%d)\n", 
                100 * mesh_success_count / total_cases, mesh_success_count, total_cases))
    cat(sprintf("  MeSH分类准确度: %.1f%% (平均)\n", 100 * avg_mesh_accuracy))
  }
  
  # 5. 引擎质量分析
  cat(sprintf("\n⭐ 引擎质量分析 (平均评分):\n"))
  for (engine in names(engine_stats)) {
    stats <- engine_stats[[engine]]
    if (stats$success > 0) {
      avg_score <- stats$total_score / stats$success
      cat(sprintf("  %s: %.3f (基于 %d 个成功案例)\n", 
                  engine, avg_score, stats$success))
    }
  }
  
  # 6. 瓶颈识别
  cat(sprintf("\n🔍 性能瓶颈识别:\n"))
  
  # 失败案例分析
  failed_cases <- which(!results$三引擎成功)
  if (length(failed_cases) > 0) {
    cat(sprintf("  失败案例数: %d\n", length(failed_cases)))
    cat("  失败案例特征:\n")
    for (i in failed_cases) {
      test_case <- test_cases[[i]]
      cat(sprintf("    - %s (%s, %s年, %s)\n", 
                  test_case$difficulty, test_case$domain, test_case$year, substr(test_case$journal, 1, 30)))
    }
  }
  
  # 改进建议
  cat(sprintf("\n💡 改进建议:\n"))
  
  crossref_rate <- 100 * sum(results$Crossref成功) / total_cases
  openalex_rate <- 100 * sum(results$OpenAlex成功) / total_cases
  pubmed_rate <- 100 * sum(results$PubMed成功) / total_cases
  
  if (crossref_rate < 30) {
    cat("  1. Crossref查询策略需要优化 (成功率偏低)\n")
  }
  if (openalex_rate < 30) {
    cat("  2. OpenAlex参数需要进一步调整\n")
  }
  if (pubmed_rate < 20) {
    cat("  3. PubMed查询范围可以扩大\n")
  }
  if (mesh_success_count < total_cases * 0.5) {
    cat("  4. MeSH提取算法需要改进\n")
  }
  
  return(list(
    total_success_rate = 100 * sum(results$三引擎成功) / total_cases,
    mesh_extraction_rate = 100 * mesh_success_count / total_cases,
    mesh_accuracy = if (mesh_success_count > 0) 100 * avg_mesh_accuracy else 0,
    bottlenecks = failed_cases,
    recommendations = list(
      improve_crossref = crossref_rate < 30,
      improve_openalex = openalex_rate < 30,
      improve_pubmed = pubmed_rate < 20,
      improve_mesh = mesh_success_count < total_cases * 0.5
    )
  ))
}

cat("✅ 综合性能分析系统已加载\n")
cat("📋 主要函数:\n")
cat("  - comprehensive_performance_test()  : 执行综合性能测试\n")
cat("  - generate_performance_report()     : 生成性能分析报告\n")

# 自动执行综合性能测试
cat("\n🚀 开始自动执行综合性能测试...\n")
performance_results <- comprehensive_performance_test()

cat("\n📊 生成性能分析报告...\n")
analysis_report <- generate_performance_report(performance_results)

# 保存结果
write.csv(performance_results$results, "data_repository/04_enhancement_reports/COMPREHENSIVE_PERFORMANCE_RESULTS.csv", row.names = FALSE)
cat("\n✅ 性能测试结果已保存: COMPREHENSIVE_PERFORMANCE_RESULTS.csv\n")

cat(sprintf("\n🎯 综合性能分析完成!\n"))
cat(sprintf("总体成功率: %.1f%%\n", analysis_report$total_success_rate))
cat(sprintf("MeSH提取率: %.1f%%\n", analysis_report$mesh_extraction_rate))
cat(sprintf("MeSH准确度: %.1f%%\n", analysis_report$mesh_accuracy))

if (analysis_report$total_success_rate >= 50) {
  cat("🎉 系统性能良好，继续优化细节！\n")
} else {
  cat("⚠️  系统需要重点优化，识别关键瓶颈。\n")
}

# 01_data_enhancement_framework.R
# 数据增强流程 - 基于两阶段框架思路
# 专注于API补全和字段标准化，为后续去重做准备
# 参考bibliometrix源代码实现字段标准化

# 加载必要的包
# 检查并安装所需的包
required_packages <- c("here", "dplyr", "stringr", "bibliometrix", "httr", "jsonlite", "progress", "DBI", "RSQLite", "curl", "future", "furrr", "progressr", "rcrossref") # 增加rcrossref包

# 设置CRAN镜像，确保包能被正确下载
options(repos = c(CRAN = "https://cloud.r-project.org/"))

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    message(sprintf("正在安装缺失的包: %s", pkg))
    install.packages(pkg, dependencies = TRUE, repos = "https://cloud.r-project.org/") # 显式指定镜像
    library(pkg, character.only = TRUE)
  } else {
    message(sprintf("包已加载: %s", pkg))
  }
}

# library(here)
# library(dplyr)
# library(stringr)
# library(bibliometrix)
# library(httr)
# library(jsonlite)
# library(progress)
# library(DBI)      # 新增：数据库接口
# library(RSQLite)  # 新增：SQLite数据库支持
# library(curl)     # 新增：HTTP请求的底层库，用于更高级的控制
# library(future)   # 新增：并行和异步编程
# library(furrr)    # 新增：适用于purrr函数的并行化

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

# 配置
config <- list(
  # 路径配置
  input_file = here("data_repository", "02_enhanced_dataset", "enhanced_data_initial.rds"),
  output_file = here("data_repository", "02_enhanced_dataset", "enhanced_data_optimized.rds"),
  logs_dir = here("data_repository", "05_execution_logs"),
  reports_dir = here("data_repository", "04_enhancement_reports"),
  api_cache_file = here("data_repository", "06_api_cache", "api_cache_enhanced.sqlite"), # 新增：API缓存文件路径
  
  # API配置 (基于增强版脚本)
  use_crossref_api = TRUE,    # 是否使用Crossref API
  use_openalex_api = TRUE,    # 是否使用OpenAlex API
  # user_email = "<EMAIL>", # 用户邮箱，用于API识别和提高请求限制 (将移至api_timeout_settings)
  
  api_timeout_settings = list( # 增强的API超时和重试设置
    timeout = 180,           # 超时时间3分钟
    connecttimeout = 120,    # 连接超时2分钟
    retries = 5,             # 增加到5次重试
    retry_delay = 15,        # 每次重试间隔15秒
    user_agent = "bibliometric-analysis/1.0 (mailto:<EMAIL>; 研究用途)", # 包含邮箱
    use_alt_api = TRUE       # 是否尝试使用备用API端点
  ),
  batch_size = 25,            # API批量请求大小
  throttle_delay = 0.5,       # API请求间隔（秒）
  cache_api_results = TRUE,   # 是否缓存API结果
  
  # 并行处理配置
  use_parallel = TRUE,        # 是否启用并行处理
  parallel_strategy = "multisession", # 并行策略，例如 "multisession", "multicore"
  
  # 标准化配置
  standardize_authors = TRUE,     # 是否标准化作者信息
  standardize_institutions = TRUE, # 是否标准化机构信息
  standardize_keywords = TRUE,    # 是否标准化关键词
  standardize_journals = TRUE     # 是否标准化期刊信息
)

# 创建必要的目录
for (dir_path in c(config$logs_dir, config$reports_dir)) {
  if (!dir.exists(dir_path)) {
    dir.create(dir_path, recursive = TRUE)
  }
}

# --- API请求核心函数 (增强版) ---
safe_api_request <- function(url, doi = NULL, api_type = "openalex") {
  # 只在调试模式下显示URL
  if (FALSE) {  # 设置为TRUE可以启用详细调试
    cat("请求API:", url, "\n")
  }
  
  # 如果提供了DOI，尝试从备选API获取（如果主API失败）
  alt_url <- NULL
  if (config$api_timeout_settings$use_alt_api && !is.null(doi)) {
    if (api_type == "openalex") {
      # 备选方案1: 使用S2 API作为OpenAlex的备选
      alt_url <- paste0("https://api.semanticscholar.org/v1/paper/", URLencode(doi, reserved = TRUE))
    } else if (api_type == "crossref") {
      # 备选方案2: 使用DataCite API作为CrossRef的备选
      alt_url <- paste0("https://api.datacite.org/works/", URLencode(doi, reserved = TRUE))
    }
  }
  
  # 尝试主API请求
  for (attempt in 1:config$api_timeout_settings$retries) {
    tryCatch({
      # 创建带有更多超时设置的handle
      handle <- curl::new_handle()
      curl::handle_setopt(handle, 
                         timeout = config$api_timeout_settings$timeout,
                         connecttimeout = config$api_timeout_settings$connecttimeout,
                         low_speed_limit = 10,
                         low_speed_time = 60,
                         httpheader = c("User-Agent" = config$api_timeout_settings$user_agent),
                         ssl_verifypeer = FALSE,  # 禁用SSL验证，可能解决一些SSL问题
                         verbose = FALSE)         # 关闭详细输出
      
      # 执行请求
      response <- curl::curl_fetch_memory(url, handle = handle)
      
      if (response$status_code >= 200 && response$status_code < 300) {
        return(list(success = TRUE, data = rawToChar(response$content), source = "primary"))
      } else {
        if (attempt < config$api_timeout_settings$retries) {
          Sys.sleep(config$api_timeout_settings$retry_delay)
        }
      }
    }, error = function(e) {
      if (attempt < config$api_timeout_settings$retries) {
        wait_time <- config$api_timeout_settings$retry_delay * attempt  # 渐进式增加等待时间
        Sys.sleep(wait_time)
      }
    })
  }
  
  # 如果主API全部失败且存在备选API，则尝试备选API
  if (!is.null(alt_url)) {
    for (attempt in 1:2) { # 备选API少尝试几次
      tryCatch({
        # 备选API的handle
        handle <- curl::new_handle()
        curl::handle_setopt(handle, 
                           timeout = config$api_timeout_settings$timeout,
                           connecttimeout = config$api_timeout_settings$connecttimeout,
                           httpheader = c("User-Agent" = config$api_timeout_settings$user_agent),
                           ssl_verifypeer = FALSE)
        
        # 执行备选请求
        response <- curl::curl_fetch_memory(alt_url, handle = handle)
        
        if (response$status_code >= 200 && response$status_code < 300) {
          return(list(success = TRUE, data = rawToChar(response$content), source = "alternative"))
        } else {
          Sys.sleep(5) # 简单等待
        }
      }, error = function(e) {
        Sys.sleep(5)
      })
    }
  }
  
  # 所有尝试都失败
  return(list(success = FALSE, data = NA_character_, source = "none"))
}

# --- 数据库连接管理函数 ---
initialize_db_connection <- function(db_file_path) {
  tryCatch({
    if (!file.exists(db_file_path)) {
      dir.create(dirname(db_file_path), showWarnings = FALSE, recursive = TRUE)
      con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
      DBI::dbExecute(con, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
      return(con)
    } else {
      con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
      # 验证连接是否有效
      tryCatch({
        DBI::dbExecute(con, "SELECT 1")
      }, error = function(e) {
        # 如果连接无效，尝试修复数据库
        message("数据库连接验证失败，尝试修复数据库...")
        DBI::dbDisconnect(con, force = TRUE)
        # 创建备份
        backup_file <- paste0(db_file_path, ".backup_", format(Sys.time(), "%Y%m%d%H%M%S"))
        file.copy(db_file_path, backup_file)
        # 重新连接
        con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
        DBI::dbExecute(con, "PRAGMA integrity_check")
        DBI::dbExecute(con, "VACUUM")
      })
      return(con)
    }
  }, error = function(e) {
    message(paste("数据库连接初始化失败:", e$message))
    # 如果创建连接失败，尝试使用临时文件
    temp_db <- tempfile(fileext = ".sqlite")
    message(paste("尝试使用临时数据库:", temp_db))
    con <- DBI::dbConnect(RSQLite::SQLite(), temp_db)
    DBI::dbExecute(con, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
    return(con)
  })
}

safe_query_cache <- function(query, params = NULL, db_file_path) {
  max_retries <- 3
  retry_count <- 0
  con_local <- NULL # 定义局部连接变量
  
  while (retry_count < max_retries) {
    tryCatch({
      # 在每次尝试中确保数据库连接有效，或者重新建立
      if (is.null(con_local) || !DBI::dbIsValid(con_local)) {
        con_local <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
        # 确保表存在
        DBI::dbExecute(con_local, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
      }

      # 执行查询
      if (is.null(params)) {
        result <- DBI::dbGetQuery(con_local, query)
      } else {
        stmt <- DBI::dbSendQuery(con_local, query)
        DBI::dbBind(stmt, params)
        result <- DBI::dbFetch(stmt)
        DBI::dbClearResult(stmt)
      }
      
      return(list(success = TRUE, result = result))
    }, error = function(e) {
      retry_count <<- retry_count + 1
      message(paste("数据库查询失败 (尝试", retry_count, "/", max_retries, "):", e$message))
      Sys.sleep(1)  # 短暂延迟后重试
      
      # 如果连接无效或出现错误，尝试强制关闭并重置以便下次循环重新建立
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local, force = TRUE) }, error = function(e) {})
      }
      con_local <<- NULL # 强制重新初始化连接
    }, finally = {
      # 确保在函数退出时关闭连接
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local) }, error = function(e) {})
      }
    })
  }
  
  # 所有重试都失败
  message("数据库操作失败，达到最大重试次数")
  return(list(success = FALSE, result = data.frame()))
}

safe_write_cache <- function(doi, api, response, db_file_path) {
  max_retries <- 3
  retry_count <- 0
  con_local <- NULL # 定义局部连接变量
  
  while (retry_count < max_retries) {
    tryCatch({
      # 在每次尝试中确保数据库连接有效，或者重新建立
      if (is.null(con_local) || !DBI::dbIsValid(con_local)) {
        con_local <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
        # 确保表存在
        DBI::dbExecute(con_local, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
      }
      
      # 准备写入
      query <- "INSERT OR REPLACE INTO api_cache (doi, api, response, timestamp) VALUES (?, ?, ?, ?)"
      timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
      
      # 执行
      stmt <- DBI::dbSendQuery(con_local, query)
      DBI::dbBind(stmt, list(doi, api, response, timestamp))
      DBI::dbClearResult(stmt)
      
      return(list(success = TRUE))
    }, error = function(e) {
      retry_count <<- retry_count + 1
      message(paste("数据库写入失败 (尝试", retry_count, "/", max_retries, "):", e$message))
      Sys.sleep(1)  # 短暂延迟后重试
      
      # 如果连接无效或出现错误，尝试强制关闭并重置以便下次循环重新建立
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local, force = TRUE) }, error = function(e) {})
      }
      con_local <<- NULL # 强制重新初始化连接
    }, finally = {
      # 确保在函数退出时关闭连接
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local) }, error = function(e) {})
      }
    })
  }
  
  # 所有重试都失败
  message("数据库写入失败，达到最大重试次数")
  return(list(success = FALSE))
}

# --- 数据库连接清理函数 ---
cleanup_db_connection <- function() {
  tryCatch({
    if (exists("con") && DBI::dbIsValid(con)) {
      message("正在安全关闭数据库连接...")
      suppressWarnings({
        DBI::dbDisconnect(con)
      })
    } else {
      message("数据库连接不存在或已经关闭。")
    }
  }, error = function(e) {
    message(paste("关闭数据库连接时发生错误:", e$message))
    # 尝试强制关闭
    if (exists("con") && !is.null(con)) {
      suppressWarnings({
        try(DBI::dbDisconnect(con, force = TRUE), silent = TRUE)
      })
    }
  })
  
  # 确保连接变量被清理
  if (exists("con")) {
    con <<- NULL # 使用 <<- 修改全局变量
  }
}

# 在脚本结束时执行清理
on.exit(cleanup_db_connection(), add = TRUE)

# --- 基于bibliometrix源代码的字段标准化函数 ---

# 作者信息标准化（基于bibliometrix::convert2df中的实现）
standardize_authors_bibliometrix <- function(authors_string) {
  if (is.na(authors_string) || authors_string == "") {
    return(NA_character_)
  }
  
  # 分割作者
  authors <- strsplit(authors_string, ";")[[1]]
  
  # 标准化每个作者（基于bibliometrix的实现）
  standardized_authors <- sapply(authors, function(author) {
    # 移除非字母数字字符（保留连字符和撇号）
    author <- gsub("[^[:alnum:][-]']", " ", author)
    # 去除首尾空格
    author <- trimws(author)
    return(author)
  })
  
  return(paste(standardized_authors, collapse = ";"))
}

# 机构信息标准化（基于bibliometrix::metaTagExtraction中的AU_UN实现）
standardize_institutions_bibliometrix <- function(institutions_string) {
  if (is.na(institutions_string) || institutions_string == "") {
    return(NA_character_)
  }
  
  # 移除方括号标记
  institutions <- gsub("\\[.*?\\] ", "", institutions_string)
  
  # 分割机构
  institution_list <- strsplit(institutions, ";")[[1]]
  
  # 机构类型标签（基于bibliometrix的uTags）
  uTags <- c("UNIV", "COLL", "SCH", "INST", "ACAD", "ECOLE", "CTR", "SCI", 
             "CENTRE", "CENTER", "CENTRO", "HOSP", "ASSOC", "COUNCIL",
             "FONDAZ", "FOUNDAT", "ISTIT", "LAB", "TECH", "RES", "CNR", 
             "ARCH", "SCUOLA", "PATENT OFF", "CENT LIB", "HEALTH", "NATL",
             "LIBRAR", "CLIN", "FDN", "OECD", "FAC", "WORLD BANK", 
             "POLITECN", "INT MONETARY FUND", "CLIMA", "METEOR", "OFFICE", 
             "ENVIR", "CONSORTIUM", "OBSERVAT", "AGRI", "MIT ", "INFN", "SUNY ")
  
  # 标准化每个机构
  standardized_institutions <- sapply(institution_list, function(institution) {
    # 移除重印作者标记
    institution <- gsub("\\(REPRINT AUTHOR\\)", "", institution)
    
    # 分割地址组件
    address_components <- strsplit(institution, ",")[[1]]
    
    # 查找机构类型标签
    tag_indices <- sapply(uTags, function(tag) {
      which(grepl(tag, address_components, fixed = TRUE))
    })
    tag_indices <- unlist(tag_indices)
    
    if (length(tag_indices) == 0) {
      return("NOTREPORTED")
    } else {
      # 检查是否包含数字（未声明的机构）
      if (grepl("[[:digit:]]", address_components[tag_indices[1]])) {
        return("NOTDECLARED")
      } else {
        return(trimws(address_components[tag_indices[1]]))
      }
    }
  })
  
  # 过滤掉NOTREPORTED和NOTDECLARED
  valid_institutions <- standardized_institutions[!standardized_institutions %in% c("NOTREPORTED", "NOTDECLARED")]
  
  if (length(valid_institutions) == 0) {
    return(NA_character_)
  } else {
    return(paste(valid_institutions, collapse = ";"))
  }
}

# 关键词标准化（基于bibliometrix::termExtraction的实现）
standardize_keywords_bibliometrix <- function(keywords_string) {
  if (is.na(keywords_string) || keywords_string == "") {
    return(NA_character_)
  }
  
  # 分割关键词
  keywords <- strsplit(keywords_string, ";")[[1]]
  
  # 标准化每个关键词（基于bibliometrix的处理方式）
  standardized_keywords <- sapply(keywords, function(keyword) {
    # 去除首尾空格
    keyword <- trimws(keyword)
    
    # 将连字符替换为双下划线（保持多词关键词）
    keyword <- gsub("-", "__", keyword)
    
    # 将空格替换为下划线（保持多词关键词）
    keyword <- gsub(" ", "_", keyword)
    
    # 转换为小写
    keyword <- tolower(keyword)
    
    return(keyword)
  })
  
  return(paste(standardized_keywords, collapse = ";"))
}

# 期刊信息标准化（基于bibliometrix的处理方式）
standardize_journals_bibliometrix <- function(journal_string) {
  if (is.na(journal_string) || journal_string == "") {
    return(NA_character_)
  }
  
  # 去除首尾空格
  journal <- trimws(journal_string)
  
  # 移除多余的空格
  journal <- gsub("\\s+", " ", journal)
  
  # 处理常见的期刊缩写（基于bibliometrix的处理）
  journal <- gsub("\\bJ\\.\\s*", "Journal of ", journal)
  journal <- gsub("\\bInt\\.\\s*", "International ", journal)
  journal <- gsub("\\bRes\\.\\s*", "Research ", journal)
  
  return(journal)
}

# 参考文献清理（基于bibliometrix::convert2df的实现）
clean_references_bibliometrix <- function(references_string) {
  if (is.na(references_string) || references_string == "") {
    return("none")
  }
  
  # 基于bibliometrix的CR字段清理
  cleaned_refs <- gsub("\\[,||\\[||\\]|| \\.\\. || \\. ", "", references_string)
  
  return(cleaned_refs)
}

# --- API补全函数 ---

# 通过标题查找DOI的函数
search_doi_by_title <- function(title, authors = NULL, year = NULL) {
  tryCatch({
    # 清理和准备搜索查询
    clean_title <- gsub("[[:punct:]]", " ", title)  # 移除标点符号
    clean_title <- gsub("\\s+", " ", clean_title)   # 合并多个空格
    clean_title <- trimws(clean_title)              # 去除首尾空格
    
    # 构建rcrossref的查询参数 - 改进查询策略
    rcr_query_title <- clean_title
    
    # 改进作者处理：提取姓氏而不是全名
    rcr_query_author <- NULL
    if (!is.null(authors) && !is.na(authors) && authors != "") {
      first_author <- strsplit(authors, ";")[[1]][1]
      if (!is.na(first_author) && first_author != "") {
        # 提取姓氏（最后一个词）
        author_parts <- strsplit(trimws(gsub("[[:punct:]]", " ", first_author)), "\\s+")[[1]]
        if (length(author_parts) > 0) {
          rcr_query_author <- author_parts[length(author_parts)]  # 取最后一个词作为姓氏
        }
      }
    }
    
    rcr_filter <- NULL
    if (!is.null(year) && !is.na(year)) {
      rcr_filter <- c(from_pub_date = as.character(year), until_pub_date = as.character(year))
    }
    
    # 临时调试输出：显示rcrossref查询参数
    log_message(sprintf("rcrossref查询: title='%s', author='%s', year='%s'", 
                        rcr_query_title, rcr_query_author %||% "", year %||% ""), "debug")
    
    # 使用rcrossref::cr_works进行查询 - 尝试不同的查询策略
    crossref_result <- NULL
    
    # 策略1：使用标题和作者（如果作者可用）
    if (!is.null(rcr_query_author) && rcr_query_author != "") {
      log_message(sprintf("尝试策略1：标题+作者查询 (作者: %s)", rcr_query_author), "debug")
      crossref_result <- rcrossref::cr_works(query.title = rcr_query_title,
                                             query.author = rcr_query_author,
                                             filter = rcr_filter,
                                             limit = 10)
    }
    
    # 策略2：如果策略1没有结果，尝试只用标题
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("尝试策略2：仅标题查询", "debug")
      crossref_result <- rcrossref::cr_works(query.title = rcr_query_title,
                                             filter = rcr_filter,
                                             limit = 10)
    }
    
    # 策略3：如果前两个策略都失败，尝试使用query参数（更宽泛的搜索）
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("尝试策略3：使用query参数进行宽泛搜索", "debug")
      # 构建查询字符串
      query_string <- rcr_query_title
      if (!is.null(rcr_query_author) && rcr_query_author != "") {
        query_string <- paste(query_string, rcr_query_author)
      }
      
      crossref_result <- rcrossref::cr_works(query = query_string,
                                             filter = rcr_filter,
                                             limit = 10)
    }
    
    if (!is.null(crossref_result$data) && nrow(crossref_result$data) > 0) {
      # 临时调试输出：显示rcrossref返回的原始数据
      log_message(sprintf("rcrossref返回数据（前5条）: %s", 
                                 paste(capture.output(print(head(crossref_result$data, 5))), collapse = "\n")), "debug")
      
      # 进行标题匹配验证
      best_match_idx <- NULL
      best_similarity <- 0
      
      for (i in 1:nrow(crossref_result$data)) {
        # 修复title字段访问方式
        candidate_title <- crossref_result$data$title[i]
        if (is.null(candidate_title) || is.na(candidate_title) || candidate_title == "") {
          next
        }
        
        current_similarity <- string_similarity(title, candidate_title)
        
        # 添加详细调试信息
        log_message(sprintf("候选标题 %d: '%s' (相似度: %.2f)", i, candidate_title, current_similarity), "debug")
        
        if (current_similarity > best_similarity) {
          best_similarity <- current_similarity
          best_match_idx <- i
        }
      }
      
      # 临时调试输出：显示最佳匹配相似度
      log_message(sprintf("最佳匹配相似度: %.2f (阈值: 0.8)", best_similarity), "debug")

      if (!is.null(best_match_idx) && best_similarity > 0.8) { # 恢复严格的相似度阈值
        doi_found <- crossref_result$data$doi[best_match_idx] # 注意：rcrossref返回的是小写的doi字段
        if (!is.na(doi_found) && doi_found != "") {
          log_message(sprintf("通过rcrossref成功找到DOI: %s", doi_found), "debug")
          return(doi_found)
        }
      } else {
        log_message(sprintf("相似度 %.2f 低于阈值 0.8，未找到匹配", best_similarity), "debug")
      }
    } else {
      log_message("rcrossref未返回任何结果", "debug")
    }
    
    return(NA_character_)
  }, error = function(e) {
    log_message(sprintf("search_doi_by_title 发生错误: %s", e$message), "error")
    return(NA_character_)
  })
}

# 简单的字符串相似度计算
string_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  
  # 转换为小写并移除标点符号
  str1_clean <- tolower(gsub("[[:punct:]]", "", str1))
  str2_clean <- tolower(gsub("[[:punct:]]", "", str2))
  
  # 计算编辑距离
  distance <- adist(str1_clean, str2_clean)[1, 1]
  
  # 计算相似度
  max_len <- max(nchar(str1_clean), nchar(str2_clean))
  if (max_len == 0) return(1)
  
  similarity <- 1 - (distance / max_len)
  return(similarity)
}

# 安全的Crossref API查询
safe_crossref_query <- function(doi, use_cache = config$cache_api_results) {
  # 确保API缓存目录存在（因为cache_api_results会用到）
  api_cache_dir <- dirname(config$api_cache_file)
  if (!dir.exists(api_cache_dir)) {
    dir.create(api_cache_dir, recursive = TRUE)
  }
  
  # 检查缓存
  if (use_cache) {
    query_result <- safe_query_cache(
      "SELECT response FROM api_cache WHERE doi = ? AND api = 'crossref'", 
      params = list(doi),
      config$api_cache_file
    )
    
    if (query_result$success && nrow(query_result$result) > 0 && !is.na(query_result$result$response[1])) {
      log_message(sprintf("使用Crossref API缓存结果: %s", doi), "info")
      return(list(success = TRUE, data = fromJSON(query_result$result$response[1])))
    }
  }
  
  # 编码DOI
  doi_encoded <- URLencode(doi, reserved = TRUE)
  url <- sprintf("https://api.crossref.org/works/%s", doi_encoded)
  
  # 使用新的 safe_api_request 函数
  api_result <- safe_api_request(url, doi, "crossref")
  
  # 处理结果
  if (api_result$success) {
    response_data <- api_result$data
    
    # 缓存结果
    if (use_cache) {
      write_result <- safe_write_cache(doi, "crossref", response_data, config$api_cache_file)
    }
    
    return(list(success = TRUE, data = fromJSON(response_data)))
  } else {
    log_message(sprintf("无法获取Crossref数据: %s", doi), "warning")
    return(list(success = FALSE, data = NULL))
  }
}

# 简化的OpenAlex API查询
safe_openalex_query <- function(doi, use_cache = config$cache_api_results) {
  # 确保API缓存目录存在（因为cache_api_results会用到）
  api_cache_dir <- dirname(config$api_cache_file)
  if (!dir.exists(api_cache_dir)) {
    dir.create(api_cache_dir, recursive = TRUE)
  }

  # 检查缓存
  if (use_cache) {
    query_result <- safe_query_cache(
      "SELECT response FROM api_cache WHERE doi = ? AND api = 'openalex'",
      params = list(doi),
      config$api_cache_file
    )

    if (query_result$success && nrow(query_result$result) > 0 && !is.na(query_result$result$response[1])) {
      log_message(sprintf("使用OpenAlex API缓存结果: %s", doi), "info")
      return(list(success = TRUE, data = fromJSON(query_result$result$response[1])))
    }
  }

  # 构建API URL
  doi_encoded <- URLencode(doi, reserved = TRUE)
  url <- sprintf("https://api.openalex.org/works/doi:%s", doi_encoded)

  # 使用新的 safe_api_request 函数
  api_result <- safe_api_request(url, doi, "openalex")

  # 处理结果
  if (api_result$success) {
    response_data <- api_result$data

    # 缓存结果
    if (use_cache) {
      write_result <- safe_write_cache(doi, "openalex", response_data, config$api_cache_file)
    }

    return(list(success = TRUE, data = fromJSON(response_data)))
  } else {
    log_message(sprintf("无法获取OpenAlex数据: %s", doi), "warning")
    return(list(success = FALSE, data = NULL))
  }
}

# --- 数据质量评估函数 ---

# 计算字段完整性
calculate_field_completeness <- function(data) {
  field_stats <- sapply(colnames(data), function(col) {
    sum(!is.na(data[[col]]) & data[[col]] != "") / nrow(data)
  })
  
  return(field_stats)
}

# 检测数据异常
detect_data_anomalies <- function(data) {
  anomalies <- list()
  
  # 检测年份异常
  if ("PY" %in% colnames(data)) {
    year_anomalies <- data %>%
      filter(!is.na(PY) & (PY < 1900 | PY > as.numeric(format(Sys.Date(), "%Y")) + 1)) %>%
      select(UT, TI, PY) %>%
      mutate(anomaly_type = "异常年份")
    
    if (nrow(year_anomalies) > 0) {
      anomalies$year_anomalies <- year_anomalies
    }
  }
  
  # 检测标题长度异常
  if ("TI" %in% colnames(data)) {
    title_anomalies <- data %>%
      filter(!is.na(TI) & (nchar(TI) < 10 | nchar(TI) > 500)) %>%
      select(UT, TI) %>%
      mutate(
        title_length = nchar(TI),
        anomaly_type = ifelse(nchar(TI) < 10, "标题过短", "标题过长")
      )
    
    if (nrow(title_anomalies) > 0) {
      anomalies$title_anomalies <- title_anomalies
    }
  }
  
  return(anomalies)
}

# --- 主函数 ---
main <- function() {
  log_message("开始数据增强流程（基于bibliometrix标准）")
  
  # 0. API缓存数据库路径创建 (不再初始化全局连接)
  api_cache_dir <- dirname(config$api_cache_file)
  if (!dir.exists(api_cache_dir)) {
    dir.create(api_cache_dir, recursive = TRUE)
    log_message(sprintf("已创建API缓存目录: %s", api_cache_dir), "info")
  }

  # 设置并行策略
  if (config$use_parallel) {
    plan(config$parallel_strategy)
    log_message(sprintf("并行策略已设置为: %s", config$parallel_strategy))
  } else {
    plan("sequential")
    log_message("并行处理已禁用，使用顺序执行")
  }

  # 1. 加载数据
  if (!file.exists(config$input_file)) {
    stop(sprintf("输入文件不存在: %s", config$input_file))
  }
  
  M <- readRDS(config$input_file)
  log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
  
  # 2. 基于bibliometrix的字段标准化
  log_message("开始基于bibliometrix标准的字段标准化")
  
  # 2.1 作者标准化
  if (config$standardize_authors && "AU" %in% colnames(M)) {
    M$AU_bibliometrix <- sapply(M$AU, standardize_authors_bibliometrix)
    log_message("作者信息标准化完成（基于bibliometrix标准）")
  }
  
  # 2.2 机构标准化
  if (config$standardize_institutions && "C1" %in% colnames(M)) {
    M$C1_bibliometrix <- sapply(M$C1, standardize_institutions_bibliometrix)
    log_message("机构信息标准化完成（基于bibliometrix标准）")
  }
  
  # 2.3 关键词标准化
  if (config$standardize_keywords && "DE" %in% colnames(M)) {
    M$DE_bibliometrix <- sapply(M$DE, standardize_keywords_bibliometrix)
    log_message("关键词标准化完成（基于bibliometrix标准）")
  }
  
  # 2.4 期刊标准化
  if (config$standardize_journals && "SO" %in% colnames(M)) {
    M$SO_bibliometrix <- sapply(M$SO, standardize_journals_bibliometrix)
    log_message("期刊信息标准化完成（基于bibliometrix标准）")
  }
  
  # 2.5 参考文献清理
  if ("CR" %in% colnames(M)) {
    M$CR_bibliometrix <- sapply(M$CR, clean_references_bibliometrix)
    log_message("参考文献清理完成（基于bibliometrix标准）")
  }
  
  # 2.6 移除重复分号（基于bibliometrix的处理）
  M <- M %>% 
    mutate_if(is.character, ~gsub(";;",";",.x))
  
  # 3. 数据质量评估（增强前）
  log_message("开始数据质量评估（增强前）")
  
  field_completeness_before <- calculate_field_completeness(M)
  anomalies_before <- detect_data_anomalies(M)
  
  # 显示关键字段的完整性
  key_fields <- c("TI", "AU", "PY", "SO", "DI", "AB", "DE", "TC")
  log_message("增强前字段完整性:")
  for (field in key_fields) {
    if (field %in% names(field_completeness_before)) {
      log_message(sprintf("  %s: %.1f%%", field, 100 * field_completeness_before[field]))
    }
  }
  
  # 4. API数据补全
  log_message("开始API数据补全")
  
  # 4.1 查找并补全缺失的DOI
  log_message("4.1 正在查找并补全缺失的DOI...")
  
  # 获取缺失DOI的记录 (确保UT是唯一的标识符)
  missing_doi_records <- M[is.na(M$DI) | M$DI == "", ]
  
  log_message(sprintf("缺失DOI的记录数: %d", nrow(missing_doi_records)))
  
  if (nrow(missing_doi_records) > 0) {
    # 限制处理数量以避免API限制
    max_records_to_process_doi_lookup <- min(nrow(missing_doi_records), 2) # 限制前2个
    records_to_process_doi_lookup <- missing_doi_records[1:max_records_to_process_doi_lookup, ]
    
    log_message(sprintf("处理前%d个缺失DOI的记录尝试补全DOI (并行化)", max_records_to_process_doi_lookup))
    
    # 使用进度条（针对future_map）
    with_progress({
      pb_doi <- progressor(steps = nrow(records_to_process_doi_lookup))
      
      # 并行查找DOI
      # 使用purrr::map_dfr而不是future_map，因为future_map_dfr会并行化对行的迭代，但每个迭代内的search_doi_by_title仍是串行的
      # 这里我们是希望 search_doi_by_title 本身是并行的，因此需要使用 future_map 来处理 record 的索引
      # 或者更直接的方式是 future_map_dfr 将每个record作为输入，但这样需要search_doi_by_title能处理单行df
      # 为了简单且有效，我们将直接在future_map中传递需要的参数

      # 收集需要查找DOI的记录的索引
      records_indices_to_lookup <- 1:nrow(records_to_process_doi_lookup)
      
      found_dois_list <- future_map(records_indices_to_lookup, ~{
        pb_doi()
        record <- records_to_process_doi_lookup[.x, ]
        
        found_doi_val <- NA_character_
        if (!is.na(record$TI) && record$TI != "") {
          found_doi_val <- search_doi_by_title(record$TI, record$AU, record$PY)
        }
        Sys.sleep(config$throttle_delay) # 防止API限制
        
        return(list(original_ut = record$UT, found_doi = found_doi_val))
      }, .options = furrr_options(seed = TRUE))
    })
    
    # 处理并行查找的结果并更新M
    doi_found_count <- 0
    for (result_item in found_dois_list) {
      if (!is.na(result_item$found_doi) && result_item$found_doi != "") {
        original_row_index <- which(M$UT == result_item$original_ut) 
        if (length(original_row_index) > 0) {
          M$DI[original_row_index] <- result_item$found_doi
          doi_found_count <- doi_found_count + 1
          log_message(sprintf("为记录 %s 成功补全DOI: %s", result_item$original_ut, result_item$found_doi), "info")
        }
      }
    }
    
    log_message(sprintf("DOI补全查找完成: 成功找到并补全了 %d 个新DOI", doi_found_count), "info")
  } else {
    log_message("没有缺失DOI的记录需要补全", "info")
  }
  
  # 4.2 通过DOI获取补充数据 (Crossref & OpenAlex)
  log_message("4.2 正在通过DOI获取补充数据 (Crossref & OpenAlex) (并行化)...")
  
  # 获取所有现有DOI (包括原始的和新补全的)
  all_dois_for_api <- unique(na.omit(M$DI[M$DI != ""]))
  
  if (length(all_dois_for_api) > 0) {
    log_message(sprintf("发现 %d 个唯一DOI用于API数据获取", length(all_dois_for_api)))
    
    # 使用进度条（针对future_map）
    with_progress({
      pb_api <- progressor(steps = length(all_dois_for_api))
      
      api_results_parallel <- future_map(all_dois_for_api, ~{
        pb_api()
        current_doi <- .x
        
        cr_data <- NULL
        oa_data <- NULL
        cr_success <- FALSE
        oa_success <- FALSE
        
        if (config$use_crossref_api) {
          cr_result <- safe_crossref_query(current_doi)
          if (cr_result$success) {
            cr_data <- cr_result$data
            cr_success <- TRUE
          }
        }
        
        if (config$use_openalex_api) {
          oa_result <- safe_openalex_query(current_doi)
          if (oa_result$success) {
            oa_data <- oa_result$data
            oa_success <- TRUE
          }
        }
        
        Sys.sleep(config$throttle_delay) # 防止API限制
        
        return(list(
          doi = current_doi,
          crossref_data = cr_data,
          openalex_data = oa_data,
          crossref_success = cr_success,
          openalex_success = oa_success
        ))
      }, .options = furrr_options(seed = TRUE))
    })
    
    crossref_data_list <- list()
    openalex_data_list <- list()
    crossref_success_count <- 0
    openalex_success_count <- 0
    
    # 处理并行结果
    for (result_item in api_results_parallel) {
      if (result_item$crossref_success) {
        crossref_data_list[[result_item$doi]] <- result_item$crossref_data
        crossref_success_count <- crossref_success_count + 1
      }
      if (result_item$openalex_success) {
        openalex_data_list[[result_item$doi]] <- result_item$openalex_data
        openalex_success_count <- openalex_success_count + 1
      }
    }
    
    log_message(sprintf("API数据获取完成: Crossref成功 %d 个, OpenAlex成功 %d 个", 
                       crossref_success_count, openalex_success_count), "info")
    
    # 将API结果合并到主数据框 M
    log_message("正在将API数据合并到主数据框...")
    
    # 处理Crossref数据
    if (length(crossref_data_list) > 0) {
      crossref_enhanced <- lapply(names(crossref_data_list), function(doi_key) {
        data <- crossref_data_list[[doi_key]]
        list(
          doi = doi_key,
          title_cr = data$title[1] %||% NA_character_,
          authors_cr = paste(data$author$given %||% "", data$author$family %||% "", collapse = "; "),
          journal_cr = data$`container-title`[1] %||% NA_character_,
          year_cr = data$published$`date-parts`[1][1] %||% NA_integer_,
          abstract_cr = data$abstract %||% NA_character_,
          publisher_cr = data$publisher %||% NA_character_,
          type_cr = data$type %||% NA_character_,
          issn_p_cr = data$ISSN[grepl("print", data$ISSN.type$type, ignore.case = TRUE)][1] %||% NA_character_,
          issn_e_cr = data$ISSN[grepl("electronic", data$ISSN.type$type, ignore.case = TRUE)][1] %||% NA_character_
        )
      })
      crossref_df <- bind_rows(crossref_enhanced) %>% mutate(doi = str_to_lower(doi))
      M <- M %>% left_join(crossref_df, by = c("DI" = "doi"))
    }
    
    # 处理OpenAlex数据
    if (length(openalex_data_list) > 0) {
      openalex_enhanced <- lapply(names(openalex_data_list), function(doi_key) {
        data <- openalex_data_list[[doi_key]]
        list(
          doi = doi_key,
          openalex_id = data$id %||% NA_character_,
          title_oa = data$title %||% NA_character_,
          authors_oa = paste(data$authorships$author$display_name %||% "", collapse = "; "),
          journal_oa = data$primary_location$source$display_name %||% NA_character_,
          year_oa = data$publication_year %||% NA_integer_,
          cited_by_count_oa = data$cited_by_count %||% NA_integer_,
          oa_status = data$open_access$oa_status %||% NA_character_,
          concepts_oa = paste(data$concepts$display_name %||% "", collapse = "; ")
        )
      })
      openalex_df <- bind_rows(openalex_enhanced) %>% mutate(doi = str_to_lower(doi))
      M <- M %>% left_join(openalex_df, by = c("DI" = "doi"))
    }
    log_message("API数据合并完成", "info")

  } else {
    log_message("没有有效的DOI进行API数据获取", "info")
  }
  
  # 5. 数据质量评估（增强后）
  log_message("开始数据质量评估（增强后）")
  
  field_completeness_after <- calculate_field_completeness(M)
  anomalies_after <- detect_data_anomalies(M)
  
  log_message("增强后字段完整性:")
  for (field in key_fields) {
    if (field %in% names(field_completeness_after)) {
      improvement <- field_completeness_after[field] - field_completeness_before[field]
      log_message(sprintf("  %s: %.1f%% (%+.1f%%)", field, 
                         100 * field_completeness_after[field], 
                         100 * improvement))
    }
  }
  
  # 6. 保存增强数据
  saveRDS(M, config$output_file)
  log_message(sprintf("增强数据已保存: %s", config$output_file))
  
  # 7. 生成增强报告
  report_file <- file.path(config$reports_dir, "data_enhancement_report.txt")
  sink(report_file)
  cat("=== 数据增强报告（基于bibliometrix标准） ===\n")
  cat("生成时间:", format(Sys.time()), "\n\n")
  cat("数据统计:\n")
  cat("- 原始记录数:", nrow(M), "\n")
  cat("- 最终记录数:", nrow(M), "\n")
  cat("- 新增字段数:", sum(grepl("_bibliometrix$", colnames(M))), "\n\n")
  
  cat("字段完整性对比:\n")
  for (field in key_fields) {
    if (field %in% names(field_completeness_before)) {
      before_pct <- 100 * field_completeness_before[field]
      after_pct <- 100 * field_completeness_after[field]
      improvement <- after_pct - before_pct
      cat(sprintf("- %s: %.1f%% → %.1f%% (%+.1f%%)\n", 
                  field, before_pct, after_pct, improvement))
    }
  }
  
  cat("\n新增字段（基于bibliometrix标准）:\n")
  new_fields <- colnames(M)[grepl("_bibliometrix$", colnames(M))]
  for (field in new_fields) {
    cat("-", field, "\n")
  }
  
  cat("\nAPI补全统计:\n")
  cat("- 有效DOI数量:", length(all_dois_for_api), "\n")
  cat("- 处理DOI数量:", ifelse(length(all_dois_for_api) > 0, min(length(all_dois_for_api), 100), 0), "\n")
  cat("- Crossref成功:", ifelse(exists("crossref_success_count"), crossref_success_count, 0), "\n")
  cat("- OpenAlex成功:", ifelse(exists("openalex_success_count"), openalex_success_count, 0), "\n")
  
  cat("\n标准化方法说明:\n")
  cat("- 作者标准化: 基于bibliometrix::convert2df中的AU字段处理\n")
  cat("- 机构标准化: 基于bibliometrix::metaTagExtraction中的AU_UN字段处理\n")
  cat("- 关键词标准化: 基于bibliometrix::termExtraction中的关键词处理\n")
  cat("- 期刊标准化: 基于bibliometrix的期刊名称处理\n")
  cat("- 参考文献清理: 基于bibliometrix::convert2df中的CR字段处理\n")
  sink()
  
  log_message(sprintf("增强报告已保存: %s", report_file))
  log_message("数据增强流程完成（基于bibliometrix标准）")
  
  return(M)
}

# 运行主函数
enhanced_data <- main() 
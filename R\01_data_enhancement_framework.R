# 01_data_enhancement_framework.R
# 数据增强流程 - 基于两阶段框架思路
# 专注于API补全和字段标准化，为后续去重做准备
# 参考bibliometrix源代码实现字段标准化

# 加载必要的包
# 检查并安装所需的包
required_packages <- c("here", "dplyr", "stringr", "bibliometrix", "httr", "jsonlite", "progress", "DBI", "RSQLite", "curl", "future", "furrr", "progressr", "rcrossref", "openxlsx")

# 设置CRAN镜像，确保包能被正确下载
options(repos = c(CRAN = "https://cloud.r-project.org/"))

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    message(sprintf("正在安装缺失的包: %s", pkg))
    install.packages(pkg, dependencies = TRUE, repos = "https://cloud.r-project.org/")
    library(pkg, character.only = TRUE)
  } else {
    message(sprintf("包已加载: %s", pkg))
  }
}

# library(here)
# library(dplyr)
# library(stringr)
# library(bibliometrix)
# library(httr)
# library(jsonlite)
# library(progress)
# library(DBI)      # 新增：数据库接口
# library(RSQLite)  # 新增：SQLite数据库支持
# library(curl)     # 新增：HTTP请求的底层库，用于更高级的控制
# library(future)   # 新增：并行和异步编程
# library(furrr)    # 新增：适用于purrr函数的并行化

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

# 配置
config <- list(
  # 路径配置
  input_file = here("data_repository", "02_enhanced_dataset", "enhanced_data_initial.rds"),
  output_file = here("data_repository", "02_enhanced_dataset", "enhanced_data_optimized.rds"),
  logs_dir = here("data_repository", "05_execution_logs"),
  reports_dir = here("data_repository", "04_enhancement_reports"),
  api_cache_file = here("data_repository", "06_api_cache", "api_cache_enhanced.sqlite"),
  
  # API配置
  use_crossref_api = TRUE,
  use_openalex_api = TRUE,
  
  api_timeout_settings = list(
    timeout = 180,
    connecttimeout = 120,
    retries = 5,
    retry_delay = 15,
    user_agent = "bibliometric-analysis/1.0 (mailto:<EMAIL>; 研究用途)",
    use_alt_api = TRUE
  ),
  batch_size = 25,
  throttle_delay = 1.0,  # 增加延迟以避免API限制
  cache_api_results = TRUE,
  
  # 并行处理配置
  use_parallel = TRUE,
  parallel_strategy = "multisession",
  
  # 标准化配置
  standardize_authors = TRUE,
  standardize_institutions = TRUE,
  standardize_keywords = TRUE,
  standardize_journals = TRUE
)

# 创建必要的目录
for (dir_path in c(config$logs_dir, config$reports_dir)) {
  if (!dir.exists(dir_path)) {
    dir.create(dir_path, recursive = TRUE)
  }
}

# --- API请求核心函数 (增强版) ---
safe_api_request <- function(url, doi = NULL, api_type = "openalex") {
  # 只在调试模式下显示URL
  if (FALSE) {  # 设置为TRUE可以启用详细调试
    cat("请求API:", url, "\n")
  }
  
  # 如果提供了DOI，尝试从备选API获取（如果主API失败）
  alt_url <- NULL
  if (config$api_timeout_settings$use_alt_api && !is.null(doi)) {
    if (api_type == "openalex") {
      # 备选方案1: 使用S2 API作为OpenAlex的备选
      alt_url <- paste0("https://api.semanticscholar.org/v1/paper/", URLencode(doi, reserved = TRUE))
    } else if (api_type == "crossref") {
      # 备选方案2: 使用DataCite API作为CrossRef的备选
      alt_url <- paste0("https://api.datacite.org/works/", URLencode(doi, reserved = TRUE))
    }
  }
  
  # 尝试主API请求
  for (attempt in 1:config$api_timeout_settings$retries) {
    tryCatch({
      # 创建带有更多超时设置的handle
      handle <- curl::new_handle()
      curl::handle_setopt(handle, 
                         timeout = config$api_timeout_settings$timeout,
                         connecttimeout = config$api_timeout_settings$connecttimeout,
                         low_speed_limit = 10,
                         low_speed_time = 60,
                         httpheader = c("User-Agent" = config$api_timeout_settings$user_agent),
                         ssl_verifypeer = FALSE,  # 禁用SSL验证，可能解决一些SSL问题
                         verbose = FALSE)         # 关闭详细输出
      
      # 执行请求
      response <- curl::curl_fetch_memory(url, handle = handle)
      
      if (response$status_code >= 200 && response$status_code < 300) {
        return(list(success = TRUE, data = rawToChar(response$content), source = "primary"))
      } else {
        if (attempt < config$api_timeout_settings$retries) {
          Sys.sleep(config$api_timeout_settings$retry_delay)
        }
      }
    }, error = function(e) {
      if (attempt < config$api_timeout_settings$retries) {
        wait_time <- config$api_timeout_settings$retry_delay * attempt  # 渐进式增加等待时间
        Sys.sleep(wait_time)
      }
    })
  }
  
  # 如果主API全部失败且存在备选API，则尝试备选API
  if (!is.null(alt_url)) {
    for (attempt in 1:2) { # 备选API少尝试几次
      tryCatch({
        # 备选API的handle
        handle <- curl::new_handle()
        curl::handle_setopt(handle, 
                           timeout = config$api_timeout_settings$timeout,
                           connecttimeout = config$api_timeout_settings$connecttimeout,
                           httpheader = c("User-Agent" = config$api_timeout_settings$user_agent),
                           ssl_verifypeer = FALSE)
        
        # 执行备选请求
        response <- curl::curl_fetch_memory(alt_url, handle = handle)
        
        if (response$status_code >= 200 && response$status_code < 300) {
          return(list(success = TRUE, data = rawToChar(response$content), source = "alternative"))
        } else {
          Sys.sleep(5) # 简单等待
        }
      }, error = function(e) {
        Sys.sleep(5)
      })
    }
  }
  
  # 所有尝试都失败
  return(list(success = FALSE, data = NA_character_, source = "none"))
}

# --- 数据库连接管理函数 ---
initialize_db_connection <- function(db_file_path) {
  tryCatch({
    if (!file.exists(db_file_path)) {
      dir.create(dirname(db_file_path), showWarnings = FALSE, recursive = TRUE)
      con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
      DBI::dbExecute(con, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
      return(con)
    } else {
      con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
      # 验证连接是否有效
      tryCatch({
        DBI::dbExecute(con, "SELECT 1")
      }, error = function(e) {
        # 如果连接无效，尝试修复数据库
        message("数据库连接验证失败，尝试修复数据库...")
        DBI::dbDisconnect(con, force = TRUE)
        # 创建备份
        backup_file <- paste0(db_file_path, ".backup_", format(Sys.time(), "%Y%m%d%H%M%S"))
        file.copy(db_file_path, backup_file)
        # 重新连接
        con <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
        DBI::dbExecute(con, "PRAGMA integrity_check")
        DBI::dbExecute(con, "VACUUM")
      })
      return(con)
    }
  }, error = function(e) {
    message(paste("数据库连接初始化失败:", e$message))
    # 如果创建连接失败，尝试使用临时文件
    temp_db <- tempfile(fileext = ".sqlite")
    message(paste("尝试使用临时数据库:", temp_db))
    con <- DBI::dbConnect(RSQLite::SQLite(), temp_db)
    DBI::dbExecute(con, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
    return(con)
  })
}

safe_query_cache <- function(query, params = NULL, db_file_path) {
  max_retries <- 3
  retry_count <- 0
  con_local <- NULL # 定义局部连接变量
  
  while (retry_count < max_retries) {
    tryCatch({
      # 在每次尝试中确保数据库连接有效，或者重新建立
      if (is.null(con_local) || !DBI::dbIsValid(con_local)) {
        con_local <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
        # 确保表存在
        DBI::dbExecute(con_local, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
      }

      # 执行查询
      if (is.null(params)) {
        result <- DBI::dbGetQuery(con_local, query)
      } else {
        stmt <- DBI::dbSendQuery(con_local, query)
        DBI::dbBind(stmt, params)
        result <- DBI::dbFetch(stmt)
        DBI::dbClearResult(stmt)
      }
      
      return(list(success = TRUE, result = result))
    }, error = function(e) {
      retry_count <<- retry_count + 1
      message(paste("数据库查询失败 (尝试", retry_count, "/", max_retries, "):", e$message))
      Sys.sleep(1)  # 短暂延迟后重试
      
      # 如果连接无效或出现错误，尝试强制关闭并重置以便下次循环重新建立
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local, force = TRUE) }, error = function(e) {})
      }
      con_local <<- NULL # 强制重新初始化连接
    }, finally = {
      # 确保在函数退出时关闭连接
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local) }, error = function(e) {})
      }
    })
  }
  
  # 所有重试都失败
  message("数据库操作失败，达到最大重试次数")
  return(list(success = FALSE, result = data.frame()))
}

safe_write_cache <- function(doi, api, response, db_file_path) {
  max_retries <- 3
  retry_count <- 0
  con_local <- NULL # 定义局部连接变量
  
  while (retry_count < max_retries) {
    tryCatch({
      # 在每次尝试中确保数据库连接有效，或者重新建立
      if (is.null(con_local) || !DBI::dbIsValid(con_local)) {
        con_local <- DBI::dbConnect(RSQLite::SQLite(), db_file_path)
        # 确保表存在
        DBI::dbExecute(con_local, "CREATE TABLE IF NOT EXISTS api_cache (doi TEXT PRIMARY KEY, api TEXT, response TEXT, timestamp TEXT)")
      }
      
      # 准备写入
      query <- "INSERT OR REPLACE INTO api_cache (doi, api, response, timestamp) VALUES (?, ?, ?, ?)"
      timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
      
      # 执行
      stmt <- DBI::dbSendQuery(con_local, query)
      DBI::dbBind(stmt, list(doi, api, response, timestamp))
      DBI::dbClearResult(stmt)
      
      return(list(success = TRUE))
    }, error = function(e) {
      retry_count <<- retry_count + 1
      message(paste("数据库写入失败 (尝试", retry_count, "/", max_retries, "):", e$message))
      Sys.sleep(1)  # 短暂延迟后重试
      
      # 如果连接无效或出现错误，尝试强制关闭并重置以便下次循环重新建立
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local, force = TRUE) }, error = function(e) {})
      }
      con_local <<- NULL # 强制重新初始化连接
    }, finally = {
      # 确保在函数退出时关闭连接
      if (!is.null(con_local) && DBI::dbIsValid(con_local)) {
        tryCatch({ DBI::dbDisconnect(con_local) }, error = function(e) {})
      }
    })
  }
  
  # 所有重试都失败
  message("数据库写入失败，达到最大重试次数")
  return(list(success = FALSE))
}

# --- 数据库连接清理函数 ---
cleanup_db_connection <- function() {
  tryCatch({
    if (exists("con") && DBI::dbIsValid(con)) {
      message("正在安全关闭数据库连接...")
      suppressWarnings({
        DBI::dbDisconnect(con)
      })
    } else {
      message("数据库连接不存在或已经关闭。")
    }
  }, error = function(e) {
    message(paste("关闭数据库连接时发生错误:", e$message))
    # 尝试强制关闭
    if (exists("con") && !is.null(con)) {
      suppressWarnings({
        try(DBI::dbDisconnect(con, force = TRUE), silent = TRUE)
      })
    }
  })
  
  # 确保连接变量被清理
  if (exists("con")) {
    con <<- NULL # 使用 <<- 修改全局变量
  }
}

# 在脚本结束时执行清理
on.exit(cleanup_db_connection(), add = TRUE)

# --- 基于bibliometrix源代码的字段标准化函数 ---

# 作者信息标准化（基于bibliometrix::convert2df中的实现）
standardize_authors_bibliometrix <- function(authors_string) {
  if (is.na(authors_string) || authors_string == "") {
    return(NA_character_)
  }
  
  # 分割作者
  authors <- strsplit(authors_string, ";")[[1]]
  
  # 标准化每个作者（基于bibliometrix的实现）
  standardized_authors <- sapply(authors, function(author) {
    # 移除非字母数字字符（保留连字符和撇号）
    author <- gsub("[^[:alnum:][-]']", " ", author)
    # 去除首尾空格
    author <- trimws(author)
    return(author)
  })
  
  return(paste(standardized_authors, collapse = ";"))
}

# 机构信息标准化（基于bibliometrix::metaTagExtraction中的AU_UN实现）
standardize_institutions_bibliometrix <- function(institutions_string) {
  if (is.na(institutions_string) || institutions_string == "") {
    return(NA_character_)
  }
  
  # 移除方括号标记
  institutions <- gsub("\\[.*?\\] ", "", institutions_string)
  
  # 分割机构
  institution_list <- strsplit(institutions, ";")[[1]]
  
  # 机构类型标签（基于bibliometrix的uTags）
  uTags <- c("UNIV", "COLL", "SCH", "INST", "ACAD", "ECOLE", "CTR", "SCI", 
             "CENTRE", "CENTER", "CENTRO", "HOSP", "ASSOC", "COUNCIL",
             "FONDAZ", "FOUNDAT", "ISTIT", "LAB", "TECH", "RES", "CNR", 
             "ARCH", "SCUOLA", "PATENT OFF", "CENT LIB", "HEALTH", "NATL",
             "LIBRAR", "CLIN", "FDN", "OECD", "FAC", "WORLD BANK", 
             "POLITECN", "INT MONETARY FUND", "CLIMA", "METEOR", "OFFICE", 
             "ENVIR", "CONSORTIUM", "OBSERVAT", "AGRI", "MIT ", "INFN", "SUNY ")
  
  # 标准化每个机构
  standardized_institutions <- sapply(institution_list, function(institution) {
    # 移除重印作者标记
    institution <- gsub("\\(REPRINT AUTHOR\\)", "", institution)
    
    # 分割地址组件
    address_components <- strsplit(institution, ",")[[1]]
    
    # 查找机构类型标签
    tag_indices <- sapply(uTags, function(tag) {
      which(grepl(tag, address_components, fixed = TRUE))
    })
    tag_indices <- unlist(tag_indices)
    
    if (length(tag_indices) == 0) {
      return("NOTREPORTED")
    } else {
      # 检查是否包含数字（未声明的机构）
      if (grepl("[[:digit:]]", address_components[tag_indices[1]])) {
        return("NOTDECLARED")
      } else {
        return(trimws(address_components[tag_indices[1]]))
      }
    }
  })
  
  # 过滤掉NOTREPORTED和NOTDECLARED
  valid_institutions <- standardized_institutions[!standardized_institutions %in% c("NOTREPORTED", "NOTDECLARED")]
  
  if (length(valid_institutions) == 0) {
    return(NA_character_)
  } else {
    return(paste(valid_institutions, collapse = ";"))
  }
}

# 关键词标准化（基于bibliometrix::termExtraction的实现）
standardize_keywords_bibliometrix <- function(keywords_string) {
  if (is.na(keywords_string) || keywords_string == "") {
    return(NA_character_)
  }
  
  # 分割关键词
  keywords <- strsplit(keywords_string, ";")[[1]]
  
  # 标准化每个关键词（基于bibliometrix的处理方式）
  standardized_keywords <- sapply(keywords, function(keyword) {
    # 去除首尾空格
    keyword <- trimws(keyword)
    
    # 将连字符替换为双下划线（保持多词关键词）
    keyword <- gsub("-", "__", keyword)
    
    # 将空格替换为下划线（保持多词关键词）
    keyword <- gsub(" ", "_", keyword)
    
    # 转换为小写
    keyword <- tolower(keyword)
    
    return(keyword)
  })
  
  return(paste(standardized_keywords, collapse = ";"))
}

# 期刊信息标准化（基于bibliometrix的处理方式）
standardize_journals_bibliometrix <- function(journal_string) {
  if (is.na(journal_string) || journal_string == "") {
    return(NA_character_)
  }
  
  # 去除首尾空格
  journal <- trimws(journal_string)
  
  # 移除多余的空格
  journal <- gsub("\\s+", " ", journal)
  
  # 处理常见的期刊缩写（基于bibliometrix的处理）
  journal <- gsub("\\bJ\\.\\s*", "Journal of ", journal)
  journal <- gsub("\\bInt\\.\\s*", "International ", journal)
  journal <- gsub("\\bRes\\.\\s*", "Research ", journal)
  
  return(journal)
}

# 参考文献清理（基于bibliometrix::convert2df的实现）
clean_references_bibliometrix <- function(references_string) {
  if (is.na(references_string) || references_string == "") {
    return("none")
  }
  
  # 基于bibliometrix的CR字段清理
  cleaned_refs <- gsub("\\[,||\\[||\\]|| \\.\\. || \\. ", "", references_string)
  
  return(cleaned_refs)
}

# --- API补全函数 ---

# 改进的字符串相似度计算
improved_string_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  
  # 转换为小写并移除标点符号
  str1_clean <- tolower(gsub("[[:punct:]]", " ", str1))
  str2_clean <- tolower(gsub("[[:punct:]]", " ", str2))
  
  # 移除多余空格
  str1_clean <- gsub("\\s+", " ", trimws(str1_clean))
  str2_clean <- gsub("\\s+", " ", trimws(str2_clean))
  
  # 方法1：编辑距离相似度
  distance <- adist(str1_clean, str2_clean)[1, 1]
  max_len <- max(nchar(str1_clean), nchar(str2_clean))
  edit_similarity <- if (max_len == 0) 1 else 1 - (distance / max_len)
  
  # 方法2：词汇重叠相似度
  words1 <- strsplit(str1_clean, "\\s+")[[1]]
  words2 <- strsplit(str2_clean, "\\s+")[[1]]
  
  # 移除停用词
  stopwords <- c("a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  words1 <- words1[!words1 %in% stopwords & nchar(words1) > 2]
  words2 <- words2[!words2 %in% stopwords & nchar(words2) > 2]
  
  if (length(words1) == 0 || length(words2) == 0) {
    word_similarity <- 0
  } else {
    common_words <- length(intersect(words1, words2))
    total_words <- length(union(words1, words2))
    word_similarity <- common_words / total_words
  }
  
  # 综合相似度：编辑距离权重0.3，词汇重叠权重0.7
  combined_similarity <- 0.3 * edit_similarity + 0.7 * word_similarity
  
  return(combined_similarity)
}

# 改进的DOI查找函数
search_doi_by_title_improved <- function(title, authors = NULL, year = NULL) {
  tryCatch({
    # 清理标题
    clean_title <- gsub("[[:punct:]]", " ", title)
    clean_title <- gsub("\\s+", " ", clean_title)
    clean_title <- trimws(clean_title)
    
    # 处理作者信息
    rcr_query_author <- NULL
    if (!is.null(authors) && !is.na(authors) && authors != "") {
      # 分割作者列表
      author_list <- strsplit(authors, ";")[[1]]
      if (length(author_list) > 0) {
        first_author <- trimws(author_list[1])
        
        # 尝试不同的作者格式解析
        if (grepl(",", first_author)) {
          # 格式：Last, First Middle
          author_parts <- strsplit(first_author, ",")[[1]]
          if (length(author_parts) >= 2) {
            last_name <- trimws(author_parts[1])
            first_name <- trimws(author_parts[2])
            # 提取名字的首字母
            first_initial <- substr(first_name, 1, 1)
            rcr_query_author <- paste(first_initial, last_name)
          }
        } else {
          # 格式：First Middle Last 或 First Last
          author_parts <- strsplit(first_author, "\\s+")[[1]]
          if (length(author_parts) >= 2) {
            # 假设最后一个是姓氏，第一个是名字
            first_name <- author_parts[1]
            last_name <- author_parts[length(author_parts)]
            # 如果名字长度大于1，取首字母
            if (nchar(first_name) > 1) {
              first_initial <- substr(first_name, 1, 1)
              rcr_query_author <- paste(first_initial, last_name)
            } else {
              rcr_query_author <- paste(first_name, last_name)
            }
          } else if (length(author_parts) == 1 && nchar(author_parts[1]) > 3) {
            rcr_query_author <- author_parts[1]
          }
        }
      }
    }
    
    # 年份过滤
    rcr_filter <- NULL
    if (!is.null(year) && !is.na(year)) {
      # 扩大年份范围以增加匹配可能性
      year_start <- max(1900, as.numeric(year) - 2)
      year_end <- min(as.numeric(format(Sys.Date(), "%Y")), as.numeric(year) + 2)
      rcr_filter <- c(from_pub_date = as.character(year_start), until_pub_date = as.character(year_end))
    }
    
    log_message(sprintf("查询: '%s'", substr(clean_title, 1, 50)))
    
    # 尝试不同的查询策略
    crossref_result <- NULL
    
    # 策略1：使用关键词查询（更宽泛）
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    # 选择重要的词汇（长度大于3的词）
    important_words <- title_words[nchar(title_words) > 3]
    if (length(important_words) > 5) {
      important_words <- important_words[1:5]  # 只取前5个重要词
    }
    
    if (length(important_words) > 0) {
      query_string <- paste(important_words, collapse = " ")
      log_message(sprintf("策略1 - 关键词查询: '%s'", query_string))
      
      crossref_result <- rcrossref::cr_works(
        query = query_string,
        filter = rcr_filter,
        limit = 10
      )
    }
    
    # 策略2：如果策略1没有结果，尝试标题查询
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("策略2 - 标题查询")
      crossref_result <- rcrossref::cr_works(
        query.title = clean_title,
        filter = rcr_filter,
        limit = 10
      )
    }
    
    # 策略3：如果前两个策略都失败，尝试更简单的查询
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("策略3 - 简化查询")
      # 只使用前3个重要词
      if (length(important_words) >= 3) {
        simple_query <- paste(important_words[1:3], collapse = " ")
      } else {
        simple_query <- paste(title_words[1:min(3, length(title_words))], collapse = " ")
      }
      
      crossref_result <- rcrossref::cr_works(
        query = simple_query,
        limit = 10
      )
    }
    
    # 分析结果
    if (!is.null(crossref_result$data) && nrow(crossref_result$data) > 0) {
      log_message(sprintf("找到 %d 个候选结果", nrow(crossref_result$data)))
      
      best_match_idx <- NULL
      best_similarity <- 0
      
      for (i in 1:min(5, nrow(crossref_result$data))) {  # 只检查前5个结果
        candidate_title <- crossref_result$data$title[i]
        if (is.null(candidate_title) || is.na(candidate_title) || candidate_title == "") {
          next
        }
        
        current_similarity <- improved_string_similarity(title, candidate_title)
        
        if (current_similarity > best_similarity) {
          best_similarity <- current_similarity
          best_match_idx <- i
        }
      }
      
      # 使用更宽松的阈值
      similarity_threshold <- 0.25
      
      if (!is.null(best_match_idx) && best_similarity > similarity_threshold) {
        doi_found <- crossref_result$data$doi[best_match_idx]
        if (!is.na(doi_found) && doi_found != "") {
          log_message(sprintf("成功找到DOI: %s (相似度: %.2f)", doi_found, best_similarity))
          return(doi_found)
        }
      } else {
        log_message(sprintf("最佳相似度 %.2f 低于阈值 %.2f", best_similarity, similarity_threshold))
      }
    } else {
      log_message("未找到任何候选结果")
    }
    
    return(NA_character_)
  }, error = function(e) {
    log_message(sprintf("查询过程发生错误: %s", e$message), "error")
    return(NA_character_)
  })
}

# 安全的Crossref API查询
safe_crossref_query <- function(doi, use_cache = config$cache_api_results) {
  # 确保API缓存目录存在（因为cache_api_results会用到）
  api_cache_dir <- dirname(config$api_cache_file)
  if (!dir.exists(api_cache_dir)) {
    dir.create(api_cache_dir, recursive = TRUE)
  }
  
  # 检查缓存
  if (use_cache) {
    query_result <- safe_query_cache(
      "SELECT response FROM api_cache WHERE doi = ? AND api = 'crossref'", 
      params = list(doi),
      config$api_cache_file
    )
    
    if (query_result$success && nrow(query_result$result) > 0 && !is.na(query_result$result$response[1])) {
      log_message(sprintf("使用Crossref API缓存结果: %s", doi), "info")
      return(list(success = TRUE, data = fromJSON(query_result$result$response[1])))
    }
  }
  
  # 编码DOI
  doi_encoded <- URLencode(doi, reserved = TRUE)
  url <- sprintf("https://api.crossref.org/works/%s", doi_encoded)
  
  # 使用新的 safe_api_request 函数
  api_result <- safe_api_request(url, doi, "crossref")
  
  # 处理结果
  if (api_result$success) {
    response_data <- api_result$data
    
    # 缓存结果
    if (use_cache) {
      write_result <- safe_write_cache(doi, "crossref", response_data, config$api_cache_file)
    }
    
    return(list(success = TRUE, data = fromJSON(response_data)))
  } else {
    log_message(sprintf("无法获取Crossref数据: %s", doi), "warning")
    return(list(success = FALSE, data = NULL))
  }
}

# 简化的OpenAlex API查询
safe_openalex_query <- function(doi, use_cache = config$cache_api_results) {
  # 确保API缓存目录存在（因为cache_api_results会用到）
  api_cache_dir <- dirname(config$api_cache_file)
  if (!dir.exists(api_cache_dir)) {
    dir.create(api_cache_dir, recursive = TRUE)
  }

  # 检查缓存
  if (use_cache) {
    query_result <- safe_query_cache(
      "SELECT response FROM api_cache WHERE doi = ? AND api = 'openalex'",
      params = list(doi),
      config$api_cache_file
    )

    if (query_result$success && nrow(query_result$result) > 0 && !is.na(query_result$result$response[1])) {
      log_message(sprintf("使用OpenAlex API缓存结果: %s", doi), "info")
      return(list(success = TRUE, data = fromJSON(query_result$result$response[1])))
    }
  }

  # 构建API URL
  doi_encoded <- URLencode(doi, reserved = TRUE)
  url <- sprintf("https://api.openalex.org/works/doi:%s", doi_encoded)

  # 使用新的 safe_api_request 函数
  api_result <- safe_api_request(url, doi, "openalex")

  # 处理结果
  if (api_result$success) {
    response_data <- api_result$data

    # 缓存结果
    if (use_cache) {
      write_result <- safe_write_cache(doi, "openalex", response_data, config$api_cache_file)
    }

    return(list(success = TRUE, data = fromJSON(response_data)))
  } else {
    log_message(sprintf("无法获取OpenAlex数据: %s", doi), "warning")
    return(list(success = FALSE, data = NULL))
  }
}

# --- 数据质量评估函数 ---

# 计算字段完整性
calculate_field_completeness <- function(data) {
  field_stats <- sapply(colnames(data), function(col) {
    sum(!is.na(data[[col]]) & data[[col]] != "") / nrow(data)
  })
  
  return(field_stats)
}

# 检测数据异常
detect_data_anomalies <- function(data) {
  anomalies <- list()
  
  # 检测年份异常
  if ("PY" %in% colnames(data)) {
    year_anomalies <- data %>%
      filter(!is.na(PY) & (PY < 1900 | PY > as.numeric(format(Sys.Date(), "%Y")) + 1)) %>%
      select(UT, TI, PY) %>%
      mutate(anomaly_type = "异常年份")
    
    if (nrow(year_anomalies) > 0) {
      anomalies$year_anomalies <- year_anomalies
    }
  }
  
  # 检测标题长度异常
  if ("TI" %in% colnames(data)) {
    title_anomalies <- data %>%
      filter(!is.na(TI) & (nchar(TI) < 10 | nchar(TI) > 500)) %>%
      select(UT, TI) %>%
      mutate(
        title_length = nchar(TI),
        anomaly_type = ifelse(nchar(TI) < 10, "标题过短", "标题过长")
      )
    
    if (nrow(title_anomalies) > 0) {
      anomalies$title_anomalies <- title_anomalies
    }
  }
  
  return(anomalies)
}

# --- 主函数 ---
main <- function() {
  log_message("开始数据增强流程")
  
  # 1. 加载数据
  if (!file.exists(config$input_file)) {
    stop(sprintf("输入文件不存在: %s", config$input_file))
  }
  
  M <- readRDS(config$input_file)
  log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
  
  # 2. 查找缺失DOI的记录
  missing_doi_records <- M[is.na(M$DI) | M$DI == "", ]
  log_message(sprintf("缺失DOI的记录数: %d", nrow(missing_doi_records)))
  
  # 限制处理前10条记录
  test_records <- head(missing_doi_records, 10)
  log_message(sprintf("将处理前10条记录进行测试"))
  
  if (nrow(test_records) > 0) {
    # 创建进度条
    pb <- progress_bar$new(
      format = "[:bar] :percent 已完成 (:current/:total)",
      total = nrow(test_records),
      clear = FALSE,
      width = 60
    )
    
    doi_found_count <- 0
    
    # 创建结果数据框
    results_df <- data.frame(
      UT = character(),
      Title = character(),
      Authors = character(),
      Year = integer(),
      Original_DOI = character(),
      Found_DOI = character(),
      Similarity = numeric(),
      stringsAsFactors = FALSE
    )
    
    for (i in 1:nrow(test_records)) {
      record <- test_records[i, ]
      pb$tick()
      
      log_message(sprintf("\n=== 处理记录 %d/%d ===", i, nrow(test_records)))
      log_message(sprintf("标题: %s", record$TI))
      log_message(sprintf("作者: %s", record$AU))
      log_message(sprintf("年份: %s", record$PY))
      
      found_doi <- NA_character_
      similarity_score <- 0
      
      if (!is.na(record$TI) && record$TI != "") {
        # 调用改进的DOI查找函数
        found_doi <- search_doi_by_title_improved(record$TI, record$AU, record$PY)
        
        # 如果找到DOI，获取相似度分数
        if (!is.na(found_doi) && found_doi != "") {
          # 使用rcrossref获取完整记录
          cr_result <- rcrossref::cr_works(dois = found_doi)
          if (!is.null(cr_result$data) && nrow(cr_result$data) > 0) {
            candidate_title <- cr_result$data$title[1]
            similarity_score <- improved_string_similarity(record$TI, candidate_title)
            
            log_message(sprintf("找到匹配文献:"))
            log_message(sprintf("- 标题: %s", candidate_title))
            log_message(sprintf("- DOI: %s", found_doi))
            log_message(sprintf("- 相似度: %.2f", similarity_score))
          }
        }
      }
      
      # 更新原始数据
      if (!is.na(found_doi) && found_doi != "") {
        original_row_index <- which(M$UT == record$UT)
        if (length(original_row_index) > 0) {
          M$DI[original_row_index] <- found_doi
          doi_found_count <- doi_found_count + 1
          log_message(sprintf("成功为记录 %s 补全DOI", record$UT))
        }
      }
      
      # 添加到结果数据框
      results_df <- rbind(results_df, data.frame(
        UT = record$UT,
        Title = record$TI,
        Authors = record$AU,
        Year = record$PY,
        Original_DOI = record$DI,
        Found_DOI = found_doi,
        Similarity = similarity_score,
        stringsAsFactors = FALSE
      ))
      
      # 防止API限制
      Sys.sleep(config$throttle_delay)
    }
    
    # 显示处理结果摘要
    log_message("\n=== 处理结果摘要 ===")
    log_message(sprintf("总处理记录数: %d", nrow(test_records)))
    log_message(sprintf("成功找到DOI数: %d", doi_found_count))
    log_message(sprintf("成功率: %.1f%%", 100 * doi_found_count / nrow(test_records)))
    
    # 显示详细结果
    log_message("\n=== 详细处理结果 ===")
    for (i in 1:nrow(results_df)) {
      log_message(sprintf("\n记录 %d:", i))
      log_message("----------------------------------------")
      log_message("原始文献信息:")
      log_message(sprintf("UT: %s", results_df$UT[i]))
      log_message(sprintf("标题: %s", results_df$Title[i]))
      log_message(sprintf("作者: %s", results_df$Authors[i]))
      log_message(sprintf("年份: %s", results_df$Year[i]))
      log_message(sprintf("DOI: %s", ifelse(is.na(results_df$Original_DOI[i]), "缺失", results_df$Original_DOI[i])))
      
      # 获取匹配文献的详细信息
      if (!is.na(results_df$Found_DOI[i])) {
        cr_result <- rcrossref::cr_works(dois = results_df$Found_DOI[i])
        if (!is.null(cr_result$data) && nrow(cr_result$data) > 0) {
          log_message("\n匹配文献信息:")
          log_message(sprintf("标题: %s", cr_result$data$title[1]))
          log_message(sprintf("作者: %s", paste(cr_result$data$author[[1]]$given, cr_result$data$author[[1]]$family, collapse = "; ")))
          log_message(sprintf("年份: %s", cr_result$data$published.print[1]))
          log_message(sprintf("期刊: %s", cr_result$data$container.title[1]))
          log_message(sprintf("DOI: %s", results_df$Found_DOI[i]))
          log_message(sprintf("相似度: %.2f", results_df$Similarity[i]))
          
          # 添加相似度评估
          similarity_level <- if(results_df$Similarity[i] >= 0.9) {
            "高 (建议直接采用)"
          } else if(results_df$Similarity[i] >= 0.7) {
            "中 (建议复核)"
          } else if(results_df$Similarity[i] >= 0.5) {
            "低 (需要人工确认)"
          } else {
            "极低 (不建议采用)"
          }
          log_message(sprintf("匹配质量: %s", similarity_level))
        }
      }
      log_message("----------------------------------------")
    }
    
    # 保存结果到Excel文件
    # 创建工作簿
    wb <- createWorkbook()
    
    # 创建详细结果工作表
    addWorksheet(wb, "DOI补全结果")
    
    # 准备对比数据
    comparison_data <- data.frame(
      类型 = character(),
      UT = character(),
      标题 = character(),
      作者 = character(),
      年份 = character(),
      期刊 = character(),
      DOI = character(),
      相似度 = character(),
      匹配质量 = character(),
      stringsAsFactors = FALSE
    )
    
    # 为每条记录添加原始信息和匹配信息
    for (i in 1:nrow(results_df)) {
      # 添加原始信息行
      comparison_data <- rbind(comparison_data, data.frame(
        类型 = "原始文献",
        UT = results_df$UT[i],
        标题 = results_df$Title[i],
        作者 = results_df$Authors[i],
        年份 = as.character(results_df$Year[i]),
        期刊 = "",
        DOI = ifelse(is.na(results_df$Original_DOI[i]), "缺失", results_df$Original_DOI[i]),
        相似度 = "",
        匹配质量 = "",
        stringsAsFactors = FALSE
      ))
      
      # 添加匹配信息行
      if (!is.na(results_df$Found_DOI[i])) {
        cr_result <- rcrossref::cr_works(dois = results_df$Found_DOI[i])
        if (!is.null(cr_result$data) && nrow(cr_result$data) > 0) {
          # 获取匹配文献的详细信息
          matched_title <- cr_result$data$title[1]
          matched_authors <- paste(cr_result$data$author[[1]]$given, cr_result$data$author[[1]]$family, collapse = "; ")
          matched_year <- ifelse(!is.null(cr_result$data$published.print[1]), 
                               cr_result$data$published.print[1], 
                               ifelse(!is.null(cr_result$data$published.online[1]), 
                                     cr_result$data$published.online[1], ""))
          matched_journal <- ifelse(!is.null(cr_result$data$container.title[1]), 
                                  cr_result$data$container.title[1], "")
          
          # 确定匹配质量
          similarity_level <- if(results_df$Similarity[i] >= 0.9) {
            "高 (建议直接采用)"
          } else if(results_df$Similarity[i] >= 0.7) {
            "中 (建议复核)"
          } else if(results_df$Similarity[i] >= 0.5) {
            "低 (需要人工确认)"
          } else {
            "极低 (不建议采用)"
          }
          
          comparison_data <- rbind(comparison_data, data.frame(
            类型 = "匹配文献",
            UT = "",
            标题 = matched_title,
            作者 = matched_authors,
            年份 = matched_year,
            期刊 = matched_journal,
            DOI = results_df$Found_DOI[i],
            相似度 = sprintf("%.2f", results_df$Similarity[i]),
            匹配质量 = similarity_level,
            stringsAsFactors = FALSE
          ))
        }
      } else {
        # 如果没有找到匹配，添加空行
        comparison_data <- rbind(comparison_data, data.frame(
          类型 = "匹配文献",
          UT = "",
          标题 = "",
          作者 = "",
          年份 = "",
          期刊 = "",
          DOI = "",
          相似度 = "",
          匹配质量 = "",
          stringsAsFactors = FALSE
        ))
      }
    }
    
    # 写入数据
    writeData(wb, "DOI补全结果", comparison_data)
    
    # 设置列宽
    setColWidths(wb, "DOI补全结果", cols = 1:ncol(comparison_data), widths = "auto")
    
    # 添加样式
    # 表头样式
    style_header <- createStyle(
      fontSize = 11,
      fontColour = "#FFFFFF",
      fgFill = "#4F81BD",
      halign = "center",
      valign = "center",
      textDecoration = "bold"
    )
    
    # 原始文献行样式
    style_original <- createStyle(
      fontSize = 10,
      fgFill = "#E6E6E6",
      halign = "left",
      valign = "center",
      wrapText = TRUE
    )
    
    # 匹配文献行样式
    style_matched <- createStyle(
      fontSize = 10,
      fgFill = "#F2F2F2",
      halign = "left",
      valign = "center",
      wrapText = TRUE
    )
    
    # 应用表头样式
    addStyle(wb, "DOI补全结果", style_header, rows = 1, cols = 1:ncol(comparison_data), gridExpand = TRUE)
    
    # 应用数据样式
    for (i in 1:nrow(comparison_data)) {
      if (comparison_data$类型[i] == "原始文献") {
        addStyle(wb, "DOI补全结果", style_original, rows = i + 1, cols = 1:ncol(comparison_data), gridExpand = TRUE)
      } else {
        addStyle(wb, "DOI补全结果", style_matched, rows = i + 1, cols = 1:ncol(comparison_data), gridExpand = TRUE)
      }
    }
    
    # 保存Excel文件
    excel_file <- file.path(config$reports_dir, "doi_completion_results.xlsx")
    saveWorkbook(wb, excel_file, overwrite = TRUE)
    log_message(sprintf("\n详细结果已保存至: %s", excel_file))
    
    # 同时保存CSV作为备份
    csv_file <- file.path(config$reports_dir, "doi_completion_results.csv")
    write.csv(comparison_data, csv_file, row.names = FALSE, fileEncoding = "UTF-8")
    log_message(sprintf("CSV备份已保存至: %s", csv_file))
  } else {
    log_message("没有缺失DOI的记录需要补全")
  }
  
  # 3. 保存结果
  saveRDS(M, config$output_file)
  log_message(sprintf("增强数据已保存: %s", config$output_file))
  
  log_message("数据增强流程完成")
  
  return(M)
}

# 运行主函数
if (interactive()) {
  enhanced_data <- main()
} else {
  enhanced_data <- main()
} 
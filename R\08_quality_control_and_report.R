# 加载必要的包
library(tidyverse)
library(here)
library(bibliometrix)

# 设置日志函数
log_message <- function(message, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[%s] [%s] %s\n", timestamp, toupper(type), message))
}

# 主函数
analyze_missing_doi <- function(input_file) {
  log_message("开始分析缺失DOI的文献记录特征")
  
  # 1. 加载数据
  log_message("步骤1: 加载数据")
  if (file.exists(input_file)) {
    M <- readRDS(input_file)
    log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
  } else {
    stop(sprintf("输入文件不存在: %s", input_file))
  }
  
  # 2. 识别缺失DOI的记录
  missing_doi_records <- M[is.na(M$DI) | M$DI == "", ]
  log_message(sprintf("缺失DOI的记录数: %d (%.2f%%)", 
                     nrow(missing_doi_records),
                     100 * nrow(missing_doi_records) / nrow(M)))
  
  # 3. 保存缺失DOI的记录为RData文件
  output_dir <- here("data_repository", "04_enhancement_reports")
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  save(missing_doi_records, file = file.path(output_dir, "missing_doi_records.RData"))
  log_message(sprintf("已保存缺失DOI的记录到: %s", 
                     file.path(output_dir, "missing_doi_records.RData")))
  
  # 4. 分析字段缺失情况
  log_message("\n分析字段缺失情况:")
  field_missing_stats <- data.frame(
    field = names(missing_doi_records),
    missing_count = sapply(missing_doi_records, function(x) sum(is.na(x) | x == "")),
    total_count = nrow(missing_doi_records),
    stringsAsFactors = FALSE
  )
  
  field_missing_stats$missing_percentage <- round(100 * field_missing_stats$missing_count / field_missing_stats$total_count, 2)
  field_missing_stats$complete_count <- field_missing_stats$total_count - field_missing_stats$missing_count
  field_missing_stats$complete_percentage <- round(100 - field_missing_stats$missing_percentage, 2)
  
  # 按缺失百分比降序排序
  field_missing_stats <- field_missing_stats[order(field_missing_stats$missing_percentage, decreasing = TRUE), ]
  
  # 5. 分析核心字段的完整性
  core_fields <- c("TI", "AU", "PY", "SO", "UT", "VL", "IS", "BP", "EP", "SN", "EI")
  field_completeness <- sapply(core_fields, function(field) {
    if (field %in% names(missing_doi_records)) {
      complete <- sum(!is.na(missing_doi_records[[field]]) & missing_doi_records[[field]] != "")
      return(list(
        field = field,
        complete = complete,
        total = nrow(missing_doi_records),
        percentage = round(100 * complete / nrow(missing_doi_records), 2)
      ))
    } else {
      return(list(
        field = field,
        complete = 0,
        total = nrow(missing_doi_records),
        percentage = 0
      ))
    }
  })
  
  # 6. 分析年份分布
  year_distribution <- missing_doi_records %>%
    filter(!is.na(PY)) %>%
    group_by(PY) %>%
    summarise(count = n(), .groups = "drop") %>%
    arrange(PY)
  
  # 7. 分析期刊分布
  journal_distribution <- missing_doi_records %>%
    filter(!is.na(SO)) %>%
    group_by(SO) %>%
    summarise(count = n(), .groups = "drop") %>%
    arrange(desc(count))
  
  # 8. 分析作者数量分布
  author_count_distribution <- missing_doi_records %>%
    filter(!is.na(AU)) %>%
    mutate(author_count = str_count(AU, ";") + 1) %>%
    group_by(author_count) %>%
    summarise(count = n(), .groups = "drop") %>%
    arrange(author_count)
  
  # 9. 分析标题长度分布
  title_length_distribution <- missing_doi_records %>%
    filter(!is.na(TI)) %>%
    mutate(title_length = nchar(TI)) %>%
    group_by(title_length_group = cut(title_length, 
                                    breaks = c(0, 50, 100, 150, 200, Inf),
                                    labels = c("1-50", "51-100", "101-150", "151-200", ">200"))) %>%
    summarise(count = n(), .groups = "drop")
  
  # 10. 分析文献类型分布
  document_type_distribution <- missing_doi_records %>%
    filter(!is.na(DT)) %>%
    group_by(DT) %>%
    summarise(count = n(), .groups = "drop") %>%
    arrange(desc(count))
  
  # 11. 分析语言分布
  language_distribution <- missing_doi_records %>%
    filter(!is.na(LA)) %>%
    group_by(LA) %>%
    summarise(count = n(), .groups = "drop") %>%
    arrange(desc(count))
  
  # 12. 分析学科分类分布
  subject_category_distribution <- missing_doi_records %>%
    filter(!is.na(SC)) %>%
    group_by(SC) %>%
    summarise(count = n(), .groups = "drop") %>%
    arrange(desc(count))
  
  # 13. 分析引用情况
  citation_distribution <- missing_doi_records %>%
    filter(!is.na(TC)) %>%
    mutate(citation_group = cut(as.numeric(TC), 
                              breaks = c(-Inf, 0, 5, 10, 20, 50, Inf),
                              labels = c("0", "1-5", "6-10", "11-20", "21-50", ">50"))) %>%
    group_by(citation_group) %>%
    summarise(count = n(), .groups = "drop")
  
  # 14. 分析机构分布
  institution_distribution <- missing_doi_records %>%
    filter(!is.na(C1)) %>%
    group_by(C1) %>%
    summarise(count = n(), .groups = "drop") %>%
    arrange(desc(count))
  
  # 15. 生成分析报告
  report <- list(
    total_records = nrow(M),
    missing_doi_count = nrow(missing_doi_records),
    missing_doi_percentage = 100 * nrow(missing_doi_records) / nrow(M),
    field_missing_stats = field_missing_stats,
    field_completeness = field_completeness,
    year_distribution = year_distribution,
    journal_distribution = journal_distribution,
    author_count_distribution = author_count_distribution,
    title_length_distribution = title_length_distribution,
    document_type_distribution = document_type_distribution,
    language_distribution = language_distribution,
    subject_category_distribution = subject_category_distribution,
    citation_distribution = citation_distribution,
    institution_distribution = institution_distribution
  )
  
  # 16. 输出分析结果
  log_message("\n=== 缺失DOI记录分析报告 ===")
  log_message(sprintf("总记录数: %d", report$total_records))
  log_message(sprintf("缺失DOI记录数: %d (%.2f%%)", 
                     report$missing_doi_count,
                     report$missing_doi_percentage))
  
  log_message("\n字段缺失情况:")
  print.data.frame(field_missing_stats, row.names = FALSE)
  
  log_message("\n核心字段完整性:")
  for (field in names(field_completeness)) {
    stats <- field_completeness[[field]]
    log_message(sprintf("%s: %d/%d (%.2f%%)", 
                       field, stats$complete, stats$total, stats$percentage))
  }
  
  log_message("\n年份分布:")
  print.data.frame(year_distribution, row.names = FALSE)
  
  log_message("\n期刊分布:")
  print.data.frame(journal_distribution, row.names = FALSE)
  
  log_message("\n作者数量分布:")
  print.data.frame(author_count_distribution, row.names = FALSE)
  
  log_message("\n标题长度分布:")
  print.data.frame(title_length_distribution, row.names = FALSE)
  
  log_message("\n文献类型分布:")
  print.data.frame(document_type_distribution, row.names = FALSE)
  
  log_message("\n语言分布:")
  print.data.frame(language_distribution, row.names = FALSE)
  
  log_message("\n学科分类分布:")
  print.data.frame(subject_category_distribution, row.names = FALSE)
  
  log_message("\n引用分布:")
  print.data.frame(citation_distribution, row.names = FALSE)
  
  log_message("\n机构分布:")
  print.data.frame(institution_distribution, row.names = FALSE)
  
  # 17. 保存分析结果
  # 保存为RDS文件
  saveRDS(report, file.path(output_dir, "missing_doi_analysis.rds"))
  
  # 保存为CSV文件
  write.csv(missing_doi_records, 
            file.path(output_dir, "missing_doi_records.csv"),
            row.names = FALSE)
  
  # 保存为Excel文件，包含多个工作表
  library(openxlsx)
  wb <- createWorkbook()
  
  # 添加各个分布的工作表
  addWorksheet(wb, "字段缺失情况")
  writeData(wb, "字段缺失情况", field_missing_stats)
  
  addWorksheet(wb, "年份分布")
  writeData(wb, "年份分布", year_distribution)
  
  addWorksheet(wb, "期刊分布")
  writeData(wb, "期刊分布", journal_distribution)
  
  addWorksheet(wb, "作者数量分布")
  writeData(wb, "作者数量分布", author_count_distribution)
  
  addWorksheet(wb, "标题长度分布")
  writeData(wb, "标题长度分布", title_length_distribution)
  
  addWorksheet(wb, "文献类型分布")
  writeData(wb, "文献类型分布", document_type_distribution)
  
  addWorksheet(wb, "语言分布")
  writeData(wb, "语言分布", language_distribution)
  
  addWorksheet(wb, "学科分类分布")
  writeData(wb, "学科分类分布", subject_category_distribution)
  
  addWorksheet(wb, "引用分布")
  writeData(wb, "引用分布", citation_distribution)
  
  addWorksheet(wb, "机构分布")
  writeData(wb, "机构分布", institution_distribution)
  
  # 保存Excel文件
  saveWorkbook(wb, file.path(output_dir, "missing_doi_analysis.xlsx"), overwrite = TRUE)
  
  log_message(sprintf("\n分析结果已保存至: %s", output_dir))
  
  return(report)
}

# 执行分析
input_file <- here("data_repository", "02_enhanced_dataset", "enhanced_data_optimized.rds")
analysis_results <- analyze_missing_doi(input_file) 
# History files
.Rhistory
.Rapp.history

# Session Data files
.RData
.RDataTmp

# User-specific files
.Renviron

# RStudio files
.Rproj.user/

# Knitr and R Markdown cache files
*_cache/
/cache/
*.utf8.md
*.knit.md

# CRAN/Bioconductor package builds
/*.tar.gz
/*.Rcheck/

# LaTeX intermediate files
*.aux
*.log
*.out
*.toc
*.nav
*.snm
*.vrb

# Compiled R packages
*.so
*.dll

# Data files - consider adding specific large data files if not versioned
# data/raw/
# data/processed/
# data/output_files/

# Log files
logs/
*.log

# Python cache
__pycache__/
*.py[cod]
*$py.class

# Environment variables
.env
*.env
.env.*

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db 
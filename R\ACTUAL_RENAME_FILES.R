# 实际重命名文件脚本
# 真正按照数据处理顺序重新命名现有文件

cat("=== 实际重命名文件脚本 ===\n")
cat("真正按照数据处理顺序重新命名现有文件...\n\n")

# 定义重命名映射
rename_mapping <- list(
  # 现有文件 -> 新文件名
  "01_data_import_wos.R" = "01_import_wos_data.R",
  "02_data_import_citespace.R" = "02_import_citespace_data.R", 
  "03_data_import_vosviewer.R" = "03_import_vosviewer_data.R",
  "04_data_validation.R" = "04_validate_and_clean_data.R",
  "05_deduplication_enhanced.R" = "05_deduplicate_records.R",
  "06_doi_completion.R" = "08_complete_missing_dois.R",  # 重要：DOI补全移到数据增强阶段
  "07_data_enhancement.R" = "07_enhance_data_comprehensive.R",
  "08_data_integration.R" = "09_integrate_enhanced_data.R",
  "09_quality_control.R" = "10_quality_control_and_report.R"
)

# 新增文件（从其他目录复制）
additional_files <- list(
  "enhanced/02_deduplication_enhanced_advanced.R" = "06_deduplicate_advanced.R"
)

# 分析当前状态
analyze_current_files <- function() {
  cat("📊 分析当前文件状态...\n")
  
  cat("现有核心文件:\n")
  for (old_name in names(rename_mapping)) {
    if (file.exists(file.path("R", old_name))) {
      cat(sprintf("  ✅ %s\n", old_name))
    } else {
      cat(sprintf("  ❌ %s (不存在)\n", old_name))
    }
  }
  
  cat("\n计划重命名:\n")
  for (old_name in names(rename_mapping)) {
    new_name <- rename_mapping[[old_name]]
    cat(sprintf("  %s → %s\n", old_name, new_name))
  }
  
  cat("\n新增文件:\n")
  for (src in names(additional_files)) {
    dst <- additional_files[[src]]
    if (file.exists(file.path("R", src))) {
      cat(sprintf("  %s → %s\n", src, dst))
    } else {
      cat(sprintf("  ❌ %s (源文件不存在)\n", src))
    }
  }
}

# 创建备份
create_backup <- function() {
  backup_dir <- "R/BACKUP_ACTUAL_RENAME"
  if (!dir.exists(backup_dir)) {
    dir.create(backup_dir, recursive = TRUE)
  }
  
  cat("📦 创建备份...\n")
  
  # 备份要重命名的文件
  for (old_name in names(rename_mapping)) {
    src_path <- file.path("R", old_name)
    if (file.exists(src_path)) {
      dst_path <- file.path(backup_dir, old_name)
      file.copy(src_path, dst_path)
      cat(sprintf("  备份: %s\n", old_name))
    }
  }
  
  cat(sprintf("✅ 备份完成: %s\n", backup_dir))
  return(backup_dir)
}

# 执行重命名
perform_rename <- function() {
  cat("\n🔄 执行重命名...\n")
  
  # 1. 重命名现有文件
  for (old_name in names(rename_mapping)) {
    old_path <- file.path("R", old_name)
    new_name <- rename_mapping[[old_name]]
    new_path <- file.path("R", new_name)
    
    if (file.exists(old_path)) {
      file.rename(old_path, new_path)
      cat(sprintf("  ✅ %s → %s\n", old_name, new_name))
    } else {
      cat(sprintf("  ❌ %s (文件不存在)\n", old_name))
    }
  }
  
  # 2. 复制新增文件
  cat("\n📋 复制新增文件...\n")
  for (src in names(additional_files)) {
    src_path <- file.path("R", src)
    dst_name <- additional_files[[src]]
    dst_path <- file.path("R", dst_name)
    
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      cat(sprintf("  ✅ %s → %s\n", src, dst_name))
    } else {
      cat(sprintf("  ❌ %s (源文件不存在)\n", src))
    }
  }
}

# 清理模板文件
cleanup_template_files <- function() {
  cat("\n🧹 清理模板文件...\n")
  
  # 删除之前创建的模板文件
  template_files <- c(
    "01_import_wos_data.R",
    "02_import_citespace_data.R", 
    "03_import_vosviewer_data.R",
    "04_validate_and_clean_data.R",
    "05_deduplicate_records.R",
    "06_deduplicate_advanced.R",
    "07_enhance_data_comprehensive.R",
    "08_complete_missing_dois.R",
    "09_integrate_enhanced_data.R",
    "10_quality_control_and_report.R"
  )
  
  for (template_file in template_files) {
    template_path <- file.path("R", template_file)
    if (file.exists(template_path)) {
      # 检查是否是模板文件（包含TODO注释）
      content <- readLines(template_path)
      if (any(grepl("TODO:", content))) {
        file.remove(template_path)
        cat(sprintf("  🗑️  删除模板: %s\n", template_file))
      }
    }
  }
}

# 验证结果
verify_result <- function() {
  cat("\n🔍 验证重命名结果...\n")
  
  expected_files <- c(
    "01_import_wos_data.R",
    "02_import_citespace_data.R", 
    "03_import_vosviewer_data.R",
    "04_validate_and_clean_data.R",
    "05_deduplicate_records.R",
    "06_deduplicate_advanced.R",
    "07_enhance_data_comprehensive.R",
    "08_complete_missing_dois.R",
    "09_integrate_enhanced_data.R",
    "10_quality_control_and_report.R"
  )
  
  cat("期望的最终文件:\n")
  all_present <- TRUE
  
  for (file in expected_files) {
    if (file.exists(file.path("R", file))) {
      cat(sprintf("  ✅ %s\n", file))
    } else {
      cat(sprintf("  ❌ %s (缺失)\n", file))
      all_present <- FALSE
    }
  }
  
  if (all_present) {
    cat("\n🎉 重命名验证成功！所有文件都已正确重命名。\n")
  } else {
    cat("\n⚠️  重命名验证失败，存在缺失文件。\n")
  }
  
  return(all_present)
}

# 生成重命名报告
generate_rename_report <- function(success) {
  cat("\n📋 生成重命名报告...\n")
  
  report_content <- sprintf('# 实际文件重命名报告

## 重命名时间
%s

## 重命名目标
按照数据处理的实际逻辑顺序，重新命名现有R脚本文件。

## 重命名映射

### 核心文件重命名
```
01_data_import_wos.R           → 01_import_wos_data.R
02_data_import_citespace.R     → 02_import_citespace_data.R
03_data_import_vosviewer.R     → 03_import_vosviewer_data.R
04_data_validation.R           → 04_validate_and_clean_data.R
05_deduplication_enhanced.R    → 05_deduplicate_records.R
06_doi_completion.R            → 08_complete_missing_dois.R  ⭐ 重要变化
07_data_enhancement.R          → 07_enhance_data_comprehensive.R
08_data_integration.R          → 09_integrate_enhanced_data.R
09_quality_control.R           → 10_quality_control_and_report.R
```

### 新增文件
```
enhanced/02_deduplication_enhanced_advanced.R → 06_deduplicate_advanced.R
```

## 核心改进

### 1. DOI补全位置调整
- **从06调整到08** - 正确定位在数据增强阶段
- **逻辑合理** - DOI补全是数据增强的重要组成部分
- **流程优化** - 在去重后、整合前进行DOI补全

### 2. 处理流程优化
```
旧流程: 01→02→03→04→05→06(DOI)→07→08→09
新流程: 01→02→03→04→05→06(高级去重)→07→08(DOI)→09→10
```

### 3. 文件名优化
- 使用动词形式，更直观地表达功能
- 统一命名规范
- 提高可读性

## 最终处理流程

### 标准执行顺序
```
01_import_wos_data.R              # 数据导入
02_import_citespace_data.R        # 数据导入
03_import_vosviewer_data.R        # 数据导入
04_validate_and_clean_data.R      # 数据验证
05_deduplicate_records.R          # 去重处理
06_deduplicate_advanced.R         # 高级去重(可选)
07_enhance_data_comprehensive.R   # 数据增强
08_complete_missing_dois.R        # DOI补全 ⭐
09_integrate_enhanced_data.R      # 数据整合
10_quality_control_and_report.R   # 质量控制
```

### 依赖关系
```
数据导入 → 验证清理 → 去重处理 → 数据增强(含DOI补全) → 质量控制
```

## 重命名状态
%s

## 备份保护
- 所有原始文件已备份到 `BACKUP_ACTUAL_RENAME/`
- 可以随时恢复到重命名前的状态

## 使用指南

### 完整流程执行
```r
# 按新的顺序执行
scripts <- c("01", "02", "03", "04", "05", "07", "08", "09", "10")
for (num in scripts) {
  script_file <- list.files("R", pattern = paste0("^", num, "_"), full.names = TRUE)[1]
  if (!is.null(script_file) && file.exists(script_file)) {
    cat("执行:", basename(script_file), "\\n")
    source(script_file)
  }
}
```

### 可选高级去重
```r
# 在05后可选执行06
source("R/06_deduplicate_advanced.R")
```

重命名完成！文件现在按照数据处理的逻辑顺序组织。
', 
    format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    if (success) "✅ 重命名成功" else "❌ 重命名失败"
  )
  
  writeLines(report_content, "R/ACTUAL_RENAME_REPORT.md")
  cat("✅ 重命名报告已生成: R/ACTUAL_RENAME_REPORT.md\n")
}

# 主执行函数
main_actual_rename <- function() {
  cat("开始实际文件重命名...\n\n")
  
  analyze_current_files()
  
  cat("\n❓ 是否执行实际重命名？这将改变现有文件名。\n")
  cat("自动执行重命名...\n")
  
  backup_dir <- create_backup()
  cleanup_template_files()
  perform_rename()
  success <- verify_result()
  generate_rename_report(success)
  
  cat("\n=== 实际文件重命名完成 ===\n")
  cat("✅ 备份了所有原始文件\n")
  cat("✅ 清理了模板文件\n")
  cat("✅ 执行了实际重命名\n")
  cat("✅ 生成了重命名报告\n")
  
  if (success) {
    cat("\n🎉 文件重命名完全成功！\n")
    cat("📋 DOI补全现在正确位于数据增强阶段 (08)\n")
    cat("🔄 文件编号与处理顺序完全对应\n")
    cat("📁 文件名直观反映处理功能\n")
  } else {
    cat("\n⚠️  重命名过程中出现问题，请检查报告\n")
    cat(sprintf("💾 可从备份恢复: %s\n", backup_dir))
  }
}

# 执行实际重命名
main_actual_rename()

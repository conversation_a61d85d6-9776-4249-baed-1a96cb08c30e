# 01 数据基础与准备：原始数据、常规基线数据与增强数据

## 1. 研究框架概述

### 1.1 研究背景与目标
*   **研究背景**：文献计量学在科学评价中扮演关键角色，但面临工具多样化与结果不一致的困境。
*   **核心挑战**：
    *   工具多样化与结果不一致问题
    *   输入数据质量和标准化是影响分析可靠性的关键瓶颈
    *   传统分析侧重描述性统计，亟需挖掘深层因果机制

### 1.2 研究策略
*   **两个数据处理路径**：
    1. 常规基线数据构建
    2. 增强数据构建
*   **两个核心分析层面**：
    1. 验证数据增强的必要性
    2. 明确工具间差异

## 2. 数据处理路径

### 2.1 原始数据
*   **定义与来源**：从文献数据库Web of Science直接下载的、未经任何人工或程序修改的原始文本文件
*   **核心特征**：
    *   保持数据的最原始状态，未经过任何结构化处理或内容清洗。
    *   可能包含格式不一致、字段信息缺失、编码问题、特殊字符以及未经处理的重复文献记录。
*   **在比较框架中的作用**：
    作为两个数据处理路径的共同起点，为后续的常规基线数据构建和增强数据构建提供原始输入。

### 2.2 常规基线数据构建
*   **定义与来源**：
    由"原始数据"经过不同工具的处理生成，包括：
    1. 工具官方处理结果：
       - CiteSpace官方处理 → `datac_citespace_default.rds` (存放于 `data_repository/01_baseline_datasets/citespace_processed/`)
       - VOSviewer官方处理 → `datav_vosviewer_default.rds` (存放于 `data_repository/01_baseline_datasets/vosviewer_processed/`)
       - biblioshiny官方处理 → `datay_bibliometrix_initial.rds` (存放于 `data_repository/01_baseline_datasets/bibliometrix_processed/`)
    2. bibliometrix极限处理 → `datax_bibliometrix_advanced.rds` (存放于 `data_repository/01_baseline_datasets/bibliometrix_processed/`)

*   **数据处理流程**：
    1. **数据导入与初步处理**：
       * 使用 `bibliometrix::convert2df()` 函数将原始WoS数据转换为R数据框结构，此过程包含基于UT字段的**第一层精确去重**。**此去重主要针对UT完全相同的记录，不会自动去重UT不同但标题或内容高度相似的记录。**
       * 处理日志保存在 `data_repository/05_execution_logs/bibliometrix_logs/import.log`
       * 中间结果保存在 `data_repository/03_intermediate_files/bibliometrix_workflow/`
       * 最终结果保存在 `data_repository/01_baseline_datasets/bibliometrix_processed/datay_bibliometrix_initial.rds`

    2. **研究策略与实现**：
       * 对于 `datay` 的生成：
         - 基础数据通过 `01_wos_convert2df.R` 生成（`datay_bibliometrix_initial.rds`）
         - 使用 `02_deduplication_biblioshiny.R` 实现**官方去重策略**，该脚本模拟了Biblioshiny在"Merge Collections"模块中调用的`bibliometrix::mergeDbSources`函数，并将其`remove.duplicated`参数设置为`TRUE`进行去重。`mergeDbSources`内部会调用`bibliometrix::duplicatedMatching`函数，其默认去重参数为：
           * `Field = \"TI\"` (基于标题字段进行匹配)
           * `exact = FALSE` (进行模糊匹配)
           * `tol = 0.95` (相似度阈值为95%)
         - 初步分析显示，此去重策略共移除4条记录。
         - 生成 `datay_bibliometrix_biblioshiny.rds` 作为官方去重的基准数据集
       * 对于 `datax` 的生成：
         - 直接使用 `R/02_deduplication_extreme.R` 处理 `datay_bibliometrix_initial.rds`
         - **核心原则：** `datax` (bibliometrix极限处理) 的构建旨在探索和展示 `bibliometrix` 包**自身**在去重方面的极限能力。因此，其去重策略严格限定在 `bibliometrix::duplicatedMatching()` 官方文档明确支持的字段范围（`TI`、`AB`、`UT`）之内，不引入任何外部库或未官方支持的字段进行核心去重操作。更复杂或需要多工具协同的去重策略将归入\"增强数据构建\"范畴。
         - **实现基于 `bibliometrix::duplicatedMatching()` 的极限去重策略：**
           * **当前策略（考虑到性能限制）：**
             - UT精确匹配：首先基于UT字段进行精确去重，确保完全相同的记录被移除。
             - 标题模糊匹配 (`Field = \"TI\", exact = FALSE, tol = 0.98`)。此步骤使用 `duplicatedMatching()`。
             - **重要说明：** 由于当前环境的资源限制（内存和C栈限制），摘要模糊匹配暂时无法执行。这是一个技术限制，而非策略选择。如果未来环境条件允许，可以考虑添加摘要模糊匹配。
           * **去重行为说明：** 经过实验验证，`duplicatedMatching()` 函数在处理重复记录时具有**顺序敏感性**。当函数识别出一组互为重复的记录时，它会保留在输入数据框中\"首先遇到\"的那一条记录。这意味着：
             - 如果数据在传递给 `duplicatedMatching()` 之前经过了不同的排序，即使使用相同的去重参数，最终保留的记录也可能不同。
             - 这种顺序敏感性是 `duplicatedMatching()` 函数的固有特性，而非错误。
             - 如果需要更精细的\"保留最佳重复记录\"策略（例如，总是保留有DOI的，或保留被引次数更高的），这应该被归类为\"增强数据构建\"范畴，需要编写自定义的R代码来实现。
           * **最佳实践建议：**
             - 在使用 `duplicatedMatching()` 之前，建议先对数据进行明确的排序，以确保去重结果的可重复性。
             - 对于大规模数据集，建议先使用精确匹配（如UT）进行初步去重，再使用模糊匹配进行精细去重，以提高效率。
             - 在应用模糊匹配时，建议从较严格的阈值（如0.98）开始，逐步放宽阈值，以找到最佳平衡点。
             - 对于重要的分析项目，建议记录每次去重的具体参数和排序方式，以确保结果的可重复性。
           * **性能考虑与实际限制：**
             - `duplicatedMatching()` 函数在处理大规模数据集时，特别是对长文本字段（如 `AB` 摘要、`DE` 关键词、`SO` 期刊）进行模糊匹配时，可能会遇到 **C 栈溢出问题**。这主要是由于其底层依赖的 `stringdistmatrix()` 函数在计算字符串距离时，会创建大量的递归调用和临时数据，超出 R 默认的 C 栈内存限制。此问题是 `bibliometrix` 框架在深度模糊匹配某些字段时的固有实际限制。
             - 当前解决方案：
               1. 优先执行UT精确匹配，减少数据集大小。
               2. 使用较严格的标题相似度阈值（0.98）进行模糊匹配。
               3. 暂时跳过摘要模糊匹配，等待未来环境条件改善或技术突破。
             - 如果未来需要执行更全面的去重，建议：
               1. 在高性能服务器上运行。
               2. 使用分布式计算框架。
               3. 开发更轻量级的字符串相似度算法。
         - 经验证，此去重策略共移除460条记录。
         - 生成 `datax_bibliometrix_advanced.rds`

    3. **工作流程**：
       1. 通过参数查询与实验验证，明确 Biblioshiny 的去重过程
       2. 根据验证结果完善 `02_deduplication_biblioshiny.R`
       3. 使用完善后的脚本处理得到 `datay`
       4. 同时使用 `02_deduplication_extreme.R` 处理得到 `datax`

    4. **字段处理与标准化**：
       * 作者字段处理：
         - 使用 `bibliometrix::authorNormalization()` 进行作者名称标准化
         - 处理日志：`data_repository/05_execution_logs/bibliometrix_logs/author_normalization.log`
         - 中间结果：`data_repository/03_intermediate_files/bibliometrix_workflow/authors_normalized.rds`
       * 关键词处理：
         - 使用 `bibliometrix::termExtraction()` 进行关键词提取和标准化
         - 处理日志：`data_repository/05_execution_logs/bibliometrix_logs/keyword_processing.log`
         - 中间结果：`data_repository/03_intermediate_files/bibliometrix_workflow/keywords_extracted.rds`
       * 参考文献处理：
         - 使用 `bibliometrix::citations()` 进行引文分析
         - 处理日志：`data_repository/05_execution_logs/bibliometrix_logs/citation_processing.log`
         - 中间结果：`data_repository/03_intermediate_files/bibliometrix_workflow/citations_processed.rds`
       * **元数据提取与标准化**：
         - 使用 `bibliometrix::metaTagExtraction()` 函数进行元数据（如被引参考文献作者信息）的提取和标准化。其默认参数为：
           - `Field = "CR_AU"` (从被引参考文献中提取作者)
           - `sep = ";"` (使用分号作为分隔符)
           - `aff.disamb = TRUE` (开启机构消歧)
         - 这有助于提高数据质量和分析准确性。

    5. **数据去重与清洗**：
       * 基于WoS的UT字段进行初步去重
       * 使用 `bibliometrix::duplicatedMatching()` 进行模糊匹配去重
       * 处理日志：`data_repository/05_execution_logs/bibliometrix_logs/deduplication.log`
       * 去重结果：`data_repository/03_intermediate_files/bibliometrix_workflow/deduplicated_full_workflow.rds`

    6. **数据验证与质量检查**：
       * 使用 `bibliometrix::biblioAnalysis()` 进行基础统计分析
       * 生成数据质量报告：`data_repository/06_data_reports/biblioshiny_reports/data_quality.html`
       * 记录处理统计信息：`data_repository/05_execution_logs/bibliometrix_logs/processing_stats.csv`

    7. **结果导出与存储**：
       * 最终处理结果保存为RDS格式：`data_repository/01_baseline_datasets/bibliometrix_processed/datay_bibliometrix_initial.rds`
       * 同时导出为CSV格式：`data_repository/01_baseline_datasets/bibliometrix_processed/datay_bibliometrix_initial.csv`
       * 处理元数据保存：`data_repository/07_metadata_and_notes/bibliometrix_metadata/processing_metadata.json`

*   **核心特征**：
    *   数据已经过初步的结构化和清洗，字段得到识别并按标准格式进行组织。
    *   经过了基础的文献去重，相比原始数据更为规整，减少了明显的重复条目。
    *   代表了各工具的原始处理能力。
    *   完整的处理日志和中间结果便于回溯和验证。

*   **在比较框架中的作用**：
    1.  **评估工具初始能力**：通过比较各工具处理原始数据的结果，评估其初始处理能力。
    2.  **数据增强效果的参照基准**：作为后续"增强数据"的对照组，用于验证数据增强的必要性。
    3.  **处理过程可追溯**：通过详细的日志和中间结果，可以回溯和验证每个处理步骤。

### 2.3 增强数据构建
*   **定义与来源**：
    完全独立于常规基线数据，采用更好的处理方法构建的高质量数据集。这些处理方法旨在最大限度地提升数据的准确性、完整性和一致性，包括：
    *   利用外部API对文献的关键字段进行自动查询和补全。
    *   实施更精细和多维度的文献去重策略。
    *   对关键词进行深度标准化、清洗、合并同义词等处理。
    *   作者姓名及机构信息的进一步消歧、规范化和匹配。
    *   文献类型、研究主题或研究方法的细化分类与补充标注。

*   **核心特征**：
    *   数据质量达到最优状态，字段信息力求完整、准确，重复数据得到有效控制。
    *   关键分析元素经过严格的标准化和消歧处理。
    *   为深度、可靠的文献计量分析提供了坚实的数据基础。

*   **在比较框架中的作用**：
    1.  **验证数据增强的必要性**：通过对比增强数据与常规基线数据的分析结果，证明高质量数据处理的重要性。
    2.  **明确工具间差异**：作为统一输入，让各工具处理增强数据，比较其处理效果，分析工具间的差异和特点。

> **新版目录：** 增强数据集应存放于 `data_repository/02_enhanced_dataset/enhanced_data_optimized.rds`

## 3. 数据采集与处理

### 3.1 比较分析框架
本研究框架的比较分析主要围绕两个数据处理路径展开：

1.  **常规基线数据路径**：
    *   评估各工具（CiteSpace、VOSviewer、biblioshiny）的官方处理能力
    *   评估bibliometrix的极限处理能力
    *   作为数据增强效果的参照基准

2.  **增强数据路径**：
    *   验证数据增强的必要性和效果
    *   评估各工具在最优数据条件下的性能表现
    *   探索多工具联合应用的策略

这种分路径的比较策略，有助于全面揭示数据质量在文献计量分析中的关键作用，并系统评估不同分析工具及本框架所提出方法的特性与效能。

### 3.2 基于增强数据的多工具横向比较：目标与联合应用指导

在增强数据路径中，使用统一的"增强数据"对多种文献计量工具进行横向比较，其核心目标在于：

1.  **揭示工具间的分析侧重与算法特性差异**
2.  **识别各工具在特定分析任务上的相对优势与局限**
3.  **探索多工具联合应用的策略与指导**
4.  **为本框架个性化算法的价值定位**

此部分旨在通过系统性的多工具比较，不仅评估工具性能，更重要的是提炼出能够指导研究者更有效地组合和运用这些强大分析工具的实践智慧。

## 2. 常规基线数据集构建：对比基准定义

为了有效评估本框架提出的高质量数据处理流程（用于构建"增强数据集"）的实际效果，并为层面一的对比分析提供参照，我们首先定义"常规方法数据输入基线"，用于构建"**常规基线数据集**" (`baseline_data`)。

*   **数据来源：** 仅使用从单一主要数据源（如Web of Science核心合集）下载的原始数据文件（例如，纯文本格式）。
*   **数据导入与初步处理（模拟不同工具）：**
    *   **初步结构化转换：** 利用 `bibliometrix::convert2df()` 函数将原始WOS格式的数据转换为R数据框（DataFrame）结构。运行 `R/01_data_import.R` 脚本即可生成此数据，文件保存在 `data_repository/01_baseline_datasets/bibliometrix_processed/datay_bibliometrix_initial.rds`。此文件即对应了**模拟的biblioshiny官方导入处理（`datay`）**，因为它使用了 `convert2df` 函数，并且 `convert2df` 内部自动基于WoS的UT字段进行了初步去重。此数据作为后续不同去重方法（包括\"bibliometrix极限处理\"）的统一输入基线。

    *   **研究策略说明：**
        * 对于 `datay` 的生成：
          - 基础数据通过 `01_wos_convert2df.R` 生成（`datay_bibliometrix_initial.rds`）
          - 使用 `02_deduplication_biblioshiny.R` 实现**官方去重策略**
            * 标题去重（相似度阈值0.95）
          - 生成 `datay_bibliometrix_biblioshiny.rds` 作为官方去重的基准数据集
        * 对于 `datax` 的生成：
          - 直接使用 `R/02_deduplication_extreme.R` 处理 `datay_bibliometrix_initial.rds`
          - **核心原则：** `datax` (bibliometrix极限处理) 的构建旨在探索和展示 `bibliometrix` 包**自身**在去重方面的极限能力。因此，其去重策略严格限定在 `bibliometrix::duplicatedMatching()` 官方文档明确支持的字段范围（`TI`、`AB`、`UT`）之内，不引入任何外部库或未官方支持的字段进行核心去重操作。更复杂或需要多工具协同的去重策略将归入\"增强数据构建\"范畴。\
          - **实现基于 `bibliometrix::duplicatedMatching()` 的极限去重策略：**
            * **当前策略（考虑到性能限制）：**
              - UT精确匹配：首先基于UT字段进行精确去重，确保完全相同的记录被移除。
              - 标题模糊匹配 (`Field = \"TI\", exact = FALSE, tol = 0.98`)。此步骤使用 `duplicatedMatching()`。
              - **重要说明：** 由于当前环境的资源限制（内存和C栈限制），摘要模糊匹配暂时无法执行。这是一个技术限制，而非策略选择。如果未来环境条件允许，可以考虑添加摘要模糊匹配。
            * **去重行为说明：** 经过实验验证，`duplicatedMatching()` 函数在处理重复记录时具有**顺序敏感性**。当函数识别出一组互为重复的记录时，它会保留在输入数据框中\"首先遇到\"的那一条记录。这意味着：
              - 如果数据在传递给 `duplicatedMatching()` 之前经过了不同的排序，即使使用相同的去重参数，最终保留的记录也可能不同。
              - 这种顺序敏感性是 `duplicatedMatching()` 函数的固有特性，而非错误。
              - 如果需要更精细的\"保留最佳重复记录\"策略（例如，总是保留有DOI的，或保留被引次数更高的），这应该被归类为\"增强数据构建\"范畴，需要编写自定义的R代码来实现。
            * **最佳实践建议：**
              - 在使用 `duplicatedMatching()` 之前，建议先对数据进行明确的排序，以确保去重结果的可重复性。
              - 对于大规模数据集，建议先使用精确匹配（如UT）进行初步去重，再使用模糊匹配进行精细去重，以提高效率。
              - 在应用模糊匹配时，建议从较严格的阈值（如0.98）开始，逐步放宽阈值，以找到最佳平衡点。
              - 对于重要的分析项目，建议记录每次去重的具体参数和排序方式，以确保结果的可重复性。
            * **性能考虑与实际限制：**
              - `duplicatedMatching()` 函数在处理大规模数据集时，特别是对长文本字段（如 `AB` 摘要、`DE` 关键词、`SO` 期刊）进行模糊匹配时，可能会遇到 **C 栈溢出问题**。这主要是由于其底层依赖的 `stringdistmatrix()` 函数在计算字符串距离时，会创建大量的递归调用和临时数据，超出 R 默认的 C 栈内存限制。此问题是 `bibliometrix` 框架在深度模糊匹配某些字段时的固有实际限制。
              - 当前解决方案：
                1. 优先执行UT精确匹配，减少数据集大小。
                2. 使用较严格的标题相似度阈值（0.98）进行模糊匹配。
                3. 暂时跳过摘要模糊匹配，等待未来环境条件改善或技术突破。
              - 如果未来需要执行更全面的去重，建议：
                1. 在高性能服务器上运行。
                2. 使用分布式计算框架。
                3. 开发更轻量级的字符串相似度算法。
            - 经验证，此去重策略共移除460条记录。
            - 生成 `datax_bibliometrix_advanced.rds`

        * 工作流程：
          1. 通过后台监控明确 Biblioshiny 的去重过程
          2. 根据监控结果完善 `02_deduplication_biblioshiny.R`
          3. 使用完善后的脚本处理得到 `datay`
          4. 同时使用 `02_deduplication_extreme.R` 处理得到 `datax`

    *   **\"bibliometrix极限处理\" (datax):** 基于 `data_repository/01_baseline_datasets/bibliometrix_processed/datay_bibliometrix_initial.rds` 文件，运行 `R/02_deduplication_extreme.R` 脚本，应用更全面的去重策略（如模糊匹配等），生成\"bibliometrix极限处理\"后的常规基线数据集。此文件保存在 `data_repository/01_baseline_datasets/bibliometrix_processed/datax_bibliometrix_advanced.rds`.
    *   **模拟 VOSviewer/CiteSpace 常规使用:** 直接将原始数据文件导入相应工具，使用其内置的默认数据导入、清洗和去重选项，**不进行**外部的深度处理。处理结果分别保存在 `data_repository/01_baseline_datasets/vosviewer_processed/datav_vosviewer_default.rds` 和 `data_repository/01_baseline_datasets/citespace_processed/datac_citespace_default.rds`。

*   **核心特征**：
    *   数据已经过初步的结构化和清洗，字段得到识别并按标准格式进行组织。
    *   经过了基础的文献去重，相比原始数据更为规整，减少了明显的重复条目。
    *   代表了各工具的原始处理能力。

*   **在比较框架中的作用**：
    1.  **评估工具初始能力**：通过比较各工具处理原始数据的结果，评估其初始处理能力。
    2.  **数据增强效果的参照基准**：作为后续"增强数据"的对照组，用于验证数据增强的必要性。

### 1.4 增强数据集构建：多源数据采集与结构化 

> **新版目录：** 所有增强数据相关的中间文件、日志、报告、元数据等，均应存放于 `data_repository/02_enhanced_dataset/`、`data_repository/03_intermediate_files/`、`data_repository/05_execution_logs/`、`data_repository/06_data_reports/`、`data_repository/07_metadata_and_notes/` 等对应目录。

### 1.5 多源数据采集 

**优先级数据源设计：**

* **主要数据源：** Web of Science核心合集(SCI/SSCI/A&HCI)
    * **设计原因：** 提供高质量、经同行评议的文献数据，收录标准严格，学科分类体系完善。
    * **优势：** 提供标准化的引文数据，有完善的学科分类体系，支持全面的引文检索。
* **补充数据源（作为api调取来源）：** Scopus、Dimensions、OpenAlex、**PubMed API**
    * **设计原因：** 扩展文献记录覆盖范围，弥补WoS收录范围的局限性，**特别是获取更精确的生物医学文献类型（如MeSH Publication Types）和摘要等信息**。
    * **优势：** Scopus提供更广泛的期刊覆盖；Dimensions包含更多资助信息；OpenAlex为开放获取数据；**PubMed提供权威的MEDLINE索引信息（含MeSH）**。
* **专业数据源：** 领域特定数据库(IEEE Xplore、PubMed等) - **注：此处PubMed指作为初始数据源的可能性，若主要依赖WoS，则PubMed更多通过API作为补充源。**
    * **设计原因：** 获取特定领域的专业文献，提高领域覆盖完整性。
    * **优势：** 包含领域专有的分类和索引术语，可能收录更多会议论文和专业资料。
    * **挑战：** 格式不一致，专业术语和分类体系需要与通用数据源整合。

* **新兴数据源与特殊索引考量：**
    *   **预印本服务器与索引:** 如 bioRxiv, medRxiv, arXiv API；WoS Preprint Citation Index。用于捕捉早期研究信号。
    *   **数据索引:** 如 WoS Data Citation Index。用于分析数据共享和重用。

* **需关注的关键字段（除标准书目信息外）：**
    *   **`OA` (开放获取状态):** 分析知识传播模式。
    *   **`FU` (资助机构与编号), `FX` (资助文本):** 分析科研投入与产出关联。

**检索策略透明化：** 详细记录检索词、过滤条件、时间范围。

* **操作方法：** 创建标准化的检索策略文档模板，包含以下字段：主题词列表(包括同义词、上位词、下位词)；布尔运算符组合逻辑(AND, OR, NOT的精确配置)；字段限定条件(TI=标题, AB=摘要, AK=作者关键词等)；引用和被引用文献关系条件；语言、文献类型、学科类别等过滤条件；版本控制信息(执行日期、数据库版本号)。

**多阶段采集：** 初步检索→精炼→扩展→确认

* **操作方法：**
    * **初步检索：** 执行初始检索词组合，记录结果数量和抽样质量评估。
    * **精炼阶段：** 分析初步结果中的学科分布、关键词分布，调整检索策略。
        * *技术细节：* 使用WoS/Scopus的分析功能生成主题分布直方图。
        * *设置阈值：* 保留频率>0.5%的学科类别，移除偏离核心主题的类别。
    * **扩展阶段：** 应用引文扩展方法(向前、向后追溯)。
        * *向前追溯：* 识别初步检索集合中的高被引文献（例如，引用频次≥领域均值2倍），然后查找并纳入**引用这些高被引文献的（通常是较新的）文献**，目的是捕获基于核心文献的新研究发展和应用。
        * *向后追溯：* 纳入引用至少3篇核心文献的文献，目的是发现与研究主题高度相关但未被初始检索捕获的文献。
    * **确认阶段：** 与领域专家确认最终检索结果的覆盖面和准确性。
        * *使用学科关键文献覆盖率指标评估(覆盖率应≥90%)。*
        * *技术实现：*
            1.  建立"基准文献清单"：此清单用于评估检索策略的覆盖度。来源包括：
                *   领域专家提名
                *   核心期刊（如JCR Q1或领域公认顶级期刊）近期（如过去5年）发表的代表性论文
                *   （可选，若数据收集后）从初步数据集中筛选出的高全局被引文献（Global Citation Score, GCS ≥ 数据集平均 GCS 的 2 倍），作为补充验证。
            2.  检索匹配：在目标数据库中检索这些基准文献，记录成功匹配的数量。
            3.  计算覆盖率：(匹配成功的基准文献数 / 总基准文献数) × 100%。
            4.  验证方法：人工抽查未匹配文献，分析未匹配原因。
            5.  改进措施：调整检索策略、补充数据源、优化关键词组合。

**文献类型控制：** 明确界定纳入分析的文献类型。初步筛选主要依据数据库提供的标准文档类型字段（如 WoS/Scopus 的 `DT` 字段）。

* **初步筛选优先级（基于标准 `DT` 字段）：**
    *   `Article` (研究论文)
    *   `Review` (综述)
    *   `Letter` (快报) - 视学科领域和研究问题决定是否纳入，需谨慎评估其在知识贡献中的角色。
    *   `Editorial Material` (社论) - 通常排除，除非研究主题明确关注学术观点和评论。
    *   `Book Chapter` (书籍章节) - 若数据源包含且与研究问题相关，可考虑。
    *   `Conference Proceeding` (会议论文) - 根据学科特性和会议质量决定。在计算机科学等领域可能非常重要。
* **二次筛选与确认：**
    *   **基于标题/摘要的语义判断：** 对 `DT` 字段模糊或信息量不足的记录，通过关键词匹配（如排除"corrigendum", "erratum", "retraction notice"）或结合人工审阅进行确认。
    *   **利用API获取精确类型：** 如利用PubMed API获取MeSH中的Publication Types，以更准确区分研究设计（例如，Clinical Trial, Meta-Analysis, Systematic Review）。
    *   **领域专家确认：** 对有争议的文献类型，由领域专家进行最终判断。
* **记录与报告：** 清晰记录文献类型的纳入和排除标准及其理由。

### 3.4 数据结构化与字段标准化

#### 3.4.1 标准数据格式定义
*   **内部标准格式：** 本框架内部主要采用基于R语言的`data.frame`结构存储和处理文献数据，借鉴并扩展`bibliometrix`包的字段命名约定。
*   **核心目标：** 确保所有来源的数据在导入后，其关键字段（如作者AU、标题TI、摘要AB、关键词DE/ID、期刊SO、发表年份PY、参考文献CR、DOI等）得到统一、规范的命名和初步的结构化处理（如多值字段统一使用分号分隔）。
*   **字段映射表：** 针对不同数据源（WoS, Scopus, PubMed等）的原始字段名与本框架内部标准字段名，建立清晰的映射关系表。此表应详细说明原始字段的含义、数据类型以及向标准字段转换时的注意事项。
    *   *示例：WoS的`AU` (Authors) -> 标准`AU`；Scopus的`Authors` -> 标准`AU`。WoS的`DE` (Author Keywords) -> 标准`DE`；Scopus的`Author Keywords` -> 标准`DE`。*

#### 3.4.2 关键字段的初始处理与映射规则
*   **作者字段 (AU, AF, C1, RP, AU_UN, AU_ID, ORCID):**
    *   `AU` (Authors): 初始提取，通常为姓, 名缩写格式。
    *   `AF` (Authors Full Name): 尝试获取作者全名。
    *   `C1` (Authors' Affiliations): 提取作者机构地址信息。
    *   `RP` (Reprint Address): 通讯作者地址，对第一作者或通讯作者机构识别重要。
    *   `AU_UN` (Author Unit of Affiliation): (后续生成) 标准化后的作者单位。
    *   `AU_ID` (Author ID): (后续生成) 作者唯一标识符。
    *   `ORCID`: 优先获取并存储ORCID。
*   **文献标识符 (UT, DI, PMID, EID):**
    *   `UT` (Unique Article Identifier): 如WoS的Accession Number。
    *   `DI` (Digital Object Identifier): DOI号，非常关键的唯一标识。
    *   `PMID` (PubMed ID): PubMed唯一标识。
    *   `EID` (Scopus ID): Scopus唯一标识。
*   **标题与摘要 (TI, AB):**
    *   进行初步清洗，如去除不必要的HTML标签、特殊转义字符。
    *   注意编码问题，统一转换为UTF-8。
*   **关键词 (DE - Author Keywords, ID - Keywords Plus/Index Terms):**
    *   统一小写化。
    *   去除首尾空格。
    *   多值关键词统一用分号分隔，并确保分号后无多余空格。
*   **期刊信息 (SO, J9, JI, SN, ISSN, eISSN):**
    *   `SO` (Source): 期刊全称。
    *   `J9` (29-Character Source Abbreviation): WoS提供的标准缩写。
    *   `JI` (ISO Source Abbreviation): ISO标准期刊缩写。
    *   `SN` (ISSN): 国际标准期刊号。
    *   `ISSN`, `eISSN`: 分别记录印刷版和电子版ISSN。
*   **发表年份 (PY):** 确保为数字格式。处理可能的错误格式或范围年份。
*   **参考文献 (CR):** 保持原始格式，后续进行专门解析和标准化。
*   **被引频次 (TC, Z9):**
    *   `TC` (Times Cited, WoS local): WoS数据库内被引频次。
    *   `Z9` (Total Times Cited Count (WoS, SCIELO, CSCD, SciELO)): WoS提供的更广泛的被引计数。
    *   记录数据来源和更新时间。
*   **文献类型 (DT):** 依据 `3.4.1` 中的文献类型控制进行初步分类和记录。
*   **其他字段：** 如 `LA` (Language), `FU` (Funding Agency and Grant Number), `FX` (Funding Text), `SC` (Subject Category)等，根据研究需求进行提取和初步处理。

#### 3.4.3 数据清洗与初步校验
*   **缺失值处理：** 识别关键字段的缺失情况，记录缺失比例。对DOI、PY等核心字段的缺失，考虑后续通过API补全。

## 4. 实施指南

### 4.1 去重策略实施

#### 4.1.1 基础去重（官方标准）
* **UT字段去重**：使用`convert2df`函数进行基础去重
* **标题模糊匹配**：使用`duplicatedMatching`函数，阈值0.95
* **结果记录**：记录去重前后的记录数量变化

#### 4.1.2 增强去重（自定义策略）
* **DOI精确匹配**：优先使用DOI进行精确去重
* **标题多轮匹配**：
  - 第一轮：阈值0.98（严格匹配）
  - 第二轮：阈值0.95（标准匹配）
  - 第三轮：阈值0.90（宽松匹配）
* **作者匹配**：阈值0.95，考虑作者顺序
* **摘要匹配**：阈值0.90，用于补充验证

#### 4.1.3 内存优化策略
* **数据预处理**：
  - 统一数据类型为字符型
  - 预处理NA值为空字符串
  - 分块处理大量数据
* **计算优化**：
  - 使用矩阵运算代替循环
  - 跳过空字段的处理
  - 及时清理中间结果
* **资源管理**：
  - Windows：设置内存限制和表达式递归限制
  - Unix：调整栈大小限制

### 4.2 工具使用指南

#### 4.2.1 Biblioshiny
* **数据导入**：支持多种数据源格式
* **基础分析**：提供文献计量分析的基本功能
* **可视化**：生成各类分析图表
* **结果导出**：支持多种格式的结果导出

#### 4.2.2 VOSviewer
* **数据准备**：数据格式要求与转换方法
* **网络构建**：共词、共被引等网络构建方法
* **可视化设置**：节点、连线的显示参数设置
* **结果解读**：网络图的解读方法

#### 4.2.3 CiteSpace
* **数据导入**：支持的数据格式与导入方法
* **参数设置**：时间切片、阈值等参数设置
* **分析功能**：突发检测、中心性分析等
* **结果解读**：各类分析结果的解读方法

### 4.3 结果验证与报告

#### 4.3.1 去重效果验证
* **记录数量变化**：详细记录每步去重的记录数量变化
* **去重原因分析**：分析每条记录被去重的原因
* **质量评估**：评估去重对数据质量的影响

#### 4.3.2 性能监控
* **内存使用**：监控每步去重的内存使用情况
* **处理时间**：记录每步去重的处理时间
* **资源消耗**：记录CPU和内存的峰值使用情况

#### 4.3.3 报告生成
* **去重报告**：生成详细的去重过程报告
* **性能报告**：记录性能优化效果
* **异常记录**：记录处理过程中的异常情况

> 注：以上策略基于`02_deduplication_custom.R`脚本的成功经验，该脚本通过合理的优化策略成功避免了C栈溢出问题。具体实现细节可能会随项目进展而调整，但核心优化思路保持不变。

## 5. 总结与展望
*   **研究框架优势**
*   **应用价值**
*   **未来发展方向** 
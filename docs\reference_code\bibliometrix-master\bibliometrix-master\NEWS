bibliometrix v4.3.0 (Release date: 2024-06-30)

Features:
* Improved the function mergeDbSources()
  Now it's possible to merge collection from all supported databases
* Added the submenu "Merge Collections" in biblioshiny
  Through the new submenu "Merge Collections", it's possible to merge collections from different DBs directly in Biblioshiny

bibliometrix v4.2.3 (Release date: 2024-05-27)

Changes:
* Major fixes in bibliometrix functions

bibliometrix v4.2.2 (Release date: 2024-05-23)

Changes:
* Major fixes in bibliometrix functions
* Many improvements in Biblioshiny tables and figures 

bibliometrix v4.2.1 (Release date: 2024-05-01)

Changes:
* Major fixes in convert2df for Scopus and Lens export files
* Many improvements in Biblioshiny tables and figures 


bibliometrix v4.2.0 (Release date: 2024-04-18)

Features:
* Added the support to OpenAlex database both using export files and API
* Added the function net2Pajek to export network objects as Pajek files

Changes:
* Biblioshiny: Added an info page about supported databases and suggested format files
* Biblioshiny: In Conceptual Structure methods, added an algorithm to automatically hide overlapping labels 
* Biblioshiny: Added a popup window which shows the stopword list and the synonym list when uploaded
* Removed depencence from factominer package
* Many code and graphical improvements


bibliometrix v4.1.4 (Release date: 2023-11-29)

Changes:
* Biblioshiny: After the data quality check, the menus of the analyses that cannot be performed are hidden
* Start to adding support to OpenAlex data
* Improvements in historiograph: 
   - Improved Scopus algorithm to match local citations
   - Added OpenAlex algorithm to match local citations
   - Added Lens.org algorithm to match local citations
* Improved pajek export in biblioshiny
* Computational speed improvements
* Solved issue with igraph 1.6.0 release canddate 
* Solved issue with Matrix package

bibliometrix v4.1.2 (Release date: 2023-03-06)

Changes:
* Solved issue with new R version (R-devel no longer sets a 'stringsAsFactors' option)
* Computational speed improvements


bibliometrix v4.1.0 (Release date: 2023-01-13)

Features:
* Added a new function missingData() to check the completeness of metadata included in a bibliographic data frame 
* Biblioshiny: Added the ability to create an excel report by adding step by step results of different analysis
* Biblioshiny: Added a popup that returns the results of the metadata completeness check of imported collections
* Biblioshiny: Revamped interface with floating options menu and more space for graphical analysis results

Changes:
* Several bug fixes
* Computational speed improvements



bibliometrix v4.0.1 (Release date: 2022-09-16)

Features:
* Biblioshiny: Added longitudinal analysis for all units (Documents, Authors, Journals)

Changes:
* A lot of bug fixes


bibliometrix v4.0.0 (Release date: 2022-05-31)

Features:
* Biblioshiny has a completely renewed interface (Biblioshiny 4.0)
* All plots have new more readble sets of colors and fonts
* Added several new analyses in Biblioshiny

Changes:
* A lot of bug fixes


bibliometrix v3.2.1 (Release date: 2022-02-21)

Changes:
* shinythemes is no longer supported. It has been replaced by new bslib package


bibliometrix v3.2 (Release date: 2022-02-10)

Features:
* Added the possibility to load a list of terms to remove in all content analyses
* Added the new package logo in all plots

Changes:
* Improvements of scopus CSV file importing function
* Solved encoding issues about non UTF-8 character sets for bibtex files.


bibliometrix v3.1.4 (Release date: 2021-07-05)

Changes:
* Added the possibily to calculate all vertex centrality measures setting the argument type="all"
* Solved the issue with extracting cited sources from Scopus collections (function metaTagExtraction)
* Solved the issue related to exclusion of the ggnetwork package from CRAN by removing the dependence from the package 


bibliometrix v3.1.3 (Release date: 2021-05-25)

Changes:
* Solved issue in bibliometrix binaries for linux releases (from CRAN checks)


bibliometrix v3.1.1 (Release date: 2021-05-10)

New functionalities:
* Content analysis functions (conceptualStructure, biblioNetwork, biblioAnalysis, etc) include the analysis of bigrams and trigrams extracted from titles and abstracts 
* Three Field Plot: improved plot layout using plotly libraries
* splitCommunities: new function to create a network plot with separated communities
* histPlot: new, improved, plot layout
* fieldByYear: New Trend Topic Plot

Changes:
* Added bibliometrix logo in all plots
* networkPlot: Improved layout algorithms to obtain better separated clusters. The level of separation is guided by a new argument "community.repulsion"
* biblioshiny: Added maxUploadSize argument to biblioshiny(). Now it is possible to set any max upload file size for the biblioshiny app using the argument "maxUploadSize". The default value is maxUploadSize=200 (megabytes) 
* biblioshiny: Added the possibility to use a sample collection 
* biblioshiny: New layout for all frequency plots (sources, authors, docuemnts, etc.)
* couplingMap: Added the possibility to label clusters through content metadata (I.e. Keywords, title terms, etc.)
* Hindex: Huge improvements in execution time.


bibliometrix v3.0.4 (Release date: 2021-01-18 )

New functionalities:
* biblioshiny: added the option to export plots as png choosing dpi value and image height
* biblioshiny: added Coupling analysis by coupling menu tab
* biblioshiny: added automatic recognition of export file formats in Data menu
* couplingMap: new function performing coupling analysis on documents, authors and sources using different coupling strength measures
* normalizeCitationScore: new fuction calculating NCS for documents, authors or sources using both gloabl and local citations

Changes:
* ThematicMap: added quadrant label annotations in plot
* Biblioshiny: added the "Export graph as PNG" button to network plots
* Biblioshiny: in  network plots, added the button "Edit" for editing nodes (add, change, delete)
* Biblioshiny: improvements in plot resolution and resizing
* Removed vignettes and examples Datasets to respect CRAN rules about package max size (<5Mb).
  Examples datasets are now included in a new package bibliometrixData.
  Vignettes are published on www.bibliometrix.org
* Added dependence to bibliometrixData package



bibliometrix v3.0.3 (Release date: 2020-09-25)

Changes:
* biblioshiny: Improvements in Historiograph visualization
* biblioshiny: Rewritten treemap using plotly
* biblioshiny: Removed treemap package dependence
* histPlot: Historiograsph now allows to plot document titles
* rpys: added html link to Google Scholar


bibliometrix v3.0.2 (Release date: 2020-06-17 )
==============

New functionalities:
* duplicatedMatching: added the argument "exact"
 
Changes:
* New layout for frequency plots (Biblioshiny sections: Sources, Authors and Documents)
* convert2df: Bug fixes
* biblioshiny: improved management of reactive events
* plots: removed warnings by ggplot2 package
* ThematicMap: min frequency now is a proportion


bibliometrix v3.0.1 (Release date: 2020-05-25)
==============

New functionalities:
* biblioshiny: Added "Node Degree Plot" in Conceptual, Intellectual and Social networks 
 
Changes:
* convert2df: Bug fixes when importing uncomplete metadata
* histNetwork: Bug fixes


bibliometrix v3.0.0 (Release date: 2020-04-15)
==============

New functionalities:
* bibliometrix: Now it is possible to gather data directly using 'DS Dimensions' or 'NCBI PubMed' APIs
* biblioshiny: Added "Most Local Cited Authors" analysis in Author Menu
* biblioshiny: biblioshiny() added the arguments port, host, and launch.browser 
* biblioshiny: Now it is possible to import and convert 'PubMed' and 'Cochrane Library' export files
* convert2df: Now it imports and converts json/xml collections downloaded using 'DS Dimensions' or 'NCBI PubMed' APIs
* convert2df: Now it imports and converts export files downlaoded from 'NCBI PubMed' in pubmed format
* convert2df: Now it imports and converts export files downlaoded from 'Scopus' in csv format
* convert2df: The routine to convert data from WOS, Scopus, PubMed and Cochrane plaintext and bibtex files has been completely rewritten to be extremely more accurate and faster.

Changes:
* Added dependence to dimensionsR and pubmedR packages to gather data using APIs
* Added dependence to tidyr package 
* histNetwork: the function has been completely rewritten to improve matching accuracy and speed
* biblioAnalysis: Now it returns also the number of references (nReferences)
* summary: Changed the layout of "Main Information about Data" table
* cocMatrix: Added the argument "n" to set the number of items to include in the occurrences matrix
* biblioNetwork: Added the argument "n" to set the number of items to include in the co-occurrences matrix
* networkPlot: Now it returns also the object nodeDegree
* Converting family internal functions are no longer visible to users.
* Removed the function readFiles. Its functionalities are now included in convert2df
* Removed dependence to reshape2 package
* Removed dependence to RISmed package


bibliometrix v2.3.2 (Release date: 2019-11-23)
==============

New functionality:
* biblioshiny: Introduced a new export window to export the collection into an excel/RData file
* biblioshiny: Assigned custom names to excel export buttons for all tables

bibliometrix v2.3.1 (Release date: 2019-11-01)
==============

New functionality:
* biblioshiny: added the support to Dimensions files
* convert2df(): added the support to Dimensions files
* dimensions2df(): introduced ths new function to import and convert Dimensions cvs/xlsx files


Changes:
* bug fixes in many functions




bibliometrix v2.2.2 (Release date: )
==============

New functionality:
* biblioshiny: added "remove isolated nodes" option in network analysis
* net2VOSviewer: introduced this new function to export and plot networks in VOSviewer software


Changes:
* networkPlot: Fixed an issue with "remove.isolates" argument
* networkPlot: Removed vosviewer option in "type" argument. Now networks can be exported in VOSviewer using the new function net2VOSviewer()



bibliometrix v2.2.1 (Release date: 2019-06-26)
==============

New functionality:
*conceptualStructure: Added the Benzecrì eigenvalue correction
*conceptualStructure: Added the explained variance information in the plot axis labels
*thematicEvolution: new measure "Inclusion Index weighted by Term Occurrences"
*thematicMap: Added the possibility to associate more than one label to each cluster (argument n.labels)
*thematicEvolution: Added the possibility to associate more than one label to each cluster (argument n.labels)
*biblioshiny: Integrated new functionality of conceptualStructure, thematicMap and thematicEvolution
*New dataset: management: The use of bibliometric approaches in business and management disciplines



bibliometrix v2.2.0 (Release date: 2019-05-02)
==============

New functionality:
*biblioshiny: new threeFieldsPlot in the Dataset tab
*added the function threeFieldsPlot 
*Speed improvements in several functions


bibliometrix v2.1.2 (Release date: 2019-03-14)
==============

New functionality:
*biblioshiny: new dendrogram plot in the factorial analysis (Conceptual Structure)
*biblioshiny: added the number of cluster argument in the factorial analysis (Conceptual Structure)
*biblioshiny: added Most Cited Sources (from References) in Sources
*biblioshiny: added disambiguation parameter in affiliation
*Speed improvements in several functions

Changes:
* retrievalByAuthorID: Now, even when there is an error, the function continues to download the data



bibliometrix v2.1.1 (Release date: 2019-02-07)
==============

New functionality:
* biblioshiny: all plots are now dynamic (using plot.ly or VisNetwork)
* biblioshiny: added new plots in dataset menu
* biblioshiny: Menu "Conceptual Structure" has been re-organized
* the function thematicMap has been completely rewritten. Now it automatically calculates the network and plots the map.




bibliometrix v2.1.0 (Release date: 2019-01-06)
==============

New functionality:
* biblioshiny networks are now plotted using VisNetwork package.
* biblioshiny menu has been completely rewritten. Now, descriptive analyses are organized by the unit of analysis
* Several descriptive plots and tables have been added
* Added the new function authorProdOverTime to calculate and plot the productivity over the time of the top authors
* Added "measure" parameter in plotThematicEvolution
* Added the new function bib2df. It gives the possibility to import data from a "generic" bibtex file format  (i.e. Zotero, JabRef, etc.)
* Added the possibility to calculate H-index for sources


Changes:
* Completely rewritten the importing function from bibtex files
* Solved an issue in dominance(). Now Dominance factor is correctly calculated
* Solved several issues in importing functions due to inconsistence in WOS/SCOPUS web-exporting procedures



bibliometrix v2.0.2 (Release date: 2018-10-26)
==============

New functionality:
* Added Country scientific production in descriptive analysis (biblioshiny)
* Added country collaboration map layout in biblioshiny
* Added "opacity" parameter in networkPlot
* Added "field" parameter in thematicMap
* Added "field" parameter in thematicEvolution
* Added bradford() function to identify core sources 
* Added law analyses (bradford and lotka) in biblioshiny 
* Added "stat" parameter in networkStat()
* Added "type" parameter in networkStat()
* Added Multidimensional Scaling Analysis in conceptualStructure()
* Added "shortlabel" parameter in bibloNetwork()


Changes:
* Now plots show lowercase labels
* networkStat() now allows calculating single centrality indices (using parameters stat and type)
* Improved the optimal number of clusters in conceputalStructure(). Now the wss criterium is used


bibliometrix v2.0.1 (Release date: 2018-10-09)
==============

New functionality:
* added the possibility to load Rdata files in biblioshiny()
* added Thematic Evolution to biblioshiny()
* added the function plotThematicEvolution() to draw thematic evolution
* added Thematic Map to biblioshiny()
* added TreeMap plot to biblioshiny()
* added Word Dynamic Analysis biblioshiny()
* added Source Dynamic Analysis to biblioshiny()
* added the function rpys() to perform the Reference Publication Year Spectroscopy
* added the function sourceGrowth to calculate source publication time series
* new white layout for biblioshiny()



bibliometrix v2.0.0 (Release date: 2018-09-03)
==============

New functionality:
* added biblioshiny(): A shiny app for bibliometrix R-package



bibliometrix v1.9.4 (Release date: 2018-06-24)
==============

New functionality:
* Added a new, and more readable, historiograph plot in histPlot()
* Added function networkStat() to perform the descriptive analysis of network graph characteristics
* Added the generic function summary() to summarize the results of the networkStat() function 
* Added a new section "the descriptive analysis of network graph characteristics" in the vignettes
* Added the possibility to use scopus collections in histNetwork() function
* Added the possibility, in histNetwork() function, to set the minimum number of citations for the documents included in the analysis
* Added the argument aff.disamb in metaTagExtraction()

Changes:
* Solved the compatibility issue with the new version of rscopus package
* Minor changes in argument list of histNetwork() and histPlot()
* Improved plot layout and colors in networkPlot()
* Solved the issue regarding the older versions of utils::promptPackage()
* Solved the issue regarding missing vertex labels in VOSviewer visualization 


bibliometrix v1.9.3 (Release date: 2018-05-15)
==============

New functionality:
* Added the possibility to import collections from COCHRANE Database in convert2df()
* Completely rewritten conceptualStructure() function. 
  Now it performs CA and MCA methods and plots factorial maps of documents.
* Added binary argument in cocMatrix(). It allows to build up a document x attribute matrix using both binary and full counting


bibliometrix v1.9.2 (Release date: 2018-03-28)
==============

Changes:
* Solved the issue https://stat.ethz.ch/pipermail/r-devel/2018-March/075737.html regarding the package Matrix


bibliometrix v1.9.1 (Release date: 2018-03-14)
==============

Changes:
* Minor fixes in several functions


bibliometrix v1.9 (Release date: 2018-01-20)
==============

New functionality:
* added the possibility to import collections from MedLine/PubMed in convert2df()
* added co-occurrence normalization in networkPlot()
Changes:
* improved speed and efficiency in citation analysis (histNetwork(), localCitations(), metaTagExtraction())
* improved function histNetwork()
* improved function metaTagExtraction()


bibliometrix v1.8 (Release date: 2017-08-30)
==============

Changes:
* improved historiograph algorithm in histNetwork()
* improved network layout in histPlot()
* added new SR field in the bibliographic data frame
* improved importing functions


bibliometrix v1.7 (Release date: 2017-07-07)
==============

New functionality:
* added new clustering algorithms into networkPlot()
* added argument 'halo' in networkPlot() to color cluster groups
* added function normalizeSimilarity() to calculate some probabilistic and set-theoretic similarity measures
* added function thematicMap() to create a thematic map based on co-word network analysis and clustering
* added function thematicEvolution() to create an Evolution thematic map based on co-word network analysis and clustering. 

Changes:
* removed function couplingSimilarity()

bibliometrix v1.6 (Release date: 2017-05-09)
==============

New functionality:

* added two datasets isiCollection and scopusCollection
* mergeDbSources() merges bibliographic data frames from SCOPUS and ISI WOS
* duplicatedMatching() searches duplicated records in a bibliographic database using the generalized Levenshtein (edit) distance
* dfMerge() removed from package
* removed dipendence from dplyr package
* added possibility to merge synonyms using termExtraction()
 
Changes:
* Minor fixes in several functions
* Minor corrections in vignettes and documentation


bibliometrix v1.5 (Release date: 2017-03-15)
==============

New functionality:

* retrievalByAuthorID() gets Author Content on SCOPUS by ID
* idByAuthor() gets Complete Author Information and ID from Scopus
* Hindex() introduced the argument years
 
Changes:
* Minor fixes in several functions
* Minor corrections in vignettes and documentation

bibliometrix v1.4 (Release date: 2017-01-24)
==============

New functionality:

* conceptualStructure() creates and plots conceptual structure map of a scientific field
* histPlot() plots historical co-citation network
* networkPlot() plots Bibliographic networks
* readFiles() loads a sequence of ISI or SCOPUS Export files into a large character object
 
Changes:
* Minor fixes in metaTagExtraction() function
* Minor fixes in convert2df() function
* Minor corrections in vignettes and documentation


bibliometrix v1.3 (Release date: 2016-11-17)
==============

New functionality:

* termExtraction() extracts terms from a textual field (abstract, title, author's keywords, etc.) of a bibliographic data frame. 
Now it integrates Porter's stemming algorithm and term extraction procedure for English, French, German, Italian and Spanish textual contents.


bibliometrix v1.2 (Release date: 2016-10-18)
==============

New functionality:
* keywordAssoc() associates authors' keywords to keywords plus
* keywordGrowth() calculates yearly cumulative occurrences of top keywords.
* termExtraction() extracts terms from a textual field (abstract, title, author's keywords, etc.) of a bibliographic data frame.

Changes:
* New statistics in summary() function
* Minor fixes in convert2df() function
* Minor corrections in vignettes and documentation


bibliometrix v1.1 (Release date: 2016-09-07)
==============

Changes:
* Minor fixes in convert2df() function
* Minor corrections in vignettes and documentation


bibliometrix v1.0 (Release date: 2016-06-28)
==============

New functionality:
* histNetwork() creates a historical co-citation network from a bibliographic data frame.

Changes:
* Minor fixes in cocMatrix() function
* Minor corrections in vignettes and documentation


bibliometrix v0.8 (Release date: 2016-06-10)
==============

New functionality:
* Hindex() calculates the authors' h-index and its variants (g-index, m-index) in a a bibliographic collection.

New data:
* garfield dataset

Changes:
* Minor corrections in vignettes and documentation


bibliometrix v0.7 (Release date: 2016-06-04)
==============

New functionality:
* timeslice() calculates Jaccard and Salton similarity indices among manuscripts of a coupling network
* dfMerge() merges two bibliographic data frames deleting duplicate manuscripts.
* metaTagExtraction() now extracts also university of affiliation for each co-author

Changes:
* Minor fixes in metaTagExtraction() function 
* Minor corrections in vignettes and documentation


bibliometrix v0.6 (Release date: 2016-05-30)
==============

New functionality:
* couplingSimilarity() calculates Jaccard and Salton similarity indices among manuscripts of a coupling network

Changes:
* Minor corrections in vignettes and documentation


bibliometrix v0.5 (Release date: 2016-05-25)
==============

New functionality:
* localCitations() generates the frequency table of the most cited local authors.

Changes:
* Minor fixes in citations() function
* Minor corrections in vignettes and documentation


bibliometrix v0.4 (Release date: 2016-05-23)
==============

Changes:
* Minor fixes in citations() function


bibliometrix v0.3 (Release date: 2016-05-21)
==============

Changes:
* Minor corrections in vignettes and documentation


bibliometrix v0.2 (Release date: 2016-05-19)
==============

Changes:
* Additional vignettes and documentation

New functionality:
* citations() generates the frequency table of the most cited references or the most cited first authors (of references).
* lotka() now performs Kolmogorov-Smirnoff two-sample test among observed and theoretical Lotka's distributions.

Bug Fixes:
* Minor fixes in importing routines and summary function.
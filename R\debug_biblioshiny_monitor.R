# debug_biblioshiny_monitor.R
# 监控 Biblioshiny 导入RDS并去重的全过程

# 加载必要的包
library(bibliometrix)
library(here)
library(dplyr)
library(stringr)

# 创建日志目录
log_dir <- here("data_repository", "05_execution_logs", "bibliometrix_logs")
if (!dir.exists(log_dir)) {
  dir.create(log_dir, recursive = TRUE)
}

# 设置日志文件
log_file <- file.path(log_dir, "biblioshiny_monitor.log")
log_con <- file(log_file, "w")

# 日志函数
log_message <- function(msg, type = "INFO") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, type, msg)
  message(formatted_msg)
  if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) { # 检查连接是否存在且打开
    cat(formatted_msg, "\n", file = log_con)
  }
}

# 监控函数调用
monitor_function <- function(fun_name) {
  # 构建tracer和exit表达式，注入fun_name
  tracer_expr <- bquote({
    # 进入函数时的处理
    log_message(sprintf("开始调用函数: %s", .(fun_name)))
    # 记录参数信息
    args_list <- as.list(sys.call())[-1] # 获取所有参数，去除函数名
    if (length(args_list) > 0) {
      log_message(sprintf("函数参数数量: %d", length(args_list)))
      for (i in seq_along(args_list)) {
        arg_name <- names(args_list)[i]
        if (is.null(arg_name)) arg_name <- paste0("arg", i)
        log_message(sprintf("参数 %s: %s", arg_name, deparse(args_list[[i]])))
      }
    }
  })

  exit_expr <- bquote({
    # 退出函数时的处理
    result <- returnValue()
    if (is.data.frame(result)) {
      log_message(sprintf("函数 %s 执行完成", .(fun_name)))
      log_message(sprintf("返回数据框: %d x %d", nrow(result), ncol(result)))
    } else {
      log_message(sprintf("函数 %s 执行完成", .(fun_name)))
    }
  })

  # 使用trace设置监控
  trace(fun_name, 
        tracer = tracer_expr,
        exit = exit_expr,
        where = asNamespace("bibliometrix"),
        print = FALSE)
}

# 设置监控
log_message("开始设置监控...")

# 监控关键函数
monitor_function("convert2df")
monitor_function("duplicatedMatching")
monitor_function("metaTagExtraction")

# 启动 Biblioshiny
log_message("启动 Biblioshiny，请在网页界面导入RDS文件并操作...")
biblioshiny()

# 清理监控
log_message("清理监控设置...")
untrace("convert2df", where = asNamespace("bibliometrix"))
untrace("duplicatedMatching", where = asNamespace("bibliometrix"))
untrace("metaTagExtraction", where = asNamespace("bibliometrix"))

# 确保监控结束消息写入后才关闭连接
log_message("监控结束") 
if (exists("log_con") && isOpen(log_con)) {
  close(log_con)
} 
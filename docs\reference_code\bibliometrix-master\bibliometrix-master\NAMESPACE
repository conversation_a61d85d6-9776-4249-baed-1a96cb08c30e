# Generated by roxygen2: do not edit by hand

S3method(plot,bibliodendrogram)
S3method(plot,bibliometrix)
S3method(summary,bibliometrix)
S3method(summary,bibliometrix_netstat)
export(Hindex)
export(KeywordGrowth)
export(authorProdOverTime)
export(biblioAnalysis)
export(biblioNetwork)
export(biblioshiny)
export(bradford)
export(citations)
export(cocMatrix)
export(collabByRegionPlot)
export(conceptualStructure)
export(convert2df)
export(couplingMap)
export(dominance)
export(duplicatedMatching)
export(fieldByYear)
export(histNetwork)
export(histPlot)
export(idByAuthor)
export(keywordAssoc)
export(localCitations)
export(lotka)
export(mergeDbSources)
export(metaTagExtraction)
export(missingData)
export(net2Pajek)
export(net2VOSviewer)
export(networkPlot)
export(networkStat)
export(normalizeCitationScore)
export(normalizeSimilarity)
export(plotThematicEvolution)
export(readFiles)
export(retrievalByAuthorID)
export(rpys)
export(sourceGrowth)
export(splitCommunities)
export(tableTag)
export(termExtraction)
export(thematicEvolution)
export(thematicMap)
export(threeFieldsPlot)
export(timeslice)
export(trim)
export(trim.leading)
export(trimES)
import(bibliometrixData)
import(ca)
import(dimensionsR)
import(forcats)
import(ggplot2)
import(ggrepel)
import(openalexR)
import(pubmedR)
import(readr)
import(readxl)
import(shiny)
import(stats)
import(stringi)
import(tidytext)
importFrom(DT,DTOutput)
importFrom(DT,datatable)
importFrom(DT,renderDT)
importFrom(Matrix,"%&%")
importFrom(Matrix,"diag<-")
importFrom(Matrix,Arith)
importFrom(Matrix,Cholesky)
importFrom(Matrix,Compare)
importFrom(Matrix,Diagonal)
importFrom(Matrix,Logic)
importFrom(Matrix,Math)
importFrom(Matrix,Math2)
importFrom(Matrix,Matrix)
importFrom(Matrix,MatrixClass)
importFrom(Matrix,Ops)
importFrom(Matrix,Summary)
importFrom(Matrix,abIseq)
importFrom(Matrix,abIseq1)
importFrom(Matrix,all.equal)
importFrom(Matrix,anyDuplicatedT)
importFrom(Matrix,as.array)
importFrom(Matrix,as.matrix)
importFrom(Matrix,band)
importFrom(Matrix,bandSparse)
importFrom(Matrix,bdiag)
importFrom(Matrix,cbind2)
importFrom(Matrix,chol)
importFrom(Matrix,chol2inv)
importFrom(Matrix,coerce)
importFrom(Matrix,colMeans)
importFrom(Matrix,colSums)
importFrom(Matrix,condest)
importFrom(Matrix,cov2cor)
importFrom(Matrix,crossprod)
importFrom(Matrix,det)
importFrom(Matrix,determinant)
importFrom(Matrix,diag)
importFrom(Matrix,diagN2U)
importFrom(Matrix,diagU2N)
importFrom(Matrix,diff)
importFrom(Matrix,drop)
importFrom(Matrix,drop0)
importFrom(Matrix,expand)
importFrom(Matrix,expm)
importFrom(Matrix,fac2sparse)
importFrom(Matrix,forceSymmetric)
importFrom(Matrix,format)
importFrom(Matrix,formatSpMatrix)
importFrom(Matrix,formatSparseM)
importFrom(Matrix,head)
importFrom(Matrix,image)
importFrom(Matrix,invPerm)
importFrom(Matrix,is.null.DN)
importFrom(Matrix,isDiagonal)
importFrom(Matrix,isLDL)
importFrom(Matrix,isSymmetric)
importFrom(Matrix,isTriangular)
importFrom(Matrix,kronecker)
importFrom(Matrix,lu)
importFrom(Matrix,mean)
importFrom(Matrix,nnzero)
importFrom(Matrix,norm)
importFrom(Matrix,onenormest)
importFrom(Matrix,pack)
importFrom(Matrix,print)
importFrom(Matrix,printSpMatrix)
importFrom(Matrix,printSpMatrix2)
importFrom(Matrix,qr)
importFrom(Matrix,qr.Q)
importFrom(Matrix,qr.R)
importFrom(Matrix,qr.coef)
importFrom(Matrix,qr.fitted)
importFrom(Matrix,qr.qty)
importFrom(Matrix,qr.qy)
importFrom(Matrix,qr.resid)
importFrom(Matrix,qrR)
importFrom(Matrix,rankMatrix)
importFrom(Matrix,rbind2)
importFrom(Matrix,rcond)
importFrom(Matrix,readHB)
importFrom(Matrix,readMM)
importFrom(Matrix,rep2abI)
importFrom(Matrix,rowMeans)
importFrom(Matrix,rowSums)
importFrom(Matrix,rsparsematrix)
importFrom(Matrix,show)
importFrom(Matrix,skewpart)
importFrom(Matrix,solve)
importFrom(Matrix,spMatrix)
importFrom(Matrix,sparse.model.matrix)
importFrom(Matrix,sparseMatrix)
importFrom(Matrix,sparseVector)
importFrom(Matrix,summary)
importFrom(Matrix,symmpart)
importFrom(Matrix,t)
importFrom(Matrix,tail)
importFrom(Matrix,tcrossprod)
importFrom(Matrix,tril)
importFrom(Matrix,triu)
importFrom(Matrix,uniqTsparse)
importFrom(Matrix,unname)
importFrom(Matrix,unpack)
importFrom(Matrix,update)
importFrom(Matrix,updown)
importFrom(Matrix,which)
importFrom(Matrix,writeMM)
importFrom(SnowballC,getStemLanguages)
importFrom(SnowballC,wordStem)
importFrom(dplyr,"%>%")
importFrom(dplyr,across)
importFrom(dplyr,anti_join)
importFrom(dplyr,any_of)
importFrom(dplyr,arrange)
importFrom(dplyr,as_tibble)
importFrom(dplyr,between)
importFrom(dplyr,bind_cols)
importFrom(dplyr,bind_rows)
importFrom(dplyr,count)
importFrom(dplyr,cummean)
importFrom(dplyr,desc)
importFrom(dplyr,distinct)
importFrom(dplyr,do)
importFrom(dplyr,group_by)
importFrom(dplyr,if_all)
importFrom(dplyr,inner_join)
importFrom(dplyr,join_by)
importFrom(dplyr,left_join)
importFrom(dplyr,mutate)
importFrom(dplyr,mutate_all)
importFrom(dplyr,mutate_at)
importFrom(dplyr,mutate_if)
importFrom(dplyr,n)
importFrom(dplyr,reframe)
importFrom(dplyr,relocate)
importFrom(dplyr,rename)
importFrom(dplyr,rename_with)
importFrom(dplyr,right_join)
importFrom(dplyr,row_number)
importFrom(dplyr,rowwise)
importFrom(dplyr,select)
importFrom(dplyr,slice)
importFrom(dplyr,slice_head)
importFrom(dplyr,slice_max)
importFrom(dplyr,slice_tail)
importFrom(dplyr,summarise)
importFrom(dplyr,summarize)
importFrom(dplyr,tibble)
importFrom(dplyr,top_n)
importFrom(dplyr,ungroup)
importFrom(grDevices,adjustcolor)
importFrom(grDevices,as.raster)
importFrom(grDevices,chull)
importFrom(grDevices,colorRampPalette)
importFrom(grDevices,dev.off)
importFrom(grDevices,heat.colors)
importFrom(grDevices,pdf)
importFrom(graphics,abline)
importFrom(graphics,barplot)
importFrom(graphics,legend)
importFrom(graphics,lines)
importFrom(graphics,par)
importFrom(graphics,plot)
importFrom(igraph,"E<-")
importFrom(igraph,"V<-")
importFrom(igraph,E)
importFrom(igraph,V)
importFrom(igraph,arpack_defaults)
importFrom(igraph,as_adjacency_matrix)
importFrom(igraph,as_long_data_frame)
importFrom(igraph,authority_score)
importFrom(igraph,betweenness)
importFrom(igraph,centr_betw)
importFrom(igraph,centr_clo)
importFrom(igraph,centr_degree)
importFrom(igraph,centr_eigen)
importFrom(igraph,closeness)
importFrom(igraph,cluster_edge_betweenness)
importFrom(igraph,cluster_fast_greedy)
importFrom(igraph,cluster_infomap)
importFrom(igraph,cluster_leading_eigen)
importFrom(igraph,cluster_leiden)
importFrom(igraph,cluster_louvain)
importFrom(igraph,cluster_optimal)
importFrom(igraph,cluster_spinglass)
importFrom(igraph,cluster_walktrap)
importFrom(igraph,count_multiple)
importFrom(igraph,decompose.graph)
importFrom(igraph,degree)
importFrom(igraph,degree_distribution)
importFrom(igraph,delete.edges)
importFrom(igraph,delete.vertices)
importFrom(igraph,diameter)
importFrom(igraph,edge_density)
importFrom(igraph,eigen_centrality)
importFrom(igraph,get.edgelist)
importFrom(igraph,graph.adjacency)
importFrom(igraph,graph_attr)
importFrom(igraph,graph_from_adjacency_matrix)
importFrom(igraph,graph_from_data_frame)
importFrom(igraph,graph_from_incidence_matrix)
importFrom(igraph,hub_score)
importFrom(igraph,induced_subgraph)
importFrom(igraph,layout.auto)
importFrom(igraph,layout.circle)
importFrom(igraph,layout.fruchterman.reingold)
importFrom(igraph,layout.kamada.kawai)
importFrom(igraph,layout.mds)
importFrom(igraph,layout.norm)
importFrom(igraph,layout.sphere)
importFrom(igraph,layout.star)
importFrom(igraph,mean_distance)
importFrom(igraph,membership)
importFrom(igraph,page.rank)
importFrom(igraph,page_rank)
importFrom(igraph,plot.igraph)
importFrom(igraph,simplify)
importFrom(igraph,transitivity)
importFrom(igraph,vcount)
importFrom(igraph,write.graph)
importFrom(openxlsx,write.xlsx)
importFrom(plotly,add_annotations)
importFrom(plotly,add_lines)
importFrom(plotly,config)
importFrom(plotly,ggplotly)
importFrom(plotly,layout)
importFrom(plotly,plot_ly)
importFrom(plotly,subplot)
importFrom(plotly,toRGB)
importFrom(purrr,map2_dfr)
importFrom(purrr,map_chr)
importFrom(purrr,map_df)
importFrom(purrr,map_dfr)
importFrom(rscopus,affiliation_retrieval)
importFrom(rscopus,author_df_orig)
importFrom(rscopus,author_search)
importFrom(rscopus,get_complete_author_info)
importFrom(stringdist,stringdistmatrix)
importFrom(stringr,str_extract_all)
importFrom(stringr,str_locate_all)
importFrom(stringr,str_replace_all)
importFrom(tidyr,drop_na)
importFrom(tidyr,gather)
importFrom(tidyr,pivot_longer)
importFrom(tidyr,pivot_wider)
importFrom(tidyr,replace_na)
importFrom(tidyr,separate)
importFrom(tidyr,spread)
importFrom(tidyr,starts_with)
importFrom(tidyr,unite)
importFrom(tidyr,unnest)
importFrom(utils,adist)
importFrom(utils,capture.output)
importFrom(utils,data)
importFrom(utils,read.csv)
importFrom(utils,write.csv)

# R目录结构优化脚本
# 基于用户手动修改的结构进行进一步优化

cat("=== R目录结构优化 ===\n")
cat("分析当前结构并提供优化建议...\n\n")

# 分析当前结构
analyze_current_structure <- function() {
  cat("📊 当前R目录结构分析:\n\n")
  
  # 检查根目录核心脚本
  core_scripts <- c(
    "01_data_import_wos.R",
    "02_data_import_citespace.R", 
    "03_data_import_vosviewer.R",
    "04_data_validation.R",
    "05_deduplication_enhanced.R",  # 缺失
    "06_doi_completion.R",
    "07_data_enhancement.R",
    "08_data_integration.R",
    "09_quality_control.R"          # 缺失
  )
  
  cat("✅ 核心处理流程 (根目录):\n")
  for (script in core_scripts) {
    if (file.exists(file.path("R", script))) {
      cat(sprintf("  ✅ %s\n", script))
    } else {
      cat(sprintf("  ❌ %s (缺失)\n", script))
    }
  }
  
  # 检查专业化目录
  specialized_dirs <- c("bibliometrix", "biblioshiny", "enhanced", "automation", "utils")
  
  cat("\n✅ 专业化分类目录:\n")
  for (dir_name in specialized_dirs) {
    dir_path <- file.path("R", dir_name)
    if (dir.exists(dir_path)) {
      files <- list.files(dir_path, pattern = "\\.R$")
      cat(sprintf("  📁 %s/ (%d个文件)\n", dir_name, length(files)))
      for (file in files) {
        cat(sprintf("    - %s\n", file))
      }
    }
  }
  
  # 检查根目录散落的文件
  root_files <- list.files("R", pattern = "\\.R$", full.names = FALSE)
  scattered_files <- setdiff(root_files, core_scripts)
  scattered_files <- scattered_files[!grepl("^(config|OPTIMIZE_STRUCTURE)", scattered_files)]
  
  if (length(scattered_files) > 0) {
    cat("\n⚠️  根目录散落的文件:\n")
    for (file in scattered_files) {
      cat(sprintf("  - %s\n", file))
    }
  }
}

# 优化建议
provide_optimization_suggestions <- function() {
  cat("\n🔧 优化建议:\n\n")
  
  cat("1. 补充缺失的核心脚本:\n")
  
  # 检查并建议复制05脚本
  if (!file.exists("R/05_deduplication_enhanced.R")) {
    if (file.exists("R/enhanced/05_deduplication_enhanced.R")) {
      cat("  📋 从 enhanced/ 复制 05_deduplication_enhanced.R 到根目录\n")
    } else {
      cat("  📋 需要创建 05_deduplication_enhanced.R\n")
    }
  }
  
  # 检查并建议复制09脚本
  if (!file.exists("R/09_quality_control.R")) {
    if (file.exists("R/automation/09_quality_control.R")) {
      cat("  📋 从 automation/ 复制 09_quality_control.R 到根目录\n")
    } else {
      cat("  📋 需要创建 09_quality_control.R\n")
    }
  }
  
  cat("\n2. 进一步分类建议:\n")
  cat("  📁 创建 doi_tools/ 目录，整理DOI相关工具\n")
  cat("  📁 创建 debug/ 目录，整理调试工具\n")
  cat("  📁 创建 reports/ 目录，整理报告生成工具\n")
}

# 执行优化操作
execute_optimization <- function() {
  cat("\n🚀 执行优化操作:\n\n")
  
  # 1. 复制缺失的核心脚本
  if (!file.exists("R/05_deduplication_enhanced.R") && 
      file.exists("R/enhanced/05_deduplication_enhanced.R")) {
    file.copy("R/enhanced/05_deduplication_enhanced.R", "R/05_deduplication_enhanced.R")
    cat("✅ 已复制 05_deduplication_enhanced.R 到根目录\n")
  }
  
  if (!file.exists("R/09_quality_control.R") && 
      file.exists("R/automation/09_quality_control.R")) {
    file.copy("R/automation/09_quality_control.R", "R/09_quality_control.R")
    cat("✅ 已复制 09_quality_control.R 到根目录\n")
  }
  
  # 2. 创建新的分类目录
  new_dirs <- c("doi_tools", "debug", "reports")
  for (dir_name in new_dirs) {
    dir_path <- file.path("R", dir_name)
    if (!dir.exists(dir_path)) {
      dir.create(dir_path)
      cat(sprintf("✅ 已创建目录: %s/\n", dir_name))
    }
  }
  
  # 3. 移动DOI相关工具
  doi_files <- c(
    "complete_doi_by_ut.R",
    "complete_doi_resume.R", 
    "crossref_doi_lookup.R",
    "doi_completion_final.R"
  )
  
  for (file in doi_files) {
    src_path <- file.path("R", file)
    dst_path <- file.path("R", "doi_tools", file)
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      file.remove(src_path)
      cat(sprintf("✅ 已移动 %s 到 doi_tools/\n", file))
    }
  }
  
  # 4. 移动调试工具
  debug_files <- c("debug_deduplication_records.R")
  for (file in debug_files) {
    src_path <- file.path("R", file)
    dst_path <- file.path("R", "debug", file)
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      file.remove(src_path)
      cat(sprintf("✅ 已移动 %s 到 debug/\n", file))
    }
  }
  
  # 5. 移动报告工具
  report_files <- c(
    "auto_final_report_generator.R",
    "FINAL_AUTO_SUMMARY.R"
  )
  
  for (file in report_files) {
    src_path <- file.path("R", file)
    dst_path <- file.path("R", "reports", file)
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      file.remove(src_path)
      cat(sprintf("✅ 已移动 %s 到 reports/\n", file))
    }
  }
  
  # 6. 移动清理和管理工具到管理目录
  if (!dir.exists("R/management")) {
    dir.create("R/management")
    cat("✅ 已创建目录: management/\n")
  }
  
  management_files <- c(
    "PROJECT_CLEANUP_DOI.R",
    "PROJECT_REORGANIZATION.R",
    "COMPLETE_RECOVERY_ALL_FILES.R",
    "EMERGENCY_RECOVERY.R",
    "FINAL_CLEANUP.R",
    "RECOVER_MISSING_FILES.R"
  )
  
  for (file in management_files) {
    src_path <- file.path("R", file)
    dst_path <- file.path("R", "management", file)
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      file.remove(src_path)
      cat(sprintf("✅ 已移动 %s 到 management/\n", file))
    }
  }
}

# 生成最终结构报告
generate_final_report <- function() {
  cat("\n📋 优化后的R目录结构:\n\n")
  
  cat("🎯 核心处理流程 (根目录):\n")
  core_scripts <- c(
    "01_data_enhancement_framework_fixed.R",
    "01_data_import_wos.R",
    "02_data_import_citespace.R", 
    "03_data_import_vosviewer.R",
    "04_data_validation.R",
    "05_deduplication_enhanced.R",
    "06_doi_completion.R",
    "07_data_enhancement.R",
    "08_data_integration.R",
    "09_quality_control.R",
    "config.R"
  )
  
  for (script in core_scripts) {
    if (file.exists(file.path("R", script))) {
      cat(sprintf("  ✅ %s\n", script))
    }
  }
  
  cat("\n📁 专业化分类目录:\n")
  specialized_dirs <- c("bibliometrix", "biblioshiny", "enhanced", "automation", 
                       "doi_tools", "debug", "reports", "management", "utils")
  
  for (dir_name in specialized_dirs) {
    dir_path <- file.path("R", dir_name)
    if (dir.exists(dir_path)) {
      files <- list.files(dir_path, pattern = "\\.R$")
      cat(sprintf("  📁 %s/ (%d个文件)\n", dir_name, length(files)))
    }
  }
  
  cat("\n🎉 优化完成！R目录结构现在更加清晰和专业化。\n")
}

# 主执行函数
main_optimization <- function() {
  analyze_current_structure()
  provide_optimization_suggestions()
  
  cat("\n❓ 是否执行优化操作？(自动执行)\n")
  execute_optimization()
  generate_final_report()
}

# 执行优化
main_optimization()

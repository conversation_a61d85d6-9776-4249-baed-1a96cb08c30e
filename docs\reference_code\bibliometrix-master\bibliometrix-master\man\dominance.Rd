% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/dominance.R
\name{dominance}
\alias{dominance}
\title{Authors' dominance ranking}
\usage{
dominance(results, k = 10)
}
\arguments{
\item{results}{is an object of the class '\code{bibliometrix}' for which the analysis of the authors' dominance ranking is desired.}

\item{k}{is an integer, used for table formatting (number of authors). Default value is 10.}
}
\value{
The function \code{dominance} returns a data frame with cases corresponding to the first \code{k} most productive authors and variables to typical field of a dominance analysis.

the data frame variables are:
\tabular{lll}{
\code{Author} \tab   \tab Author's name\cr
\code{Dominance Factor}  \tab   \tab Dominance Factor (DF = FAA / MAA)\cr
\code{Tot Articles}   \tab   \tab N. of Authored Articles (TAA)\cr
\code{Single Authored}   \tab   \tab N. of Single-Authored Articles (SAA)\cr
\code{Multi Authored}   \tab   \tab N. of Multi-Authored Articles (MAA=TAA-SAA)\cr
\code{First Authored} \tab   \tab N. of First Authored Articles (FAA)\cr
\code{Rank by Articles}    \tab   \tab Author Ranking by N. of Articles\cr
\code{Rank by DF}    \tab   \tab Author Ranking by Dominance Factor}
}
\description{
It calculates the authors' dominance ranking from an object of the class '\code{bibliometrix}' as proposed by Kumar & Kumar, 2008.
}
\examples{
data(scientometrics, package = "bibliometrixData")
results <- biblioAnalysis(scientometrics)
DF=dominance(results)
DF

}
\seealso{
\code{\link{biblioAnalysis}} function for bibliometric analysis

\code{\link{summary}} method for class '\code{bibliometrix}'
}

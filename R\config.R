# 文献计量分析项目配置文件
# 统一管理项目路径、参数和设置

PROJECT_CONFIG <- list(
  # 项目信息
  project = list(
    name = "Bibliometric Analysis",
    version = "2.0.0",
    description = "文献计量分析系统"
  ),
  
  # 路径配置
  paths = list(
    raw_data = "data_repository/01_raw_data",
    baseline_data = "data_repository/02_baseline_data", 
    enhanced_data = "data_repository/03_enhanced_data",
    analysis_outputs = "data_repository/04_analysis_outputs",
    reports = "data_repository/05_reports",
    cache = "data_repository/06_cache",
    logs = "data_repository/07_logs"
  ),
  
  # 文件命名规范
  naming = list(
    raw_prefix = "dataa_",      # 原始数据前缀
    baseline_prefix = "datay_", # 基线数据前缀
    enhanced_prefix = "datax_"  # 增强数据前缀
  ),

  # 脚本执行顺序
  execution_order = list(
    data_processing = c(
      "01_data_import_wos.R",           # WoS数据导入与转换
      "02_data_import_citespace.R",     # CiteSpace数据处理
      "03_data_import_vosviewer.R",     # VOSviewer数据处理
      "04_data_validation.R",           # 数据验证与质量检查
      "05_deduplication_enhanced.R",    # 增强去重处理
      "06_doi_completion.R",            # DOI补全系统
      "07_data_enhancement.R",          # 数据增强处理
      "08_data_integration.R",          # 数据整合
      "09_quality_control.R"            # 质量控制与报告
    ),
    analysis = c(
      # 分析脚本将在后续阶段添加
      # "10_descriptive_analysis.R",
      # "11_network_analysis.R",
      # "12_trend_analysis.R"
    ),
    visualization = c(
      # 可视化脚本将在后续阶段添加
      # "20_network_visualization.R",
      # "21_trend_visualization.R"
    ),
    reporting = c(
      # 报告生成脚本将在后续阶段添加
      # "30_processing_report.R",
      # "31_quality_report.R"
    )
  ),
  
  # 处理参数
  processing = list(
    # 去重参数
    deduplication = list(
      title_tolerances = c(0.98, 0.95, 0.90),
      author_tolerance = 0.95,
      abstract_tolerance = 0.90
    ),
    
    # DOI补全参数
    doi_completion = list(
      title_threshold = 0.75,
      journal_threshold = 0.4,
      year_threshold = 0.5,
      subject_threshold = 0.8,
      final_threshold = 0.65,
      api_delay = 1.0,
      batch_size = 100
    ),
    
    # 数据验证参数
    validation = list(
      min_records = 1,
      required_fields = c("UT", "TI", "PY", "AU"),
      year_range = c(1900, as.numeric(format(Sys.Date(), "%Y")))
    )
  ),
  
  # 分析参数
  analysis = list(
    network = list(
      min_degree = 2,
      layout_algorithm = "fruchterman_reingold"
    ),
    
    trends = list(
      time_window = 5,
      smoothing_method = "loess"
    ),
    
    collaboration = list(
      min_coauthors = 2,
      country_analysis = TRUE
    )
  ),
  
  # 可视化参数
  visualization = list(
    theme = "minimal",
    color_palette = "viridis",
    figure_width = 12,
    figure_height = 8,
    dpi = 300
  ),
  
  # 输出格式
  output = list(
    data_format = "rds",
    report_format = c("html", "pdf"),
    figure_format = c("png", "svg")
  )
)

# 获取配置函数
get_config <- function(section = NULL, key = NULL) {
  if (is.null(section)) {
    return(PROJECT_CONFIG)
  }
  
  if (is.null(key)) {
    return(PROJECT_CONFIG[[section]])
  }
  
  return(PROJECT_CONFIG[[section]][[key]])
}

# 获取路径函数
get_path <- function(path_name) {
  return(PROJECT_CONFIG$paths[[path_name]])
}

# 获取文件名函数
get_filename <- function(prefix, name, extension = "rds") {
  return(paste0(prefix, name, ".", extension))
}

# 获取执行顺序函数
get_execution_order <- function(stage = "data_processing") {
  return(PROJECT_CONFIG$execution_order[[stage]])
}

# 批量执行脚本函数
execute_pipeline <- function(stage = "data_processing", skip_existing = TRUE) {
  scripts <- get_execution_order(stage)

  cat(sprintf("开始执行%s阶段流程...\n", stage))

  for (script in scripts) {
    script_path <- file.path("R", script)

    if (file.exists(script_path)) {
      cat(sprintf("执行: %s\n", script))

      tryCatch({
        source(script_path)
        cat(sprintf("✅ %s 执行完成\n", script))
      }, error = function(e) {
        cat(sprintf("❌ %s 执行失败: %s\n", script, e$message))
      })
    } else {
      cat(sprintf("⚠️  脚本不存在: %s\n", script))
    }
  }

  cat(sprintf("%s阶段流程执行完成\n", stage))
}

cat("✅ 项目配置已加载 (v2.1.0)\n")


# === 重新整理后的执行顺序 ===
  # 脚本执行顺序 (重新整理后)
  execution_order = list(
    data_processing = c(
      "01_import_wos_data.R",           # WoS数据导入
      "02_import_citespace_data.R",     # CiteSpace数据导入
      "03_import_vosviewer_data.R",     # VOSviewer数据导入
      "04_validate_and_clean_data.R",   # 数据验证与清理
      "05_deduplicate_records.R",       # 去重处理
      "07_enhance_data_comprehensive.R", # 数据增强
      "08_complete_missing_dois.R",     # DOI补全
      "09_integrate_enhanced_data.R",   # 数据整合
      "10_quality_control_and_report.R" # 质量控制
    ),
    advanced_options = c(
      "06_deduplicate_advanced.R"       # 高级去重 (可选)
    )
  ),

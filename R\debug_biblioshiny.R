# debug_biblioshiny.R
# 用于调试 Biblioshiny 的文件合并和去重过程

# 加载必要的包
library(bibliometrix)
library(here)
library(dplyr)

# 创建环境监控函数
monitor_environment <- function() {
  # 记录初始环境状态
  initial_env <- ls(envir = .GlobalEnv)
  initial_objects <- lapply(initial_env, function(x) {
    obj <- get(x, envir = .GlobalEnv)
    list(
      name = x,
      class = class(obj),
      size = if(is.data.frame(obj)) nrow(obj) else length(obj)
    )
  })
  
  # 记录已报告的变化
  reported_changes <- list()
  
  # 返回监控函数
  function() {
    current_env <- ls(envir = .GlobalEnv)
    current_objects <- lapply(current_env, function(x) {
      obj <- get(x, envir = .GlobalEnv)
      list(
        name = x,
        class = class(obj),
        size = if(is.data.frame(obj)) nrow(obj) else length(obj)
      )
    })
    
    # 检测变化
    changes <- list(
      added = setdiff(current_env, initial_env),
      removed = setdiff(initial_env, current_env),
      modified = intersect(current_env, initial_env)
    )
    
    # 检查修改的对象
    for(obj_name in changes$modified) {
      old_obj <- initial_objects[[which(sapply(initial_objects, function(x) x$name == obj_name))]]
      new_obj <- current_objects[[which(sapply(current_objects, function(x) x$name == obj_name))]]
      
      if(!identical(old_obj, new_obj)) {
        change_key <- sprintf("%s_%s_%s", obj_name, 
                            paste(old_obj$class, collapse=","),
                            paste(new_obj$class, collapse=","))
        
        if(!(change_key %in% reported_changes)) {
          message(sprintf("对象 '%s' 发生变化:", obj_name))
          message(sprintf("  旧状态: 类型=%s, 大小=%s", 
                         paste(old_obj$class, collapse=","), 
                         old_obj$size))
          message(sprintf("  新状态: 类型=%s, 大小=%s", 
                         paste(new_obj$class, collapse=","), 
                         new_obj$size))
          reported_changes <<- c(reported_changes, change_key)
        }
      }
    }
    
    # 报告新增对象
    for(obj_name in changes$added) {
      if(!(obj_name %in% reported_changes)) {
        obj <- current_objects[[which(sapply(current_objects, function(x) x$name == obj_name))]]
        message(sprintf("新增对象: %s (类型=%s, 大小=%s)", 
                       obj_name, 
                       paste(obj$class, collapse=","), 
                       obj$size))
        reported_changes <<- c(reported_changes, obj_name)
      }
    }
    
    # 报告删除对象
    for(obj_name in changes$removed) {
      if(!(obj_name %in% reported_changes)) {
        message(sprintf("删除对象: %s", obj_name))
        reported_changes <<- c(reported_changes, obj_name)
      }
    }
  }
}

# 创建函数调用监控
monitor_function_calls <- function() {
  # 记录函数调用
  calls <- list()
  reported_calls <- character()
  
  # 返回监控函数
  function(fun_name, args) {
    # 生成调用标识
    call_id <- sprintf("%s_%s", fun_name, 
                      paste(sapply(args, function(x) {
                        if(is.data.frame(x)) sprintf("df_%dx%d", nrow(x), ncol(x))
                        else if(is.vector(x)) sprintf("vec_%d", length(x))
                        else class(x)[1]
                      }), collapse="_"))
    
    # 只报告新的调用
    if(!(call_id %in% reported_calls)) {
      call_info <- list(
        time = Sys.time(),
        fun_name = fun_name,
        arguments = args
      )
      calls <<- c(calls, list(call_info))
      reported_calls <<- c(reported_calls, call_id)
      
      # 输出调用信息
      message(sprintf("\n函数调用: %s", fun_name))
      message(sprintf("时间: %s", format(call_info$time)))
      message("参数:")
      for(arg_name in names(args)) {
        message(sprintf("  %s: %s", arg_name, 
                       if(is.data.frame(args[[arg_name]])) 
                         sprintf("数据框 %d x %d", nrow(args[[arg_name]]), ncol(args[[arg_name]]))
                       else if(is.vector(args[[arg_name]])) 
                         sprintf("向量 长度=%d", length(args[[arg_name]]))
                       else 
                         class(args[[arg_name]])[1]))
      }
    }
  }
}

# 设置环境监控
env_monitor <- monitor_environment()
fun_monitor <- monitor_function_calls()

# 设置函数跟踪
trace(bibliometrix::readFiles, 
      tracer = function(...) fun_monitor("readFiles", list(...)))
trace(bibliometrix::convert2df, 
      tracer = function(...) fun_monitor("convert2df", list(...)))
trace(bibliometrix::duplicatedMatching, 
      tracer = function(...) fun_monitor("duplicatedMatching", list(...)))

# 设置定期环境检查
check_interval <- 5  # 每5秒检查一次
last_check <- Sys.time()

# 创建检查函数
check_environment <- function() {
  current_time <- Sys.time()
  if(as.numeric(difftime(current_time, last_check, units="secs")) >= check_interval) {
    env_monitor()
    last_check <<- current_time
  }
}

# 设置监控标志
monitoring <- TRUE

# 设置定期检查
while(monitoring) {
  tryCatch({
    check_environment()
    Sys.sleep(1)  # 每秒检查一次是否需要执行环境检查
  }, error = function(e) {
    message("监控过程出错: ", conditionMessage(e))
    monitoring <<- FALSE
  }, interrupt = function(e) {
    message("\n监控被用户中断")
    monitoring <<- FALSE
  })
}

# 清理跟踪
untrace(bibliometrix::readFiles)
untrace(bibliometrix::convert2df)
untrace(bibliometrix::duplicatedMatching)

# 启动 Biblioshiny
biblioshiny() 
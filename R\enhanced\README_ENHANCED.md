# 增强算法与自定义流程脚本说明

本目录用于存放所有增强型数据处理与去重相关脚本，现已细分为如下两个子目录：

## 1. data_enhancement/ 数据增强
- **01_basic_field_standardization.R**：基础字段标准化，适用于初步数据清洗。
- **02_framework_field_standardization.R**：框架化字段标准化，适合需要自定义字段映射和批量处理的场景。
- **03_complete_field_standardization.R**：全量字段标准化，适用于高质量数据增强需求。
- **doi_completion_pipeline.R**：DOI补全唯一主流程脚本，支持批量补全、断点续跑、UT/元数据补全、Crossref查找等多模式（mode参数：batch/resume/by_ut/crossref），推荐作为标准入口。

## 2. deduplication/ 去重流程
- **01_detect_and_report_duplicates.R**：仅检测并报告重复记录，不做实际去重。
- **02_stage1_deterministic_deduplication.R**：第一阶段确定性去重，适合大批量初筛。
- **03_enhanced_advanced_deduplication.R**：增强型高级去重，支持多策略和参数化。
- **enhanced_deduplication_pipeline.R**：唯一主流程脚本，支持增强数据集与标准数据集两种模式（mode参数："enhanced"/"bibliometrix"），推荐作为标准入口。

---

### 设计原则
- 所有增强算法均与官方数据结构兼容，便于与Biblioshiny/Bibliometrix流程对比。
- 推荐优先使用主流程脚本（如pipeline），其余脚本作为功能模块或备选方案。
- 详细使用说明请参见各脚本内注释。

---

如需进一步定制或扩展，请联系项目维护者。


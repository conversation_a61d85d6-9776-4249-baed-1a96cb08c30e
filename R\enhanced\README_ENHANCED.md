# Enhanced目录说明

## 📁 目录概述

Enhanced目录包含高级数据增强和去重处理功能，提供多种实现版本以满足不同需求。

## 🔧 核心文件说明

### 数据增强类
- `01_data_enhancement_framework.R` - 高级API框架版本 (1112行)
  - 基于bibliometrix源代码的标准化
  - 高级API请求管理和数据库连接
  - 并行处理配置
  - 备选API机制

- `01_data_enhancement_complete.R` - 完整功能版本 (713行)
  - 多源API集成 (Crossref + OpenAlex)
  - SQLite缓存系统
  - 字段标准化和质量评估
  - 数据异常检测

- `01_data_enhancement_simple.R` - 简化版本
  - 基础功能，适合快速处理

### 去重处理类
- `02_deduplication_enhanced_advanced.R` - 高级多轮去重 (314行)
  - 多阈值标题匹配 (0.98→0.95→0.90)
  - 作者和摘要模糊匹配
  - 详细的重复项日志记录

- `05_deduplication_enhanced.R` - 核心增强去重
  - 标准的增强去重处理

- `02_detect_duplicates_only.R` - 重复检测工具
  - 仅检测重复项，不删除

- `01_stage1_deterministic_deduplication.R` - 确定性去重
  - 第一阶段精确匹配去重

## 🎯 使用建议

### 数据增强场景
1. **标准处理**: 使用根目录的 `07_data_enhancement.R` (整合版)
2. **高级API需求**: 使用 `01_data_enhancement_framework.R`
3. **完整功能**: 使用 `01_data_enhancement_complete.R`
4. **快速处理**: 使用 `01_data_enhancement_simple.R`

### 去重处理场景
1. **标准去重**: 使用根目录的 `05_deduplication_enhanced.R`
2. **高级多轮去重**: 使用 `02_deduplication_enhanced_advanced.R`
3. **学术标准**: 使用 `../biblioshiny/02_deduplication_biblioshiny.R`
4. **重复检测**: 使用 `02_detect_duplicates_only.R`

## 🔗 与主流程的关系

Enhanced目录中的脚本主要作为专用工具和高级功能的实现参考：

```
主流程 (根目录)          Enhanced目录 (专用工具)
├── 07_data_enhancement.R  ← 整合自多个enhanced版本
├── 05_deduplication_enhanced.R  ← 对应enhanced版本
└── 06_doi_completion.R    ← 集成到enhancement中
```

## ⚠️ 注意事项

1. **不要删除**: 所有文件都有其特定用途和价值
2. **版本选择**: 根据具体需求选择合适的版本
3. **配置调整**: 注意各版本的配置参数差异
4. **依赖关系**: 某些脚本可能依赖特定的数据结构

## 📊 代码质量评估

- **代码行数**: 总计约2500+行高质量代码
- **功能完整性**: 覆盖完整的数据增强流程
- **算法先进性**: 基于最新的学术标准和最佳实践
- **工程化程度**: 包含错误处理、日志记录、批处理等
- **可维护性**: 模块化设计，清晰的函数结构

Enhanced目录代表了项目中最高质量的数据处理实现。


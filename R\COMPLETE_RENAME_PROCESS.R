# 完成重命名过程
# 恢复缺失文件并完成重命名

cat("=== 完成重命名过程 ===\n")
cat("恢复缺失文件并完成重命名...\n\n")

# 恢复缺失的文件
restore_missing_files <- function() {
  cat("📦 恢复缺失的文件...\n")
  
  # 从备份目录恢复文件
  missing_files <- list(
    "BACKUP_FINAL_CLEANUP/06_doi_completion.R" = "06_doi_completion.R",
    "BACKUP_FINAL_CLEANUP/07_data_enhancement.R" = "07_data_enhancement.R", 
    "BACKUP_FINAL_CLEANUP/08_data_integration.R" = "08_data_integration.R",
    "BACKUP_FINAL_CLEANUP/09_quality_control.R" = "09_quality_control.R"
  )
  
  for (backup_path in names(missing_files)) {
    target_file <- missing_files[[backup_path]]
    src_path <- file.path("R", backup_path)
    dst_path <- file.path("R", target_file)
    
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      cat(sprintf("  ✅ 恢复: %s → %s\n", backup_path, target_file))
    } else {
      cat(sprintf("  ❌ 备份文件不存在: %s\n", backup_path))
    }
  }
}

# 完成重命名
complete_rename <- function() {
  cat("\n🔄 完成剩余重命名...\n")
  
  # 剩余的重命名映射
  remaining_renames <- list(
    "06_doi_completion.R" = "08_complete_missing_dois.R",
    "07_data_enhancement.R" = "07_enhance_data_comprehensive.R",
    "08_data_integration.R" = "09_integrate_enhanced_data.R", 
    "09_quality_control.R" = "10_quality_control_and_report.R"
  )
  
  for (old_name in names(remaining_renames)) {
    old_path <- file.path("R", old_name)
    new_name <- remaining_renames[[old_name]]
    new_path <- file.path("R", new_name)
    
    if (file.exists(old_path)) {
      file.rename(old_path, new_path)
      cat(sprintf("  ✅ %s → %s\n", old_name, new_name))
    } else {
      cat(sprintf("  ❌ %s (文件不存在)\n", old_name))
    }
  }
}

# 最终验证
final_verification <- function() {
  cat("\n🔍 最终验证...\n")
  
  expected_files <- c(
    "01_import_wos_data.R",
    "02_import_citespace_data.R", 
    "03_import_vosviewer_data.R",
    "04_validate_and_clean_data.R",
    "05_deduplicate_records.R",
    "06_deduplicate_advanced.R",
    "07_enhance_data_comprehensive.R",
    "08_complete_missing_dois.R",
    "09_integrate_enhanced_data.R",
    "10_quality_control_and_report.R"
  )
  
  cat("最终文件检查:\n")
  all_present <- TRUE
  
  for (file in expected_files) {
    if (file.exists(file.path("R", file))) {
      cat(sprintf("  ✅ %s\n", file))
    } else {
      cat(sprintf("  ❌ %s (缺失)\n", file))
      all_present <- FALSE
    }
  }
  
  if (all_present) {
    cat("\n🎉 所有文件重命名成功！\n")
    
    # 显示最终的处理流程
    cat("\n📋 最终处理流程:\n")
    cat("01_import_wos_data.R              # WoS数据导入\n")
    cat("02_import_citespace_data.R        # CiteSpace数据导入\n") 
    cat("03_import_vosviewer_data.R        # VOSviewer数据导入\n")
    cat("04_validate_and_clean_data.R      # 数据验证与清理\n")
    cat("05_deduplicate_records.R          # 去重处理\n")
    cat("06_deduplicate_advanced.R         # 高级去重(可选)\n")
    cat("07_enhance_data_comprehensive.R   # 数据增强\n")
    cat("08_complete_missing_dois.R        # DOI补全 ⭐\n")
    cat("09_integrate_enhanced_data.R      # 数据整合\n")
    cat("10_quality_control_and_report.R   # 质量控制\n")
    
    cat("\n🎯 核心改进:\n")
    cat("- DOI补全从06移到08，正确定位在数据增强阶段\n")
    cat("- 文件编号与处理顺序完全对应\n")
    cat("- 文件名直观反映处理功能\n")
    
  } else {
    cat("\n⚠️  仍有文件缺失\n")
  }
  
  return(all_present)
}

# 清理多余文件
cleanup_extra_files <- function() {
  cat("\n🧹 清理多余文件...\n")
  
  # 删除重复的模板文件
  extra_files <- c(
    "01_import_wos_data.R",  # 如果是空模板
    "02_import_citespace_data.R",
    "03_import_vosviewer_data.R", 
    "04_validate_and_clean_data.R",
    "05_deduplicate_records.R"
  )
  
  for (file in extra_files) {
    file_path <- file.path("R", file)
    if (file.exists(file_path)) {
      # 检查是否是模板文件
      content <- readLines(file_path, warn = FALSE)
      if (length(content) > 0 && any(grepl("TODO:", content))) {
        file.remove(file_path)
        cat(sprintf("  🗑️  删除模板: %s\n", file))
      }
    }
  }
}

# 生成最终报告
generate_final_report <- function(success) {
  cat("\n📋 生成最终报告...\n")
  
  report_content <- sprintf('# 文件重命名最终报告

## 完成时间
%s

## 重命名结果
%s

## 最终文件结构

### 核心处理流程 (10个文件)
```
01_import_wos_data.R              # WoS数据导入
02_import_citespace_data.R        # CiteSpace数据导入  
03_import_vosviewer_data.R        # VOSviewer数据导入
04_validate_and_clean_data.R      # 数据验证与清理
05_deduplicate_records.R          # 去重处理
06_deduplicate_advanced.R         # 高级去重(可选)
07_enhance_data_comprehensive.R   # 数据增强
08_complete_missing_dois.R        # DOI补全 ⭐ 重要位置
09_integrate_enhanced_data.R      # 数据整合
10_quality_control_and_report.R   # 质量控制
```

### 处理流程
```
数据导入(01-03) → 验证清理(04) → 去重(05-06) → 增强+DOI补全(07-08) → 整合+质控(09-10)
```

## 核心改进

### 1. DOI补全位置优化
- **从06调整到08** - 正确定位在数据增强阶段
- **逻辑合理** - DOI补全是数据增强的重要组成部分
- **流程优化** - 在数据增强后、整合前进行DOI补全

### 2. 编号与顺序对应
- 文件编号与实际处理顺序完全一致
- 消除了编号跳跃和逻辑混乱
- 便于理解和维护

### 3. 文件名优化
- 使用动词形式，直观表达功能
- 统一命名规范
- 提高可读性

## 使用指南

### 标准执行顺序
```r
# 完整流程
scripts <- c("01", "02", "03", "04", "05", "07", "08", "09", "10")
for (num in scripts) {
  script_file <- list.files("R", pattern = paste0("^", num, "_"), full.names = TRUE)[1]
  if (!is.null(script_file) && file.exists(script_file)) {
    cat("执行:", basename(script_file), "\\n")
    source(script_file)
  }
}
```

### 可选高级功能
```r
# 高级去重 (在05后可选执行)
source("R/06_deduplicate_advanced.R")
```

### 部分流程
```r
# 只执行数据增强部分
source("R/07_enhance_data_comprehensive.R")  # 数据增强
source("R/08_complete_missing_dois.R")       # DOI补全
source("R/09_integrate_enhanced_data.R")     # 数据整合
```

## 备份保护
- `BACKUP_ACTUAL_RENAME/` - 重命名前的原始文件
- `BACKUP_FINAL_CLEANUP/` - 清理前的备份文件
- `enhanced/` - 高级功能版本保持不变

## 总结

文件重命名完成！现在的R脚本结构：

1. **逻辑清晰** - 编号与处理顺序完全对应
2. **DOI补全正确定位** - 在数据增强阶段(08)
3. **文件名直观** - 直接反映处理功能
4. **便于使用** - 按编号顺序执行即可
5. **安全保障** - 完整的备份保护

这个结构完全符合数据处理的最佳实践！
', 
    format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    if (success) "✅ 重命名完全成功" else "❌ 重命名未完成"
  )
  
  writeLines(report_content, "R/FINAL_RENAME_REPORT.md")
  cat("✅ 最终报告已生成: R/FINAL_RENAME_REPORT.md\n")
}

# 主执行函数
main_complete_rename <- function() {
  cat("开始完成重命名过程...\n\n")
  
  restore_missing_files()
  complete_rename()
  cleanup_extra_files()
  success <- final_verification()
  generate_final_report(success)
  
  cat("\n=== 重命名过程完成 ===\n")
  cat("✅ 恢复了缺失文件\n")
  cat("✅ 完成了剩余重命名\n")
  cat("✅ 清理了多余文件\n")
  cat("✅ 生成了最终报告\n")
  
  if (success) {
    cat("\n🎉 文件重命名完全成功！\n")
    cat("📋 DOI补全现在正确位于数据增强阶段 (08)\n")
    cat("🔄 所有文件按处理顺序完美组织\n")
  } else {
    cat("\n⚠️  重命名过程仍有问题，请检查报告\n")
  }
}

# 执行完成重命名
main_complete_rename()

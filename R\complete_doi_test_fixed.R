# 修复版DOI补全测试脚本
# 解决网络连接和数据验证问题

# 加载必要的包
library(tidyverse)
library(here)
library(httr)
library(jsonlite)
library(stringdist)

# 设置日志函数
log_message <- function(message, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[%s] [%s] %s\n", timestamp, toupper(type), message))
}

# 网络连接检查函数
check_network_connection <- function() {
  tryCatch({
    response <- GET("https://api.crossref.org/", timeout(10))
    status_code(response) == 200
  }, error = function(e) {
    FALSE
  })
}

# 带重试机制的API请求函数
safe_api_request <- function(url, max_retries = 3, retry_delay = 5) {
  for (attempt in 1:max_retries) {
    tryCatch({
      response <- GET(url, user_agent("BiblioEnhancer/1.0"), timeout(30))
      if (status_code(response) == 200) {
        return(response)
      } else {
        log_message(sprintf("API请求失败，状态码: %d，尝试 %d/%d", 
                           status_code(response), attempt, max_retries), "warning")
      }
    }, error = function(e) {
      log_message(sprintf("网络请求错误: %s，尝试 %d/%d", 
                         e$message, attempt, max_retries), "warning")
    })
    
    if (attempt < max_retries) {
      log_message(sprintf("等待 %d 秒后重试...", retry_delay), "info")
      Sys.sleep(retry_delay)
    }
  }
  return(NULL)
}

# 数据标准化函数
normalize_title <- function(title) {
  if (is.na(title)) return(NA)
  title <- tolower(title)
  title <- gsub("[[:punct:]]", " ", title)
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  title <- gsub(paste0("\\b(", paste(stop_words, collapse="|"), ")\\b"), "", title)
  title <- gsub("\\s+", " ", title)
  trimws(title)
}

# 改进相似度计算
calculate_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  1 - stringdist(str1, str2, method="jw")
}

# 简化的DOI查询函数
get_doi_simple <- function(title, year, debug = FALSE) {
  # 检查网络连接
  if (!check_network_connection()) {
    log_message("网络连接检查失败，跳过API查询", "warning")
    return(list(doi = NA, score = 0))
  }
  
  # 构建查询URL
  clean_title <- normalize_title(title)
  title_words <- strsplit(clean_title, "\\s+")[[1]]
  keywords <- title_words[nchar(title_words) > 3]
  if (length(keywords) > 5) keywords <- keywords[1:5]
  
  if (length(keywords) == 0) return(list(doi = NA, score = 0))
  
  query_string <- paste(keywords, collapse = " ")
  url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=5", 
                 URLencode(query_string), as.numeric(year)-1, as.numeric(year)+1)
  
  response <- safe_api_request(url)
  if (is.null(response)) return(list(doi = NA, score = 0))
  
  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) { 
    log_message(sprintf("JSON解析错误: %s", e$message), "error")
    return(list(doi = NA, score = 0)) 
  })
  
  if (debug) {
    log_message(sprintf("查询关键词: '%s'", query_string))
  }
  
  if (!is.null(content$message$items) && length(content$message$items) > 0) {
    items <- content$message$items
    
    if (debug && nrow(items) > 0) {
      log_message(sprintf("找到 %d 个候选结果:", nrow(items)))
      for (i in 1:min(3, nrow(items))) {
        item <- items[i,]
        log_message(sprintf("  %d. 标题: %s | DOI: %s | score: %.2f",
          i, substr(item$title[[1]], 1, 60), item$DOI, item$score))
      }
    }
    
    # 寻找最佳匹配
    best_match_idx <- NULL
    best_similarity <- 0
    
    for (i in 1:nrow(items)) {
      item <- items[i,]
      if (!is.null(item$title) && length(item$title) > 0) {
        candidate_title <- item$title[[1]]
        current_similarity <- calculate_similarity(normalize_title(title), normalize_title(candidate_title))
        
        if (current_similarity > best_similarity) {
          best_similarity <- current_similarity
          best_match_idx <- i
        }
      }
    }
    
    # 使用较低的阈值
    similarity_threshold <- 0.3
    
    if (!is.null(best_match_idx) && best_similarity > similarity_threshold) {
      doi_found <- items$DOI[best_match_idx]
      crossref_score <- items$score[best_match_idx]
      
      if (!is.na(doi_found) && doi_found != "") {
        if (debug) {
          log_message(sprintf("成功找到DOI: %s (相似度: %.2f, Crossref分数: %.2f)", 
                             doi_found, best_similarity, crossref_score))
        }
        return(list(doi = doi_found, score = crossref_score, similarity = best_similarity))
      }
    } else {
      if (debug) {
        log_message(sprintf("最佳相似度 %.2f 低于阈值 %.2f", best_similarity, similarity_threshold))
      }
    }
  } else {
    if (debug) {
      log_message("未找到任何候选结果")
    }
  }
  
  return(list(doi = NA, score = 0))
}

# 主测试函数
test_doi_completion <- function() {
  log_message("开始DOI补全测试（修复版）")
  
  # 1. 检查网络连接
  log_message("步骤1: 检查网络连接")
  if (!check_network_connection()) {
    log_message("网络连接失败，无法继续测试", "error")
    return(FALSE)
  }
  log_message("网络连接正常")
  
  # 2. 加载数据
  log_message("步骤2: 加载数据")
  input_file <- here("data_repository", "04_enhancement_reports", "missing_doi_records.csv")
  
  if (!file.exists(input_file)) {
    log_message(sprintf("输入文件不存在: %s", input_file), "error")
    return(FALSE)
  }
  
  missing_doi_records <- read_csv(input_file, show_col_types = FALSE)
  log_message(sprintf("成功加载数据: %d行", nrow(missing_doi_records)))
  
  # 3. 完整处理（处理所有记录）
  test_records <- nrow(missing_doi_records)
  log_message(sprintf("开始处理全部%d条记录（完整模式）", test_records))
  
  results <- data.frame(
    UT = missing_doi_records$UT[1:test_records],
    TI = missing_doi_records$TI[1:test_records],
    AU = missing_doi_records$AU[1:test_records],
    PY = missing_doi_records$PY[1:test_records],
    SO = missing_doi_records$SO[1:test_records],
    VL = missing_doi_records$VL[1:test_records],
    IS = missing_doi_records$IS[1:test_records],
    PG = missing_doi_records$PG[1:test_records],
    DOI = NA,
    confidence = NA,
    similarity = NA,
    quality_score = NA,
    review_needed = FALSE,
    match_details = "",
    query_keywords = "",
    status = "pending",
    stringsAsFactors = FALSE
  )
  
  success_count <- 0
  
  for (i in 1:test_records) {
    log_message(sprintf("\n=== 处理第 %d/%d 条记录 ===", i, test_records))
    log_message(sprintf("标题: %s", substr(results$TI[i], 1, 80)))
    
    # 查询DOI
    doi_result <- get_doi_simple(results$TI[i], results$PY[i], debug = TRUE)
    
    if (!is.na(doi_result$doi)) {
      results$DOI[i] <- doi_result$doi
      results$confidence[i] <- doi_result$score
      results$similarity[i] <- doi_result$similarity
      results$status[i] <- "success"

      # 计算质量分数
      quality_score <- doi_result$similarity * 0.7 + (doi_result$score / 100) * 0.3
      results$quality_score[i] <- quality_score

      # 记录查询关键词
      title_words <- strsplit(normalize_title(results$TI[i]), "\\s+")[[1]]
      keywords <- title_words[nchar(title_words) > 3]
      if (length(keywords) > 5) keywords <- keywords[1:5]
      results$query_keywords[i] <- paste(keywords, collapse = ", ")

      # 判断是否需要人工审核
      if (quality_score < 0.6 || doi_result$similarity < 0.5 || doi_result$score < 30) {
        results$review_needed[i] <- TRUE
        results$match_details[i] <- sprintf("低置信度匹配 (相似度:%.2f, Crossref分数:%.1f)",
                                           doi_result$similarity, doi_result$score)
      } else {
        results$match_details[i] <- sprintf("高置信度匹配 (相似度:%.2f, Crossref分数:%.1f)",
                                           doi_result$similarity, doi_result$score)
      }

      success_count <- success_count + 1
      log_message(sprintf("✓ 成功找到DOI: %s (质量分数: %.2f)", doi_result$doi, quality_score))
    } else {
      results$status[i] <- "failed"
      results$match_details[i] <- "未找到匹配的DOI"
      log_message("✗ 未找到匹配的DOI")
    }
    
    # 防止API限制
    Sys.sleep(2)
  }
  
  # 4. 详细统计分析
  success_rate <- 100 * success_count / test_records
  high_quality_count <- sum(!is.na(results$quality_score) & results$quality_score >= 0.7, na.rm = TRUE)
  review_needed_count <- sum(results$review_needed, na.rm = TRUE)

  log_message(sprintf("\n=== 完整处理结果统计 ==="))
  log_message(sprintf("总记录数: %d", test_records))
  log_message(sprintf("成功补全: %d (%.2f%%)", success_count, success_rate))
  log_message(sprintf("高质量匹配: %d (%.2f%%)", high_quality_count, 100 * high_quality_count / test_records))
  log_message(sprintf("需要人工审核: %d (%.2f%%)", review_needed_count, 100 * review_needed_count / test_records))
  log_message(sprintf("补全失败: %d (%.2f%%)", test_records - success_count, 100 - success_rate))

  # 质量分布统计
  quality_excellent <- sum(!is.na(results$quality_score) & results$quality_score >= 0.8, na.rm = TRUE)
  quality_good <- sum(!is.na(results$quality_score) & results$quality_score >= 0.6 & results$quality_score < 0.8, na.rm = TRUE)
  quality_fair <- sum(!is.na(results$quality_score) & results$quality_score >= 0.4 & results$quality_score < 0.6, na.rm = TRUE)
  quality_poor <- sum(!is.na(results$quality_score) & results$quality_score < 0.4, na.rm = TRUE)

  log_message(sprintf("\n=== 质量分布 ==="))
  log_message(sprintf("优秀 (≥0.8): %d (%.2f%%)", quality_excellent, 100 * quality_excellent / test_records))
  log_message(sprintf("良好 (0.6-0.8): %d (%.2f%%)", quality_good, 100 * quality_good / test_records))
  log_message(sprintf("一般 (0.4-0.6): %d (%.2f%%)", quality_fair, 100 * quality_fair / test_records))
  log_message(sprintf("较差 (<0.4): %d (%.2f%%)", quality_poor, 100 * quality_poor / test_records))

  # 5. 保存详细结果
  output_dir <- here("data_repository", "04_enhancement_reports")
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

  # 保存CSV文件
  output_file <- file.path(output_dir, "doi_completion_full_enhanced.csv")
  write_csv(results, output_file)

  # 创建Excel报告
  if (require(openxlsx, quietly = TRUE)) {
    wb <- createWorkbook()

    # 工作表1：完整结果
    addWorksheet(wb, "完整结果")
    writeData(wb, "完整结果", results)

    # 工作表2：高质量匹配
    high_quality_results <- results[!is.na(results$quality_score) & results$quality_score >= 0.7, ]
    addWorksheet(wb, "高质量匹配")
    writeData(wb, "高质量匹配", high_quality_results)

    # 工作表3：需要人工审核
    review_needed_results <- results[results$review_needed == TRUE, ]
    addWorksheet(wb, "需要人工审核")
    writeData(wb, "需要人工审核", review_needed_results)

    # 工作表4：失败记录
    failed_results <- results[is.na(results$DOI), ]
    addWorksheet(wb, "失败记录")
    writeData(wb, "失败记录", failed_results)

    # 工作表5：统计摘要
    addWorksheet(wb, "统计摘要")
    summary_stats <- data.frame(
      指标 = c("总记录数", "成功补全", "成功率", "高质量匹配", "高质量率",
               "需要人工审核", "审核率", "补全失败", "失败率",
               "优秀质量", "良好质量", "一般质量", "较差质量"),
      数值 = c(test_records, success_count, sprintf("%.2f%%", success_rate),
               high_quality_count, sprintf("%.2f%%", 100 * high_quality_count / test_records),
               review_needed_count, sprintf("%.2f%%", 100 * review_needed_count / test_records),
               test_records - success_count, sprintf("%.2f%%", 100 - success_rate),
               quality_excellent, quality_good, quality_fair, quality_poor)
    )
    writeData(wb, "统计摘要", summary_stats)

    # 保存Excel文件
    excel_file <- file.path(output_dir, "doi_completion_full_enhanced.xlsx")
    saveWorkbook(wb, excel_file, overwrite = TRUE)
    log_message(sprintf("Excel报告已保存: %s", excel_file))
  }

  log_message(sprintf("CSV结果已保存: %s", output_file))
  
  return(success_count > 0)
}

# 运行测试
if (interactive()) {
  test_result <- test_doi_completion()
} else {
  test_result <- test_doi_completion()
}

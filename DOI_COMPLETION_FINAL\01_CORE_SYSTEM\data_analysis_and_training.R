# 数据分析与训练系统
# 分析全量数据，训练优化评分阈值，实现DOI-MeSH联合补全

library(httr)
library(jsonlite)
library(stringdist)

# 加载所有系统
source("doi_completion_core.R")
source("doi_completion_final_optimized.R")
source("pubmed_mesh_optimized.R")

cat("=== 数据分析与训练系统 ===\n")
cat("🎯 目标: 分析全量数据，训练阈值，优化DOI-MeSH补全\n")
cat("📊 策略: 先DOI补全 → 再MeSH查询 + 机器学习阈值优化\n\n")

# === 数据加载与分析 ===
load_and_analyze_data <- function() {
  cat("📂 加载数据文件...\n")
  
  data_file <- "C:/Users/<USER>/Desktop/bibliometric-analysis/data_repository/01_baseline_datasets/bibliometrix_processed/datay_bibliometrix_initial.rds"
  
  if (!file.exists(data_file)) {
    cat("❌ 数据文件不存在:", data_file, "\n")
    return(NULL)
  }
  
  # 加载RDS数据
  data <- readRDS(data_file)
  cat("✅ 数据加载成功\n")
  
  # 分析数据结构
  cat(sprintf("📊 数据概览:\n"))
  cat(sprintf("  总记录数: %d\n", nrow(data)))
  cat(sprintf("  字段数: %d\n", ncol(data)))
  
  # 分析关键字段
  key_fields <- c("TI", "AU", "PY", "SO", "DI", "UT", "AB", "DE", "ID")
  available_fields <- intersect(key_fields, colnames(data))
  cat(sprintf("  可用关键字段: %s\n", paste(available_fields, collapse = ", ")))
  
  # DOI分布分析
  if ("DI" %in% colnames(data)) {
    doi_available <- sum(!is.na(data$DI) & data$DI != "")
    doi_missing <- nrow(data) - doi_available
    doi_rate <- 100 * doi_available / nrow(data)
    
    cat(sprintf("\n🔍 DOI分布分析:\n"))
    cat(sprintf("  已有DOI: %d (%.1f%%)\n", doi_available, doi_rate))
    cat(sprintf("  缺失DOI: %d (%.1f%%)\n", doi_missing, 100 - doi_rate))
  }
  
  # 年份分布分析
  if ("PY" %in% colnames(data)) {
    year_range <- range(data$PY, na.rm = TRUE)
    cat(sprintf("\n📅 年份分布: %d - %d\n", year_range[1], year_range[2]))
    
    # 按年代分组
    data$decade <- floor(data$PY / 10) * 10
    decade_counts <- table(data$decade)
    cat("  年代分布:\n")
    for (decade in names(decade_counts)) {
      cat(sprintf("    %s年代: %d篇\n", decade, decade_counts[decade]))
    }
  }
  
  # 期刊分布分析
  if ("SO" %in% colnames(data)) {
    top_journals <- head(sort(table(data$SO), decreasing = TRUE), 10)
    cat(sprintf("\n📚 主要期刊 (前10):\n"))
    for (i in 1:length(top_journals)) {
      cat(sprintf("  %d. %s: %d篇\n", i, names(top_journals)[i], top_journals[i]))
    }
  }
  
  return(data)
}

# === 智能DOI-MeSH联合补全策略 ===
smart_doi_mesh_completion <- function(title, authors, year, journal, existing_doi = NULL) {
  cat(sprintf("🔍 智能DOI-MeSH补全: %s\n", substr(title, 1, 50)))
  
  result <- list(
    original_doi = existing_doi,
    completed_doi = NULL,
    mesh_info = NULL,
    completion_source = NULL,
    mesh_source = NULL,
    success = FALSE
  )
  
  # 策略1: 如果已有DOI，直接查询MeSH
  if (!is.null(existing_doi) && existing_doi != "" && !is.na(existing_doi)) {
    cat("  已有DOI，直接查询MeSH...\n")
    result$completed_doi <- existing_doi
    result$completion_source <- "existing"
    
    # 通过DOI查询PubMed获取MeSH
    mesh_info <- query_mesh_by_doi(existing_doi)
    if (!is.null(mesh_info)) {
      result$mesh_info <- mesh_info
      result$mesh_source <- "doi_lookup"
      result$success <- TRUE
      cat(sprintf("  ✅ MeSH查询成功: %s\n", paste(mesh_info$mesh_publication_types_cn, collapse = ", ")))
      return(result)
    }
  }
  
  # 策略2: 使用多引擎补全DOI
  cat("  使用多引擎补全DOI...\n")
  
  # 优先级: Crossref > OpenAlex > PubMed优化版
  engines <- list(
    list(name = "crossref", func = function() search_doi(title, authors, year, journal)),
    list(name = "openalex", func = function() search_doi_openalex_final(title, authors, year, journal)),
    list(name = "pubmed", func = function() search_doi_pubmed_optimized(title, authors, year, journal))
  )
  
  for (engine in engines) {
    cat(sprintf("    尝试 %s...\n", engine$name))
    
    if (engine$name == "crossref") {
      doi_result <- search_doi(title, authors, year, journal)
      if (!is.null(doi_result)) doi_result$source <- "crossref"
    } else if (engine$name == "openalex") {
      doi_result <- search_doi_openalex_final(title, authors, year, journal)
    } else if (engine$name == "pubmed") {
      doi_result <- search_doi_pubmed_optimized(title, authors, year, journal)
    }
    
    if (!is.null(doi_result)) {
      result$completed_doi <- doi_result$doi
      result$completion_source <- engine$name
      
      # 如果是PubMed，直接使用其MeSH信息
      if (engine$name == "pubmed" && !is.null(doi_result$mesh_publication_types)) {
        result$mesh_info <- doi_result
        result$mesh_source <- "pubmed_direct"
        result$success <- TRUE
        cat(sprintf("    ✅ %s成功 (含MeSH): %s\n", engine$name, doi_result$doi))
        return(result)
      }
      
      # 对于其他引擎，尝试通过DOI查询MeSH
      cat(sprintf("    ✅ %s找到DOI: %s，查询MeSH...\n", engine$name, doi_result$doi))
      mesh_info <- query_mesh_by_doi(doi_result$doi)
      if (!is.null(mesh_info)) {
        result$mesh_info <- mesh_info
        result$mesh_source <- "doi_lookup"
        result$success <- TRUE
        cat(sprintf("    ✅ MeSH查询成功\n"))
        return(result)
      }
      
      # 即使没有MeSH，DOI补全也算成功
      result$success <- TRUE
      cat(sprintf("    ✅ DOI补全成功，但无MeSH信息\n"))
      return(result)
    }
  }
  
  cat("  ❌ 所有引擎都未找到DOI\n")
  return(result)
}

# === 通过DOI查询MeSH信息 ===
query_mesh_by_doi <- function(doi) {
  tryCatch({
    # 使用DOI在PubMed中搜索
    search_terms <- sprintf('"%s"[DOI]', doi)
    esearch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=%s&retmax=1&retmode=json",
                          URLencode(search_terms))
    
    search_response <- GET(esearch_url, 
                          user_agent("DOI_MeSH_Lookup/1.0"),
                          timeout(20))
    
    if (status_code(search_response) != 200) return(NULL)
    
    search_content <- fromJSON(rawToChar(search_response$content))
    
    if (is.null(search_content$esearchresult$idlist) || 
        length(search_content$esearchresult$idlist) == 0) {
      return(NULL)
    }
    
    pmid <- search_content$esearchresult$idlist[1]
    
    # 获取详细MeSH信息
    efetch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=%s&retmode=xml", pmid)
    
    fetch_response <- GET(efetch_url,
                         user_agent("DOI_MeSH_Lookup/1.0"),
                         timeout(20))
    
    if (status_code(fetch_response) != 200) return(NULL)
    
    # 解析XML并提取MeSH信息
    xml_content <- read_xml(rawToChar(fetch_response$content))
    article <- xml_find_first(xml_content, "//PubmedArticle")
    
    if (is.null(article)) return(NULL)
    
    # 提取MeSH Publication Types
    pub_type_nodes <- xml_find_all(article, ".//PublicationType")
    mesh_pub_types <- c()
    mesh_pub_types_cn <- c()
    
    if (length(pub_type_nodes) > 0) {
      for (pt_node in pub_type_nodes) {
        pub_type <- xml_text(pt_node)
        mesh_pub_types <- c(mesh_pub_types, pub_type)
        
        # 中文翻译
        if (pub_type %in% names(enhanced_mesh_types)) {
          mesh_pub_types_cn <- c(mesh_pub_types_cn, enhanced_mesh_types[[pub_type]]$cn)
        } else {
          mesh_pub_types_cn <- c(mesh_pub_types_cn, pub_type)
        }
      }
    }
    
    # 提取MeSH主题词
    mesh_heading_nodes <- xml_find_all(article, ".//MeshHeading/DescriptorName")
    mesh_headings <- c()
    if (length(mesh_heading_nodes) > 0) {
      mesh_headings <- sapply(mesh_heading_nodes, xml_text)
    }
    
    return(list(
      pmid = pmid,
      mesh_publication_types = mesh_pub_types,
      mesh_publication_types_cn = mesh_pub_types_cn,
      mesh_headings = mesh_headings[1:min(10, length(mesh_headings))],
      source = "doi_lookup"
    ))
    
  }, error = function(e) {
    return(NULL)
  })
}

# === 阈值训练系统 ===
train_optimal_thresholds <- function(data, sample_size = 100) {
  cat(sprintf("\n🤖 开始阈值训练 (样本大小: %d)\n", sample_size))
  
  # 随机采样
  if (nrow(data) > sample_size) {
    sample_indices <- sample(nrow(data), sample_size)
    training_data <- data[sample_indices, ]
  } else {
    training_data <- data
  }
  
  cat(sprintf("📊 训练数据: %d 条记录\n", nrow(training_data)))
  
  # 初始化训练结果
  training_results <- data.frame(
    index = 1:nrow(training_data),
    title = substr(training_data$TI, 1, 50),
    year = training_data$PY,
    has_original_doi = !is.na(training_data$DI) & training_data$DI != "",
    crossref_success = FALSE,
    crossref_score = 0,
    openalex_success = FALSE,
    openalex_score = 0,
    pubmed_success = FALSE,
    pubmed_score = 0,
    best_engine = "",
    best_score = 0,
    stringsAsFactors = FALSE
  )
  
  # 收集所有成功案例的评分数据
  all_scores <- list(
    crossref = list(title_sim = c(), journal_sim = c(), year_sim = c(), final_score = c()),
    openalex = list(title_sim = c(), journal_sim = c(), year_sim = c(), final_score = c()),
    pubmed = list(title_sim = c(), journal_sim = c(), year_sim = c(), final_score = c())
  )
  
  # 逐个测试训练样本
  for (i in 1:nrow(training_data)) {
    record <- training_data[i, ]
    cat(sprintf("\n训练样本 %d/%d: %s\n", i, nrow(training_data), substr(record$TI, 1, 40)))
    
    # 测试各引擎
    engines_to_test <- list(
      list(name = "crossref", func = function() search_doi(record$TI, record$AU, record$PY, record$SO)),
      list(name = "openalex", func = function() search_doi_openalex_final(record$TI, record$AU, record$PY, record$SO)),
      list(name = "pubmed", func = function() search_doi_pubmed_optimized(record$TI, record$AU, record$PY, record$SO))
    )
    
    best_score <- 0
    best_engine <- ""
    
    for (engine in engines_to_test) {
      cat(sprintf("  测试 %s...\n", engine$name))
      
      if (engine$name == "crossref") {
        result <- search_doi(record$TI, record$AU, record$PY, record$SO)
        if (!is.null(result)) result$source <- "crossref"
      } else if (engine$name == "openalex") {
        result <- search_doi_openalex_final(record$TI, record$AU, record$PY, record$SO)
      } else if (engine$name == "pubmed") {
        result <- search_doi_pubmed_optimized(record$TI, record$AU, record$PY, record$SO)
      }
      
      if (!is.null(result)) {
        # 记录成功和评分
        if (engine$name == "crossref") {
          training_results$crossref_success[i] <- TRUE
          training_results$crossref_score[i] <- result$final_score
        } else if (engine$name == "openalex") {
          training_results$openalex_success[i] <- TRUE
          training_results$openalex_score[i] <- result$final_score
        } else if (engine$name == "pubmed") {
          training_results$pubmed_success[i] <- TRUE
          training_results$pubmed_score[i] <- result$final_score
        }
        
        # 收集评分数据用于阈值分析
        all_scores[[engine$name]]$title_sim <- c(all_scores[[engine$name]]$title_sim, result$title_similarity)
        all_scores[[engine$name]]$journal_sim <- c(all_scores[[engine$name]]$journal_sim, result$journal_similarity)
        all_scores[[engine$name]]$year_sim <- c(all_scores[[engine$name]]$year_sim, result$year_similarity)
        all_scores[[engine$name]]$final_score <- c(all_scores[[engine$name]]$final_score, result$final_score)
        
        # 更新最佳引擎
        if (result$final_score > best_score) {
          best_score <- result$final_score
          best_engine <- engine$name
        }
        
        cat(sprintf("    ✅ 成功: %.3f\n", result$final_score))
      } else {
        cat(sprintf("    ❌ 失败\n"))
      }
      
      # 训练时使用较短间隔
      Sys.sleep(1)
    }
    
    training_results$best_engine[i] <- best_engine
    training_results$best_score[i] <- best_score
    
    # 每10个样本报告进度
    if (i %% 10 == 0) {
      current_success_rate <- 100 * sum(training_results$best_score[1:i] > 0) / i
      cat(sprintf("📊 当前成功率: %.1f%% (%d/%d)\n", current_success_rate, sum(training_results$best_score[1:i] > 0), i))
    }
  }
  
  return(list(
    training_results = training_results,
    score_distributions = all_scores
  ))
}

# === 分析和优化阈值 ===
analyze_and_optimize_thresholds <- function(training_output) {
  cat(sprintf("\n📈 分析训练结果并优化阈值\n"))
  
  training_results <- training_output$training_results
  score_distributions <- training_output$score_distributions
  
  # 总体统计
  total_samples <- nrow(training_results)
  crossref_success_count <- sum(training_results$crossref_success)
  openalex_success_count <- sum(training_results$openalex_success)
  pubmed_success_count <- sum(training_results$pubmed_success)
  
  cat(sprintf("📊 训练结果统计:\n"))
  cat(sprintf("  总样本数: %d\n", total_samples))
  cat(sprintf("  Crossref成功: %d (%.1f%%)\n", crossref_success_count, 100 * crossref_success_count / total_samples))
  cat(sprintf("  OpenAlex成功: %d (%.1f%%)\n", openalex_success_count, 100 * openalex_success_count / total_samples))
  cat(sprintf("  PubMed成功: %d (%.1f%%)\n", pubmed_success_count, 100 * pubmed_success_count / total_samples))
  
  # 分析每个引擎的评分分布
  optimized_thresholds <- list()
  
  for (engine in names(score_distributions)) {
    scores <- score_distributions[[engine]]
    
    if (length(scores$final_score) > 0) {
      cat(sprintf("\n🔧 %s 阈值优化:\n", toupper(engine)))
      
      # 计算统计量
      title_sim_stats <- summary(scores$title_sim)
      journal_sim_stats <- summary(scores$journal_sim)
      year_sim_stats <- summary(scores$year_sim)
      final_score_stats <- summary(scores$final_score)
      
      # 建议的优化阈值 (基于25%分位数，确保75%的成功案例能通过)
      optimized_thresholds[[engine]] <- list(
        title_threshold = max(0.5, quantile(scores$title_sim, 0.25)),
        journal_threshold = max(0.2, quantile(scores$journal_sim, 0.25)),
        year_threshold = max(0.1, quantile(scores$year_sim, 0.25)),
        final_threshold = max(0.4, quantile(scores$final_score, 0.25))
      )
      
      cat(sprintf("  建议标题阈值: %.3f (当前成功案例25%%分位: %.3f)\n", 
                  optimized_thresholds[[engine]]$title_threshold, quantile(scores$title_sim, 0.25)))
      cat(sprintf("  建议期刊阈值: %.3f (当前成功案例25%%分位: %.3f)\n", 
                  optimized_thresholds[[engine]]$journal_threshold, quantile(scores$journal_sim, 0.25)))
      cat(sprintf("  建议年份阈值: %.3f (当前成功案例25%%分位: %.3f)\n", 
                  optimized_thresholds[[engine]]$year_threshold, quantile(scores$year_sim, 0.25)))
      cat(sprintf("  建议综合阈值: %.3f (当前成功案例25%%分位: %.3f)\n", 
                  optimized_thresholds[[engine]]$final_threshold, quantile(scores$final_score, 0.25)))
    }
  }
  
  return(optimized_thresholds)
}

cat("✅ 数据分析与训练系统已加载\n")
cat("📋 主要函数:\n")
cat("  - load_and_analyze_data()           : 加载并分析全量数据\n")
cat("  - smart_doi_mesh_completion()       : 智能DOI-MeSH联合补全\n")
cat("  - train_optimal_thresholds()        : 训练优化阈值\n")
cat("  - analyze_and_optimize_thresholds()  : 分析并优化阈值\n")

# 自动执行数据分析
cat("\n🚀 开始数据分析...\n")
full_data <- load_and_analyze_data()

if (!is.null(full_data)) {
  cat("\n🤖 开始阈值训练 (使用50个样本)...\n")
  training_output <- train_optimal_thresholds(full_data, sample_size = 50)
  
  cat("\n📈 分析训练结果...\n")
  optimized_thresholds <- analyze_and_optimize_thresholds(training_output)
  
  # 保存训练结果
  write.csv(training_output$training_results, "data_repository/04_enhancement_reports/THRESHOLD_TRAINING_RESULTS.csv", row.names = FALSE)
  
  # 保存优化阈值
  saveRDS(optimized_thresholds, "DOI_COMPLETION_FINAL/01_CORE_SYSTEM/optimized_thresholds.rds")
  
  cat("\n✅ 训练完成！优化阈值已保存。\n")
} else {
  cat("❌ 数据加载失败，无法进行训练。\n")
}

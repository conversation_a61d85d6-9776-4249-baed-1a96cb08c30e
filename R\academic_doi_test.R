# 学术标准DOI补全系统 - 测试版本
# 测试前20条记录以验证算法有效性

cat("=== 学术标准DOI补全系统 - 测试版本 ===\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 学术标准的文本标准化函数
academic_normalize_text <- function(text) {
  if (is.na(text) || text == "") return("")
  text <- tolower(text)
  text <- gsub("[[:punct:]]", " ", text)
  text <- gsub("\\s+", " ", text)
  text <- trimws(text)
  
  # 移除停用词
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  words <- strsplit(text, "\\s+")[[1]]
  words <- words[!words %in% stop_words & nchar(words) > 2]
  
  return(paste(words, collapse = " "))
}

# 学术标准的相似度计算
academic_similarity <- function(text1, text2) {
  if (is.na(text1) || is.na(text2) || text1 == "" || text2 == "") return(0)
  
  norm1 <- academic_normalize_text(text1)
  norm2 <- academic_normalize_text(text2)
  
  if (norm1 == "" || norm2 == "") return(0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  return(similarity)
}

# 作者姓名匹配函数
match_authors <- function(authors1, authors2) {
  if (is.na(authors1) || is.na(authors2) || authors1 == "" || authors2 == "") return(0)
  
  extract_surnames <- function(author_string) {
    authors <- strsplit(author_string, "[;,]")[[1]]
    surnames <- c()
    
    for (author in authors[1:min(3, length(authors))]) {
      author <- trimws(author)
      words <- strsplit(author, "\\s+")[[1]]
      if (length(words) > 0) {
        surname <- toupper(words[1])
        if (nchar(surname) > 2) {
          surnames <- c(surnames, surname)
        }
      }
    }
    return(unique(surnames))
  }
  
  surnames1 <- extract_surnames(authors1)
  surnames2 <- extract_surnames(authors2)
  
  if (length(surnames1) == 0 || length(surnames2) == 0) return(0)
  
  matches <- sum(surnames1 %in% surnames2)
  total <- max(length(surnames1), length(surnames2))
  
  return(matches / total)
}

# 期刊匹配函数
match_journals <- function(journal1, journal2) {
  if (is.na(journal1) || is.na(journal2) || journal1 == "" || journal2 == "") return(0)
  
  normalize_journal <- function(journal) {
    journal <- tolower(journal)
    journal <- gsub("^the\\s+", "", journal)
    journal <- gsub("\\s*\\(.*\\)$", "", journal)
    journal <- gsub("[[:punct:]]", " ", journal)
    journal <- gsub("\\s+", " ", journal)
    return(trimws(journal))
  }
  
  norm1 <- normalize_journal(journal1)
  norm2 <- normalize_journal(journal2)
  
  if (norm1 == norm2) return(1.0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  return(similarity)
}

# 学术标准的DOI查询函数
academic_doi_search <- function(title, authors, year, journal) {
  tryCatch({
    clean_title <- academic_normalize_text(title)
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    
    # 只使用最重要的关键词
    keywords <- title_words[nchar(title_words) > 4]
    if (length(keywords) > 3) keywords <- keywords[1:3]
    if (length(keywords) == 0) return(NULL)
    
    query_string <- paste(keywords, collapse = " ")
    
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=10", 
                   URLencode(query_string), as.numeric(year)-1, as.numeric(year)+1)
    
    response <- GET(url, user_agent("AcademicDOI/1.0"), timeout(30))
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$message$items) || length(content$message$items) == 0) return(NULL)
    
    items <- content$message$items
    best_match <- NULL
    best_score <- 0
    
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      candidate_title <- if (!is.null(item$title) && length(item$title) > 0) item$title[[1]] else ""
      candidate_authors <- ""
      candidate_journal <- ""
      candidate_year <- ""
      
      if (!is.null(item$author) && nrow(item$author) > 0) {
        author_names <- paste(item$author$family, item$author$given, sep = " ")
        candidate_authors <- paste(author_names[1:min(3, length(author_names))], collapse = "; ")
      }
      
      if (!is.null(item$`container-title`) && length(item$`container-title`) > 0) {
        candidate_journal <- item$`container-title`[[1]]
      }
      
      if (!is.null(item$`published-print`$`date-parts`)) {
        candidate_year <- item$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(item$`published-online`$`date-parts`)) {
        candidate_year <- item$`published-online`$`date-parts`[[1]][[1]]
      }
      
      # 学术标准的多重验证
      title_sim <- academic_similarity(title, candidate_title)
      author_sim <- match_authors(authors, candidate_authors)
      journal_sim <- match_journals(journal, candidate_journal)
      year_match <- if (candidate_year == year) 1.0 else 0.0
      
      # 学术标准评分系统（严格）
      academic_score <- (title_sim * 0.4) + (author_sim * 0.25) + (journal_sim * 0.25) + (year_match * 0.1)
      
      # 调整后的学术标准（更实用但仍严格）
      if (title_sim >= 0.75 &&           # 标题相似度至少75%
          author_sim >= 0.3 &&           # 至少30%作者匹配
          journal_sim >= 0.5 &&          # 期刊相似度至少50%
          year_match == 1.0 &&           # 年份必须完全匹配
          academic_score > best_score) {
        
        best_score <- academic_score
        best_match <- list(
          doi = item$DOI,
          title = candidate_title,
          authors = candidate_authors,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          author_similarity = author_sim,
          journal_similarity = journal_sim,
          year_match = year_match,
          academic_score = academic_score,
          crossref_score = item$score
        )
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    return(NULL)
  })
}

# 测试处理函数
test_academic_process <- function() {
  cat("开始学术标准DOI补全测试...\n")
  
  input_file <- "data_repository/04_enhancement_reports/missing_doi_records.csv"
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  
  # 只处理前20条记录进行测试
  test_count <- min(20, nrow(data))
  cat(sprintf("测试前%d条记录\n", test_count))
  
  results <- data.frame(
    序号 = 1:test_count,
    UT = data$UT[1:test_count],
    原始标题 = data$TI[1:test_count],
    原始作者 = data$AU[1:test_count],
    原始年份 = data$PY[1:test_count],
    原始期刊 = data$SO[1:test_count],
    补全DOI = NA,
    匹配标题 = NA,
    匹配作者 = NA,
    匹配期刊 = NA,
    标题相似度 = NA,
    作者匹配度 = NA,
    期刊匹配度 = NA,
    学术评分 = NA,
    验证级别 = NA,
    补全状态 = "待处理",
    stringsAsFactors = FALSE
  )
  
  success_count <- 0
  
  for (i in 1:test_count) {
    cat(sprintf("处理第%d/%d条记录: %s\n", i, test_count, substr(data$TI[i], 1, 50)))
    
    match_result <- academic_doi_search(
      title = data$TI[i],
      authors = data$AU[i], 
      year = data$PY[i],
      journal = data$SO[i]
    )
    
    if (!is.null(match_result)) {
      results$补全DOI[i] <- match_result$doi
      results$匹配标题[i] <- match_result$title
      results$匹配作者[i] <- match_result$authors
      results$匹配期刊[i] <- match_result$journal
      results$标题相似度[i] <- round(match_result$title_similarity, 3)
      results$作者匹配度[i] <- round(match_result$author_similarity, 3)
      results$期刊匹配度[i] <- round(match_result$journal_similarity, 3)
      results$学术评分[i] <- round(match_result$academic_score, 3)
      results$补全状态[i] <- "成功"
      
      # 确定验证级别
      if (match_result$title_similarity >= 0.95 && 
          match_result$author_similarity >= 0.7 && 
          match_result$journal_similarity >= 0.9) {
        results$验证级别[i] <- "A级-极高置信度"
      } else if (match_result$title_similarity >= 0.9 && 
                 match_result$author_similarity >= 0.6 && 
                 match_result$journal_similarity >= 0.8) {
        results$验证级别[i] <- "B级-高置信度"
      } else {
        results$验证级别[i] <- "C级-可接受"
      }
      
      success_count <- success_count + 1
      cat(sprintf("  ✓ 成功匹配 (学术评分: %.3f, 级别: %s)\n", 
                  match_result$academic_score, results$验证级别[i]))
    } else {
      results$补全状态[i] <- "未找到符合学术标准的匹配"
      results$验证级别[i] <- "未补全"
      cat("  ✗ 未找到符合学术标准的匹配\n")
    }
    
    Sys.sleep(1.5)
  }
  
  # 统计结果
  success_rate <- 100 * success_count / test_count
  
  cat(sprintf("\n=== 学术标准测试结果 ===\n"))
  cat(sprintf("测试记录数: %d\n", test_count))
  cat(sprintf("成功补全: %d (%.1f%%)\n", success_count, success_rate))
  cat(sprintf("A级极高置信度: %d\n", sum(results$验证级别 == "A级-极高置信度", na.rm = TRUE)))
  cat(sprintf("B级高置信度: %d\n", sum(results$验证级别 == "B级-高置信度", na.rm = TRUE)))
  cat(sprintf("C级可接受: %d\n", sum(results$验证级别 == "C级-可接受", na.rm = TRUE)))
  
  # 保存测试结果
  output_file <- "data_repository/04_enhancement_reports/academic_doi_test_results.csv"
  write.csv(results, output_file, row.names = FALSE)
  cat(sprintf("\n测试结果已保存: %s\n", output_file))
  
  # 显示成功的匹配示例
  successful_results <- results[results$补全状态 == "成功", ]
  if (nrow(successful_results) > 0) {
    cat(sprintf("\n=== 成功匹配示例 ===\n"))
    for (i in 1:min(5, nrow(successful_results))) {
      record <- successful_results[i, ]
      cat(sprintf("\n示例 %d (%s):\n", i, record$验证级别))
      cat(sprintf("原始标题: %s\n", substr(record$原始标题, 1, 80)))
      cat(sprintf("匹配标题: %s\n", substr(record$匹配标题, 1, 80)))
      cat(sprintf("DOI: %s\n", record$补全DOI))
      cat(sprintf("评分: 标题%.3f, 作者%.3f, 期刊%.3f, 学术%.3f\n", 
                  record$标题相似度, record$作者匹配度, record$期刊匹配度, record$学术评分))
    }
  }
  
  return(results)
}

# 执行测试
results <- test_academic_process()

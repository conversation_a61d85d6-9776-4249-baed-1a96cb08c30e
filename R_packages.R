# R包依赖管理
# 版本: 1.0.0

# 加载必要的包
required_packages <- c(
  "tidyverse",    # 数据处理和可视化
  "bibliometrix", # 文献计量分析
  "igraph",       # 网络分析
  "ggplot2",      # 数据可视化
  "dplyr",        # 数据处理
  "stringr",      # 字符串处理
  "lubridate",    # 日期处理
  "jsonlite",     # JSON处理
  "yaml",         # YAML处理
  "testthat",     # 单元测试
  "roxygen2",     # 文档生成
  "devtools"      # 开发工具
)

# 检查并安装缺失的包
missing_packages <- required_packages[!required_packages %in% installed.packages()[,"Package"]]
if (length(missing_packages) > 0) {
  install.packages(missing_packages)
}

# 加载所有必要的包
for (pkg in required_packages) {
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 初始化renv（如果存在）
if ("renv" %in% installed.packages()[,"Package"]) {
  library(renv)
  renv::init()
  renv::snapshot()
}

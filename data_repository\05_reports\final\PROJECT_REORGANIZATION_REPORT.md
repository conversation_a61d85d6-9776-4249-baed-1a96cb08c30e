# 项目重组报告

## 重组时间
2025-06-19 17:11:01

## 新的项目结构

### 数据目录
- `01_raw_data/`: 原始WoS数据文件
- `02_baseline_data/`: bibliometrix、CiteSpace、VOSviewer基线处理结果
- `03_enhanced_data/`: 去重、DOI补全等增强处理结果
- `04_analysis_outputs/`: 网络、趋势、合作等分析结果
- `05_reports/`: 各类处理和分析报告
- `06_cache/`: API缓存和临时文件
- `07_logs/`: 处理日志和错误记录

### R脚本
- `01_data_import.R`: 数据导入与转换
- `04_deduplication.R`: 去重处理
- `05_doi_completion.R`: DOI补全
- `07_quality_control.R`: 质量控制
- `config.R`: 项目配置文件

### 配置管理
- 统一的配置文件管理所有参数
- 标准化的文件命名规范
- 清晰的路径管理

## 主要改进

1. **结构清晰**: 按照处理流程组织目录结构
2. **命名规范**: 统一的文件和目录命名
3. **配置统一**: 集中管理所有配置参数
4. **代码精简**: 移除重复和过时代码
5. **文档完善**: 更新框架文档和使用指南

## 使用方法

```r
# 加载配置
source("R/config.R")

# 获取路径
raw_data_path <- get_path("raw_data")

# 获取配置参数
dedup_params <- get_config("processing", "deduplication")
```

## 注意事项

1. 旧的目录结构已保留，确保数据安全
2. 重复的脚本已归档到 `R/archive/`
3. 所有配置参数现在统一管理
4. 建议逐步迁移到新的结构

重组完成！项目现在具有更清晰的结构和更好的可维护性。


# PubMed DOI补全系统 - 简化版本
# 专注生物医学文献的DOI补全

library(httr)
library(jsonlite)
library(stringdist)

# 加载核心函数
source("doi_completion_core.R")

cat("=== PubMed DOI补全系统 (简化版) ===\n")
cat("🎯 专注生物医学文献DOI补全\n")
cat("📊 数据源: PubMed E-utilities API\n\n")

# === 简化的PubMed DOI搜索函数 ===
search_doi_pubmed_simple <- function(title, authors, year, journal) {
  tryCatch({
    # 构建简单的查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    # 构建PubMed查询
    search_terms <- paste(keywords, collapse = " AND ")
    
    # Step 1: 简单搜索
    esearch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=%s&retmax=10&retmode=json",
                          URLencode(search_terms))
    
    cat(sprintf("PubMed搜索: %s\n", search_terms))
    
    search_response <- GET(esearch_url, 
                          user_agent("DOI_Completion_PubMed_Simple/1.0"),
                          timeout(20))
    
    if (status_code(search_response) != 200) {
      cat("PubMed搜索失败, 状态码:", status_code(search_response), "\n")
      return(NULL)
    }
    
    search_content <- fromJSON(rawToChar(search_response$content))
    
    if (is.null(search_content$esearchresult$idlist) || 
        length(search_content$esearchresult$idlist) == 0) {
      cat("PubMed未返回搜索结果\n")
      return(NULL)
    }
    
    pmids <- search_content$esearchresult$idlist
    cat(sprintf("PubMed返回 %d 个PMID\n", length(pmids)))
    
    # Step 2: 获取详细信息
    pmid_list <- paste(pmids[1:min(5, length(pmids))], collapse = ",")  # 限制为前5个
    esummary_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=pubmed&id=%s&retmode=json",
                           pmid_list)
    
    summary_response <- GET(esummary_url,
                           user_agent("DOI_Completion_PubMed_Simple/1.0"),
                           timeout(20))
    
    if (status_code(summary_response) != 200) {
      cat("PubMed详情获取失败, 状态码:", status_code(summary_response), "\n")
      return(NULL)
    }
    
    summary_content <- fromJSON(rawToChar(summary_response$content))
    
    if (is.null(summary_content$result)) {
      cat("PubMed详情为空\n")
      return(NULL)
    }
    
    best_match <- NULL
    best_score <- 0
    
    # 简化的结果评估
    for (pmid in pmids[1:min(5, length(pmids))]) {
      if (pmid %in% names(summary_content$result) && pmid != "uids") {
        item <- summary_content$result[[pmid]]
        
        # 安全提取数据
        candidate_title <- ""
        candidate_journal <- ""
        candidate_year <- ""
        candidate_doi <- ""
        
        # 提取标题
        if (!is.null(item) && is.list(item) && "title" %in% names(item)) {
          candidate_title <- as.character(item$title)[1]
        }
        
        # 提取期刊
        if (!is.null(item) && is.list(item)) {
          if ("fulljournalname" %in% names(item)) {
            candidate_journal <- as.character(item$fulljournalname)[1]
          } else if ("source" %in% names(item)) {
            candidate_journal <- as.character(item$source)[1]
          }
        }
        
        # 提取年份
        if (!is.null(item) && is.list(item) && "pubdate" %in% names(item)) {
          pubdate_str <- as.character(item$pubdate)[1]
          year_match <- regmatches(pubdate_str, regexpr("\\d{4}", pubdate_str))
          if (length(year_match) > 0) {
            candidate_year <- year_match[1]
          }
        }
        
        # 提取DOI - 简化版本
        if (!is.null(item) && is.list(item) && "articleids" %in% names(item)) {
          articleids <- item$articleids
          if (is.list(articleids)) {
            for (aid in articleids) {
              if (is.list(aid) && "idtype" %in% names(aid) && "value" %in% names(aid)) {
                if (as.character(aid$idtype) == "doi") {
                  candidate_doi <- as.character(aid$value)
                  break
                }
              }
            }
          }
        }
        
        # 如果没有DOI，跳过
        if (candidate_doi == "" || is.na(candidate_doi)) {
          cat(sprintf("PMID %s: 无DOI，跳过\n", pmid))
          next
        }
        
        # 简化的相似度计算
        title_sim <- calculate_title_similarity(title, candidate_title)
        journal_sim <- calculate_journal_similarity(journal, candidate_journal)
        year_sim <- calculate_year_similarity(year, candidate_year)
        
        # 生物医学相关性检查
        bio_keywords <- c("medical", "clinical", "patient", "disease", "therapy", "treatment", 
                         "health", "medicine", "biology", "molecular", "cell", "tissue")
        title_lower <- tolower(title)
        candidate_lower <- tolower(candidate_title)
        
        bio_rel <- 0.5  # 默认值
        if (any(sapply(bio_keywords, function(x) grepl(x, title_lower))) &&
            any(sapply(bio_keywords, function(x) grepl(x, candidate_lower)))) {
          bio_rel <- 1.0
        } else if (any(sapply(bio_keywords, function(x) grepl(x, title_lower))) ||
                   any(sapply(bio_keywords, function(x) grepl(x, candidate_lower)))) {
          bio_rel <- 0.7
        }
        
        # 综合评分
        final_score <- (title_sim * 0.6) + (journal_sim * 0.2) + 
                       (year_sim * 0.1) + (bio_rel * 0.1)
        
        cat(sprintf("PMID %s: T=%.3f, J=%.3f, Y=%.3f, B=%.3f, 总分=%.3f\n",
                    pmid, title_sim, journal_sim, year_sim, bio_rel, final_score))
        
        # 宽松的接受条件
        if (title_sim >= 0.65 &&           # 降低标题阈值
            journal_sim >= 0.25 &&         # 降低期刊阈值
            year_sim >= 0.20 &&            # 降低年份阈值
            final_score >= 0.55 &&         # 降低综合阈值
            final_score > best_score) {
          
          best_score <- final_score
          best_match <- list(
            doi = candidate_doi,
            title = candidate_title,
            journal = candidate_journal,
            year = candidate_year,
            pmid = pmid,
            title_similarity = title_sim,
            journal_similarity = journal_sim,
            year_similarity = year_sim,
            biomedical_relevance = bio_rel,
            final_score = final_score,
            source = "pubmed"
          )
          
          cat(sprintf("  ✅ 新的最佳匹配! DOI: %s (PMID: %s)\n", candidate_doi, pmid))
        }
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("PubMed API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 测试PubMed简化系统 ===
test_pubmed_simple <- function() {
  cat("\n=== PubMed简化系统测试 ===\n")
  
  # 明确的生物医学测试用例
  test_cases <- list(
    list(
      title = "Clinical trial of new cancer treatment",
      authors = "Smith J",
      year = 2020,
      journal = "New England Journal of Medicine",
      description = "癌症临床试验"
    ),
    list(
      title = "Molecular biology of heart disease",
      authors = "Johnson A",
      year = 2019,
      journal = "Circulation",
      description = "心脏病分子生物学"
    ),
    list(
      title = "Artificial intelligence in medical diagnosis",
      authors = "Brown C",
      year = 2021,
      journal = "Nature Medicine",
      description = "AI医疗诊断"
    )
  )
  
  success_count <- 0
  
  for (i in 1:length(test_cases)) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n--- 测试案例 %d: %s ---\n", i, test_case$description))
    cat(sprintf("标题: %s\n", test_case$title))
    
    result <- search_doi_pubmed_simple(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal
    )
    
    if (!is.null(result)) {
      success_count <- success_count + 1
      quality <- assess_quality(result$title_similarity, result$final_score)
      cat(sprintf("✅ PubMed成功: %s (PMID: %s, 质量: %s)\n", 
                  result$doi, result$pmid, quality))
    } else {
      cat("❌ PubMed未找到匹配\n")
    }
    
    if (i < length(test_cases)) {
      cat("等待3秒...\n")
      Sys.sleep(3)
    }
  }
  
  success_rate <- 100 * success_count / length(test_cases)
  cat(sprintf("\n📊 PubMed简化系统结果: %.1f%% 成功率 (%d/%d)\n", 
              success_rate, success_count, length(test_cases)))
  
  return(list(
    success_count = success_count,
    success_rate = success_rate
  ))
}

cat("✅ PubMed简化DOI补全系统已加载\n")
cat("📋 主要函数:\n")
cat("  - search_doi_pubmed_simple()  : 简化PubMed DOI搜索\n")
cat("  - test_pubmed_simple()        : 简化系统测试\n")

# 自动执行测试
cat("\n🚀 开始PubMed简化系统测试...\n")
pubmed_result <- test_pubmed_simple()

cat(sprintf("\n🎯 PubMed简化系统测试完成!\n"))
cat(sprintf("成功率: %.1f%%\n", pubmed_result$success_rate))

if (pubmed_result$success_rate >= 33.3) {
  cat("🎉 PubMed表现良好，可以集成到三引擎系统！\n")
} else if (pubmed_result$success_rate > 0) {
  cat("📈 PubMed有一定效果，可作为生物医学文献的专用补充。\n")
} else {
  cat("⚠️  PubMed需要进一步优化，但仍可作为生物医学领域的专用引擎。\n")
}

cat("\n💡 建议: PubMed最适合作为生物医学文献的专用引擎，与Crossref和OpenAlex形成互补。\n")

# 修复版本的数据增强框架
# 专门解决Crossref API问题

# 加载必要的包
required_packages <- c("here", "dplyr", "stringr", "bibliometrix", "rcrossref", "progress")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

# 配置
config <- list(
  input_file = here("data_repository", "02_enhanced_dataset", "enhanced_data_initial.rds"),
  output_file = here("data_repository", "02_enhanced_dataset", "enhanced_data_optimized.rds"),
  use_crossref_api = TRUE,
  throttle_delay = 1.0  # 增加延迟以避免API限制
)

# 改进的字符串相似度计算
improved_string_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  
  # 转换为小写并移除标点符号
  str1_clean <- tolower(gsub("[[:punct:]]", " ", str1))
  str2_clean <- tolower(gsub("[[:punct:]]", " ", str2))
  
  # 移除多余空格
  str1_clean <- gsub("\\s+", " ", trimws(str1_clean))
  str2_clean <- gsub("\\s+", " ", trimws(str2_clean))
  
  # 方法1：编辑距离相似度
  distance <- adist(str1_clean, str2_clean)[1, 1]
  max_len <- max(nchar(str1_clean), nchar(str2_clean))
  edit_similarity <- if (max_len == 0) 1 else 1 - (distance / max_len)
  
  # 方法2：词汇重叠相似度
  words1 <- strsplit(str1_clean, "\\s+")[[1]]
  words2 <- strsplit(str2_clean, "\\s+")[[1]]
  
  # 移除停用词
  stopwords <- c("a", "an", "the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  words1 <- words1[!words1 %in% stopwords & nchar(words1) > 2]
  words2 <- words2[!words2 %in% stopwords & nchar(words2) > 2]
  
  if (length(words1) == 0 || length(words2) == 0) {
    word_similarity <- 0
  } else {
    common_words <- length(intersect(words1, words2))
    total_words <- length(union(words1, words2))
    word_similarity <- common_words / total_words
  }
  
  # 综合相似度：编辑距离权重0.3，词汇重叠权重0.7
  combined_similarity <- 0.3 * edit_similarity + 0.7 * word_similarity
  
  return(combined_similarity)
}

# 改进的DOI查找函数
search_doi_by_title_improved <- function(title, authors = NULL, year = NULL) {
  tryCatch({
    # 清理标题
    clean_title <- gsub("[[:punct:]]", " ", title)
    clean_title <- gsub("\\s+", " ", clean_title)
    clean_title <- trimws(clean_title)
    
    # 处理作者信息
    rcr_query_author <- NULL
    if (!is.null(authors) && !is.na(authors) && authors != "") {
      # 分割作者列表
      author_list <- strsplit(authors, ";")[[1]]
      if (length(author_list) > 0) {
        first_author <- trimws(author_list[1])
        
        # 尝试不同的作者格式解析
        if (grepl(",", first_author)) {
          # 格式：Last, First Middle
          author_parts <- strsplit(first_author, ",")[[1]]
          if (length(author_parts) >= 2) {
            last_name <- trimws(author_parts[1])
            first_name <- trimws(author_parts[2])
            # 提取名字的首字母
            first_initial <- substr(first_name, 1, 1)
            rcr_query_author <- paste(first_initial, last_name)
          }
        } else {
          # 格式：First Middle Last 或 First Last
          author_parts <- strsplit(first_author, "\\s+")[[1]]
          if (length(author_parts) >= 2) {
            # 假设最后一个是姓氏，第一个是名字
            first_name <- author_parts[1]
            last_name <- author_parts[length(author_parts)]
            # 如果名字长度大于1，取首字母
            if (nchar(first_name) > 1) {
              first_initial <- substr(first_name, 1, 1)
              rcr_query_author <- paste(first_initial, last_name)
            } else {
              rcr_query_author <- paste(first_name, last_name)
            }
          } else if (length(author_parts) == 1 && nchar(author_parts[1]) > 3) {
            rcr_query_author <- author_parts[1]
          }
        }
      }
    }
    
    # 年份过滤
    rcr_filter <- NULL
    if (!is.null(year) && !is.na(year)) {
      # 扩大年份范围以增加匹配可能性
      year_start <- max(1900, as.numeric(year) - 2)
      year_end <- min(as.numeric(format(Sys.Date(), "%Y")), as.numeric(year) + 2)
      rcr_filter <- c(from_pub_date = as.character(year_start), until_pub_date = as.character(year_end))
    }
    
    log_message(sprintf("查询: '%s'", substr(clean_title, 1, 50)))
    
    # 尝试不同的查询策略
    crossref_result <- NULL
    
    # 策略1：使用关键词查询（更宽泛）
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    # 选择重要的词汇（长度大于3的词）
    important_words <- title_words[nchar(title_words) > 3]
    if (length(important_words) > 5) {
      important_words <- important_words[1:5]  # 只取前5个重要词
    }
    
    if (length(important_words) > 0) {
      query_string <- paste(important_words, collapse = " ")
      log_message(sprintf("策略1 - 关键词查询: '%s'", query_string))
      
      crossref_result <- rcrossref::cr_works(
        query = query_string,
        filter = rcr_filter,
        limit = 10
      )
    }
    
    # 策略2：如果策略1没有结果，尝试标题查询
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("策略2 - 标题查询")
      crossref_result <- rcrossref::cr_works(
        query.title = clean_title,
        filter = rcr_filter,
        limit = 10
      )
    }
    
    # 策略3：如果前两个策略都失败，尝试更简单的查询
    if (is.null(crossref_result) || nrow(crossref_result$data) == 0) {
      log_message("策略3 - 简化查询")
      # 只使用前3个重要词
      if (length(important_words) >= 3) {
        simple_query <- paste(important_words[1:3], collapse = " ")
      } else {
        simple_query <- paste(title_words[1:min(3, length(title_words))], collapse = " ")
      }
      
      crossref_result <- rcrossref::cr_works(
        query = simple_query,
        limit = 10
      )
    }
    
    # 分析结果
    if (!is.null(crossref_result$data) && nrow(crossref_result$data) > 0) {
      log_message(sprintf("找到 %d 个候选结果", nrow(crossref_result$data)))
      
      best_match_idx <- NULL
      best_similarity <- 0
      
      for (i in 1:min(5, nrow(crossref_result$data))) {  # 只检查前5个结果
        candidate_title <- crossref_result$data$title[i]
        if (is.null(candidate_title) || is.na(candidate_title) || candidate_title == "") {
          next
        }
        
        current_similarity <- improved_string_similarity(title, candidate_title)
        
        if (current_similarity > best_similarity) {
          best_similarity <- current_similarity
          best_match_idx <- i
        }
      }
      
      # 使用更宽松的阈值
      similarity_threshold <- 0.25
      
      if (!is.null(best_match_idx) && best_similarity > similarity_threshold) {
        doi_found <- crossref_result$data$doi[best_match_idx]
        if (!is.na(doi_found) && doi_found != "") {
          log_message(sprintf("成功找到DOI: %s (相似度: %.2f)", doi_found, best_similarity))
          return(doi_found)
        }
      } else {
        log_message(sprintf("最佳相似度 %.2f 低于阈值 %.2f", best_similarity, similarity_threshold))
      }
    } else {
      log_message("未找到任何候选结果")
    }
    
    return(NA_character_)
  }, error = function(e) {
    log_message(sprintf("查询过程发生错误: %s", e$message), "error")
    return(NA_character_)
  })
}

# 主函数
main <- function() {
  log_message("开始修复版数据增强流程")
  
  # 1. 加载数据
  if (!file.exists(config$input_file)) {
    stop(sprintf("输入文件不存在: %s", config$input_file))
  }
  
  M <- readRDS(config$input_file)
  log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
  
  # 2. 查找缺失DOI的记录
  missing_doi_records <- M[is.na(M$DI) | M$DI == "", ]
  log_message(sprintf("缺失DOI的记录数: %d", nrow(missing_doi_records)))
  
  if (nrow(missing_doi_records) > 0) {
    # 限制处理数量进行测试
    max_records_to_process <- min(nrow(missing_doi_records), 5)
    records_to_process <- missing_doi_records[1:max_records_to_process, ]
    
    log_message(sprintf("测试处理前%d个缺失DOI的记录", max_records_to_process))
    
    # 创建进度条
    pb <- progress_bar$new(
      format = "[:bar] :percent 已完成 (:current/:total)",
      total = nrow(records_to_process),
      clear = FALSE,
      width = 60
    )
    
    doi_found_count <- 0
    
    for (i in 1:nrow(records_to_process)) {
      record <- records_to_process[i, ]
      pb$tick()
      
      log_message(sprintf("\n=== 处理记录 %d/%d ===", i, nrow(records_to_process)))
      
      found_doi <- NA_character_
      if (!is.na(record$TI) && record$TI != "") {
        found_doi <- search_doi_by_title_improved(record$TI, record$AU, record$PY)
      }
      
      if (!is.na(found_doi) && found_doi != "") {
        # 更新原始数据
        original_row_index <- which(M$UT == record$UT)
        if (length(original_row_index) > 0) {
          M$DI[original_row_index] <- found_doi
          doi_found_count <- doi_found_count + 1
          log_message(sprintf("成功为记录 %s 补全DOI", record$UT))
        }
      }
      
      # 防止API限制
      Sys.sleep(config$throttle_delay)
    }
    
    log_message(sprintf("\nDOI补全完成: 成功找到并补全了 %d 个新DOI", doi_found_count))
  } else {
    log_message("没有缺失DOI的记录需要补全")
  }
  
  # 3. 保存结果
  saveRDS(M, config$output_file)
  log_message(sprintf("增强数据已保存: %s", config$output_file))
  
  log_message("修复版数据增强流程完成")
  
  return(M)
}

# 运行主函数
if (interactive()) {
  enhanced_data <- main()
} else {
  enhanced_data <- main()
}

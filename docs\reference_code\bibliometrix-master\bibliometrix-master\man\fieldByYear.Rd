% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/fieldByYear.R
\name{fieldByYear}
\alias{fieldByYear}
\title{Field Tag distribution by Year}
\usage{
fieldByYear(
  M,
  field = "ID",
  timespan = NULL,
  min.freq = 2,
  n.items = 5,
  labelsize = NULL,
  remove.terms = NULL,
  synonyms = NULL,
  dynamic.plot = FALSE,
  graph = TRUE
)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by \code{\link{convert2df}} function.}

\item{field}{is a character object. It indicates one of the field tags of the
standard ISI WoS Field Tag codify.}

\item{timespan}{is a vector with the min and max year. If it is = NULL, the analysis is performed on the entire period. Default is \code{timespan = NULL}.}

\item{min.freq}{is an integer. It indicates the min frequency of the items to include in the analysis}

\item{n.items}{is an integer. I indicates the maximum number of items per year to include in the plot.}

\item{labelsize}{is deprecated argument. It will be removed in the next update.}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}

\item{dynamic.plot}{is a logical. If TRUE plot aesthetics are optimized for plotly package.}

\item{graph}{is logical. If TRUE the function plots Filed Tag distribution by Year graph. Default is \code{graph = TRUE}.}
}
\value{
The function \code{fieldByYear} returns a list containing threeobjects:
\tabular{lll}{
\code{df}  \tab   \tab is a data frame\cr
\code{df_graph}\tab    \tab is a data frame with data used to build the graph\cr
\code{graph}   \tab   \tab a ggplot object}
}
\description{
It calculates the median year for each item of a field tag.
}
\examples{
data(management, package = "bibliometrixData")
timespan=c(2005,2015)
res <- fieldByYear(management, field = "ID", timespan = timespan, 
                    min.freq = 5, n.items = 5, graph = TRUE)

}
\seealso{
\code{\link{biblioAnalysis}} function for bibliometric analysis

\code{\link{summary}} method for class '\code{bibliometrix}'
}

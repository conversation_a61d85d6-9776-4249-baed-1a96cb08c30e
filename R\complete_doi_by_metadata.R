# 加载必要的包
library(tidyverse)
library(here)
library(httr)
library(jsonlite)
library(openxlsx)
library(stringdist)  # 用于字符串相似度计算

# 设置日志函数
log_message <- function(message, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[%s] [%s] %s\n", timestamp, toupper(type), message))
}

# 数据标准化函数
normalize_text <- function(text) {
  if (is.na(text)) return(NA)
  # 转为小写
  text <- tolower(text)
  # 移除标点符号
  text <- gsub("[[:punct:]]", " ", text)
  # 处理空格
  text <- gsub("\\s+", " ", text)
  text <- trimws(text)
  return(text)
}

# 改进标题标准化
normalize_title <- function(title) {
  if (is.na(title)) return(NA)
  # 基础标准化
  title <- tolower(title)
  # 移除更多特殊字符
  title <- gsub("[[:punct:]]", " ", title)
  # 移除常见词
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  title <- gsub(paste0("\\b(", paste(stop_words, collapse="|"), ")\\b"), "", title)
  # 处理空格
  title <- gsub("\\s+", " ", title)
  title <- trimws(title)
  return(title)
}

# 改进作者标准化
normalize_author <- function(author) {
  if (is.na(author)) return(NA)
  # 基础标准化
  author <- tolower(author)
  # 移除特殊字符
  author <- gsub("[[:punct:]]", " ", author)
  # 处理空格
  author <- gsub("\\s+", " ", author)
  author <- trimws(author)
  # 提取所有作者姓氏
  authors <- strsplit(author, ",")[[1]]
  authors <- sapply(authors, function(x) {
    parts <- strsplit(x, " ")[[1]]
    return(parts[length(parts)])  # 取最后一个词作为姓氏
  })
  return(paste(authors, collapse=","))
}

# 改进期刊标准化
normalize_journal <- function(journal) {
  if (is.na(journal)) return(NA)
  # 基础标准化
  journal <- tolower(journal)
  # 移除特殊字符
  journal <- gsub("[[:punct:]]", " ", journal)
  # 处理空格
  journal <- gsub("\\s+", " ", journal)
  journal <- trimws(journal)
  return(journal)
}

# 改进相似度计算
calculate_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  # 使用Jaro-Winkler距离计算相似度
  return(1 - stringdist(str1, str2, method="jw"))
}

# 网络连接检查函数
check_network_connection <- function() {
  tryCatch({
    response <- GET("https://api.crossref.org/", timeout(10))
    return(status_code(response) == 200)
  }, error = function(e) {
    return(FALSE)
  })
}

# 带重试机制的API请求函数
safe_api_request <- function(url, max_retries = 3, retry_delay = 5) {
  for (attempt in 1:max_retries) {
    tryCatch({
      response <- GET(url, user_agent("BiblioEnhancer/1.0 (mailto:<EMAIL>)"), timeout(30))
      if (status_code(response) == 200) {
        return(response)
      } else {
        log_message(sprintf("API请求失败，状态码: %d，尝试 %d/%d", status_code(response), attempt, max_retries), "warning")
      }
    }, error = function(e) {
      log_message(sprintf("网络请求错误: %s，尝试 %d/%d", e$message, attempt, max_retries), "warning")
    })

    if (attempt < max_retries) {
      log_message(sprintf("等待 %d 秒后重试...", retry_delay), "info")
      Sys.sleep(retry_delay)
    }
  }
  return(NULL)
}

# 策略一：黄金标准查询（增强版）
get_doi_gold <- function(title, author, year, journal, debug=FALSE) {
  # 检查网络连接
  if (!check_network_connection()) {
    log_message("网络连接检查失败，跳过API查询", "warning")
    return(list(doi = NA, score = 0))
  }

  url <- sprintf("https://api.crossref.org/works?query.bibliographic=%s&query.author=%s&filter=from-pub-date:%s,until-pub-date:%s&query.container-title=%s&rows=2",
                 URLencode(title), URLencode(author), year, year, URLencode(journal))

  response <- safe_api_request(url)
  if (is.null(response)) return(list(doi = NA, score = 0))

  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) { return(list(doi = NA, score = 0)) })
  # 输出原始内容
  if (debug && !is.null(content$message$items) && length(content$message$items) > 0) {
    log_message("[DEBUG] 黄金标准API返回前2条:")
    for (i in 1:min(2, nrow(content$message$items))) {
      item <- content$message$items[i,]
      log_message(sprintf("  %d. 标题: %s | 作者: %s | 期刊: %s | DOI: %s | score: %.2f",
        i, item$title[[1]],
        ifelse(!is.null(item$author), paste(sapply(item$author, function(a) a$family), collapse=","), "NA"),
        ifelse(!is.null(item$`container-title`), item$`container-title`[1], "NA"),
        item$DOI, item$score))
    }
  }
  if (!is.null(content$message$items) && length(content$message$items) > 0) {
    item <- content$message$items[1,]
    # 进一步降低阈值到25
    if (item$score >= 25) {
      return(list(
        doi = item$DOI,
        score = item$score,
        journal = item$`container-title`[1],
        volume = item$volume,
        issue = item$issue,
        page = item$page
      ))
    }
  }
  return(list(doi = NA, score = 0))
}

# 策略二：白银标准查询
get_doi_silver <- function(title, author, year, debug=FALSE) {
  url <- sprintf("https://api.crossref.org/works?query.bibliographic=%s&query.author=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=2", 
                 URLencode(title), URLencode(author), year, year)
  response <- tryCatch({
    GET(url, user_agent("BiblioEnhancer/1.0 (mailto:<EMAIL>)"))
  }, error = function(e) { return(NULL) })
  if (is.null(response) || status_code(response) != 200) return(NULL)
  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) { return(NULL) })
  # 输出原始内容
  if (debug && !is.null(content$message$items) && length(content$message$items) > 0) {
    log_message("[DEBUG] 白银标准API返回前2条:")
    for (i in 1:min(2, nrow(content$message$items))) {
      item <- content$message$items[i,]
      log_message(sprintf("  %d. 标题: %s | 作者: %s | 期刊: %s | DOI: %s | score: %.2f",
        i, item$title[[1]],
        ifelse(!is.null(item$author), paste(sapply(item$author, function(a) a$family), collapse=","), "NA"),
        ifelse(!is.null(item$`container-title`), item$`container-title`[1], "NA"),
        item$DOI, item$score))
    }
  }
  if (!is.null(content$message$items) && length(content$message$items) > 0) {
    return(content$message$items)
  }
  return(NULL)
}

# 策略三：青铜标准查询
get_doi_bronze <- function(title, year, journal, debug=FALSE) {
  url <- sprintf("https://api.crossref.org/works?query.bibliographic=%s&filter=from-pub-date:%s,until-pub-date:%s&query.container-title=%s&rows=5",
                 URLencode(title), year, year, URLencode(journal))
  response <- tryCatch({
    GET(url, user_agent("BiblioEnhancer/1.0 (mailto:<EMAIL>)"))
  }, error = function(e) { return(NULL) })
  if (is.null(response) || status_code(response) != 200) return(NULL)
  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) { return(NULL) })
  # 输出原始内容
  if (debug && !is.null(content$message$items) && length(content$message$items) > 0) {
    log_message("[DEBUG] 青铜标准API返回前2条:")
    for (i in 1:min(2, nrow(content$message$items))) {
      item <- content$message$items[i,]
      log_message(sprintf("  %d. 标题: %s | 作者: %s | 期刊: %s | DOI: %s | score: %.2f",
        i, item$title[[1]],
        ifelse(!is.null(item$author), paste(sapply(item$author, function(a) a$family), collapse=","), "NA"),
        ifelse(!is.null(item$`container-title`), item$`container-title`[1], "NA"),
        item$DOI, item$score))
    }
  }
  if (!is.null(content$message$items) && length(content$message$items) > 0) {
    return(content$message$items)
  }
  return(NULL)
}

# 策略四：关键词查询（新增）
get_doi_keywords <- function(title, year, debug=FALSE) {
  # 提取标题中的关键词
  title_words <- strsplit(normalize_title(title), "\\s+")[[1]]
  # 选择长度大于3的重要词汇
  keywords <- title_words[nchar(title_words) > 3]
  if (length(keywords) > 5) keywords <- keywords[1:5]

  if (length(keywords) == 0) return(NULL)

  query_string <- paste(keywords, collapse = " ")
  url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=10",
                 URLencode(query_string), as.numeric(year)-1, as.numeric(year)+1)

  response <- tryCatch({
    GET(url, user_agent("BiblioEnhancer/1.0 (mailto:<EMAIL>)"))
  }, error = function(e) { return(NULL) })
  if (is.null(response) || status_code(response) != 200) return(NULL)
  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) { return(NULL) })

  if (debug && !is.null(content$message$items) && length(content$message$items) > 0) {
    log_message(sprintf("[DEBUG] 关键词查询 '%s' 返回前3条:", query_string))
    for (i in 1:min(3, nrow(content$message$items))) {
      item <- content$message$items[i,]
      log_message(sprintf("  %d. 标题: %s | DOI: %s | score: %.2f",
        i, substr(item$title[[1]], 1, 60), item$DOI, item$score))
    }
  }

  if (!is.null(content$message$items) && length(content$message$items) > 0) {
    return(content$message$items)
  }
  return(NULL)
}

# 策略五：模糊标题查询（新增）
get_doi_fuzzy_title <- function(title, debug=FALSE) {
  # 简化标题，只保留核心词汇
  simplified_title <- normalize_title(title)
  words <- strsplit(simplified_title, "\\s+")[[1]]
  # 只使用前8个词
  if (length(words) > 8) {
    simplified_title <- paste(words[1:8], collapse = " ")
  }

  url <- sprintf("https://api.crossref.org/works?query.title=%s&rows=15",
                 URLencode(simplified_title))

  response <- tryCatch({
    GET(url, user_agent("BiblioEnhancer/1.0 (mailto:<EMAIL>)"))
  }, error = function(e) { return(NULL) })
  if (is.null(response) || status_code(response) != 200) return(NULL)
  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) { return(NULL) })

  if (debug && !is.null(content$message$items) && length(content$message$items) > 0) {
    log_message(sprintf("[DEBUG] 模糊标题查询返回前3条:"))
    for (i in 1:min(3, nrow(content$message$items))) {
      item <- content$message$items[i,]
      log_message(sprintf("  %d. 标题: %s | DOI: %s | score: %.2f",
        i, substr(item$title[[1]], 1, 60), item$DOI, item$score))
    }
  }

  if (!is.null(content$message$items) && length(content$message$items) > 0) {
    return(content$message$items)
  }
  return(NULL)
}

# 简化的验证函数 - 更宽松的匹配策略
verify_match <- function(result, original, threshold = 0.3) {
  # 首先验证标题相似度
  if (!is.null(result$title) && !is.na(original$TI)) {
    title_sim <- calculate_similarity(
      normalize_title(result$title[[1]]),
      normalize_title(original$TI)
    )
    # 如果标题相似度很高，直接通过
    if (title_sim >= 0.7) {
      return(TRUE)
    }
  }

  score <- 0
  total_weight <- 0

  # 验证期刊名 (权重: 0.5)
  if (!is.null(result$`container-title`) && !is.na(original$SO)) {
    journal_sim <- calculate_similarity(
      normalize_journal(result$`container-title`[1]),
      normalize_journal(original$SO)
    )
    score <- score + journal_sim * 0.5
    total_weight <- total_weight + 0.5
  }

  # 验证年份 (权重: 0.4)
  if (!is.null(result$`published-print`) && !is.na(original$PY)) {
    tryCatch({
      pub_year <- as.numeric(substr(result$`published-print`$`date-parts`[[1]][1], 1, 4))
      if (!is.na(pub_year) && abs(pub_year - as.numeric(original$PY)) <= 1) {
        score <- score + 1.0 * 0.4
      }
      total_weight <- total_weight + 0.4
    }, error = function(e) {
      # 如果年份解析失败，跳过
    })
  }

  # 验证卷号 (权重: 0.1) - 可选，添加更严格的检查
  if (!is.null(result$volume) && !is.na(result$volume) &&
      !is.na(original$VL) && !is.null(original$VL) && original$VL != "") {
    if (as.character(result$volume) == as.character(original$VL)) {
      score <- score + 1.0 * 0.1
    }
    total_weight <- total_weight + 0.1
  }

  # 计算综合相似度
  if (total_weight > 0) {
    final_score <- score / total_weight
    return(final_score >= threshold)
  }

  # 如果没有足够的信息进行验证，但Crossref score较高，也可以通过
  if (!is.null(result$score) && result$score >= 50) {
    return(TRUE)
  }

  return(FALSE)
}

# 主函数
complete_doi_layered <- function(input_file) {
  log_message("开始分层瀑布式DOI补全")
  
  # 1. 加载数据
  log_message("步骤1: 加载数据")
  if (file.exists(input_file)) {
    missing_doi_records <- read_csv(input_file, show_col_types = FALSE)
    log_message(sprintf("成功加载数据: %d行", nrow(missing_doi_records)))
  } else {
    stop(sprintf("输入文件不存在: %s", input_file))
  }
  
  # 2. 数据预处理
  log_message("步骤2: 数据预处理")
  missing_doi_records <- missing_doi_records %>%
    mutate(
      normalized_title = sapply(TI, normalize_title),
      normalized_author = sapply(AU, normalize_author),
      normalized_journal = sapply(SO, normalize_journal)
    )
  
  # 3. 创建结果数据框
  results <- data.frame(
    UT = missing_doi_records$UT,
    TI = missing_doi_records$TI,
    AU = missing_doi_records$AU,
    PY = missing_doi_records$PY,
    SO = missing_doi_records$SO,
    VL = missing_doi_records$VL,
    IS = missing_doi_records$IS,
    PG = missing_doi_records$PG,
    DOI = NA,
    strategy = NA,
    confidence = NA,
    stringsAsFactors = FALSE
  )
  
  # 4. 批量处理
  # 处理所有记录以实现完整性目标
  total_records <- nrow(results)
  log_message(sprintf("开始处理 %d 条记录（完整处理模式）", total_records))
  
  for (i in 1:total_records) {
    log_message(sprintf("处理第 %d/%d 条记录", i, total_records))
    
    # 获取当前记录
    current <- missing_doi_records[i,]
    
    # 策略一：黄金标准
    gold_result <- get_doi_gold(
      current$normalized_title,
      current$normalized_author,
      current$PY,
      current$normalized_journal,
      debug=TRUE
    )
    
    if (!is.na(gold_result$doi) && gold_result$score > 30) {  # 降低阈值从90到30
      results$DOI[i] <- gold_result$doi
      results$strategy[i] <- "gold"
      results$confidence[i] <- gold_result$score
      next
    }
    
    # 策略二：白银标准
    silver_results <- get_doi_silver(
      current$normalized_title,
      current$normalized_author,
      current$PY,
      debug=TRUE
    )
    
    if (!is.null(silver_results)) {
      for (j in 1:nrow(silver_results)) {
        result <- silver_results[j,]
        if (verify_match(result, current)) {
          results$DOI[i] <- result$DOI
          results$strategy[i] <- "silver"
          results$confidence[i] <- result$score
          break
        }
      }
      if (!is.na(results$DOI[i])) next
    }
    
    # 策略三：青铜标准
    bronze_results <- get_doi_bronze(
      current$normalized_title,
      current$PY,
      current$normalized_journal,
      debug=TRUE
    )

    if (!is.null(bronze_results)) {
      for (j in 1:nrow(bronze_results)) {
        result <- bronze_results[j,]
        if (verify_match(result, current)) {
          results$DOI[i] <- result$DOI
          results$strategy[i] <- "bronze"
          results$confidence[i] <- result$score
          break
        }
      }
      if (!is.na(results$DOI[i])) next
    }

    # 策略四：关键词查询
    keywords_results <- get_doi_keywords(
      current$TI,  # 使用原始标题
      current$PY,
      debug=TRUE
    )

    if (!is.null(keywords_results)) {
      for (j in 1:nrow(keywords_results)) {
        result <- keywords_results[j,]
        if (verify_match(result, current)) {
          results$DOI[i] <- result$DOI
          results$strategy[i] <- "keywords"
          results$confidence[i] <- result$score
          break
        }
      }
      if (!is.na(results$DOI[i])) next
    }

    # 策略五：模糊标题查询（最后尝试）
    fuzzy_results <- get_doi_fuzzy_title(
      current$TI,  # 使用原始标题
      debug=TRUE
    )

    if (!is.null(fuzzy_results)) {
      for (j in 1:nrow(fuzzy_results)) {
        result <- fuzzy_results[j,]
        if (verify_match(result, current)) {
          results$DOI[i] <- result$DOI
          results$strategy[i] <- "fuzzy"
          results$confidence[i] <- result$score
          break
        }
      }
    }

    # 添加延时避免API限制
    Sys.sleep(1.5)  # 增加延时以确保API稳定性
  }
  
  # 5. 质量评估和分类
  log_message("步骤5: 质量评估和结果分类")

  # 添加质量评估字段
  results$quality_score <- NA
  results$review_needed <- FALSE
  results$match_details <- ""

  for (i in 1:nrow(results)) {
    if (!is.na(results$DOI[i])) {
      confidence <- results$confidence[i]
      strategy <- results$strategy[i]

      # 计算质量分数
      quality_score <- 0
      if (strategy == "gold") quality_score <- confidence / 100
      else if (strategy == "silver") quality_score <- (confidence / 100) * 0.8
      else if (strategy == "bronze") quality_score <- (confidence / 100) * 0.6
      else if (strategy == "keywords") quality_score <- (confidence / 100) * 0.4
      else if (strategy == "fuzzy") quality_score <- (confidence / 100) * 0.3

      results$quality_score[i] <- quality_score

      # 标记需要人工审核的记录
      if (quality_score < 0.5 || confidence < 40) {
        results$review_needed[i] <- TRUE
        results$match_details[i] <- sprintf("低置信度匹配 (策略:%s, 分数:%.1f)", strategy, confidence)
      } else {
        results$match_details[i] <- sprintf("高置信度匹配 (策略:%s, 分数:%.1f)", strategy, confidence)
      }
    } else {
      results$match_details[i] <- "未找到匹配的DOI"
    }
  }

  # 6. 统计结果
  success_count <- sum(!is.na(results$DOI))
  success_rate <- 100 * success_count / total_records
  high_quality_count <- sum(!is.na(results$quality_score) & results$quality_score >= 0.7)
  review_needed_count <- sum(results$review_needed, na.rm = TRUE)

  strategy_counts <- table(results$strategy, useNA = "ifany")

  log_message(sprintf("\n=== 补全结果统计 ==="))
  log_message(sprintf("总记录数: %d", total_records))
  log_message(sprintf("成功补全: %d (%.2f%%)", success_count, success_rate))
  log_message(sprintf("高质量匹配: %d (%.2f%%)", high_quality_count, 100 * high_quality_count / total_records))
  log_message(sprintf("需要人工审核: %d (%.2f%%)", review_needed_count, 100 * review_needed_count / total_records))
  log_message(sprintf("补全失败: %d (%.2f%%)",
                     total_records - success_count,
                     100 - success_rate))
  log_message("\n=== 各策略成功数 ===")
  for (strategy in names(strategy_counts)) {
    log_message(sprintf("%s: %d", strategy, strategy_counts[strategy]))
  }
  
  # 7. 保存详细结果
  output_dir <- here("data_repository", "04_enhancement_reports")
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)

  # 保存为RDS
  saveRDS(results, file.path(output_dir, "doi_completion_enhanced.rds"))

  # 创建详细的Excel报告
  wb <- createWorkbook()

  # 工作表1：完整结果
  addWorksheet(wb, "完整结果")
  writeData(wb, "完整结果", results)

  # 工作表2：高质量匹配
  high_quality_results <- results[!is.na(results$quality_score) & results$quality_score >= 0.7, ]
  addWorksheet(wb, "高质量匹配")
  writeData(wb, "高质量匹配", high_quality_results)

  # 工作表3：需要人工审核
  review_needed_results <- results[results$review_needed == TRUE, ]
  addWorksheet(wb, "需要人工审核")
  writeData(wb, "需要人工审核", review_needed_results)

  # 工作表4：失败记录
  failed_results <- results[is.na(results$DOI), ]
  addWorksheet(wb, "失败记录")
  writeData(wb, "失败记录", failed_results)

  # 工作表5：详细统计
  addWorksheet(wb, "详细统计")
  detailed_stats <- data.frame(
    指标 = c("总记录数", "成功补全", "成功率", "高质量匹配", "高质量率",
             "需要人工审核", "审核率", "补全失败", "失败率",
             paste("策略", names(strategy_counts), "成功数")),
    数值 = c(total_records,
             success_count,
             sprintf("%.2f%%", success_rate),
             high_quality_count,
             sprintf("%.2f%%", 100 * high_quality_count / total_records),
             review_needed_count,
             sprintf("%.2f%%", 100 * review_needed_count / total_records),
             total_records - success_count,
             sprintf("%.2f%%", 100 - success_rate),
             as.character(strategy_counts))
  )
  writeData(wb, "详细统计", detailed_stats)

  # 工作表6：质量分布
  addWorksheet(wb, "质量分布")
  quality_distribution <- data.frame(
    质量等级 = c("优秀 (≥0.8)", "良好 (0.6-0.8)", "一般 (0.4-0.6)", "较差 (0.2-0.4)", "很差 (<0.2)", "未匹配"),
    数量 = c(
      sum(!is.na(results$quality_score) & results$quality_score >= 0.8),
      sum(!is.na(results$quality_score) & results$quality_score >= 0.6 & results$quality_score < 0.8),
      sum(!is.na(results$quality_score) & results$quality_score >= 0.4 & results$quality_score < 0.6),
      sum(!is.na(results$quality_score) & results$quality_score >= 0.2 & results$quality_score < 0.4),
      sum(!is.na(results$quality_score) & results$quality_score < 0.2),
      sum(is.na(results$quality_score))
    )
  )
  quality_distribution$百分比 <- sprintf("%.2f%%", 100 * quality_distribution$数量 / total_records)
  writeData(wb, "质量分布", quality_distribution)

  # 保存Excel文件
  saveWorkbook(wb, file.path(output_dir, "doi_completion_enhanced_detailed.xlsx"),
               overwrite = TRUE)

  log_message(sprintf("\n=== 结果已保存 ==="))
  log_message(sprintf("详细结果: %s", file.path(output_dir, "doi_completion_enhanced_detailed.xlsx")))
  log_message(sprintf("RDS文件: %s", file.path(output_dir, "doi_completion_enhanced.rds")))
  
  return(results)
}

# 执行补全
input_file <- here("data_repository", "04_enhancement_reports", 
                   "missing_doi_records.csv")
results <- complete_doi_layered(input_file) 
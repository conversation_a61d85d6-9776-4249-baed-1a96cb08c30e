# 学术标准DOI补全系统
# 以数据准确性为第一优先级，符合学术研究标准
# 作者: Augment Agent (Academic Version)
# 日期: 2025-06-19

cat("=== 学术标准DOI补全系统 ===\n")
cat("原则: 数据准确性第一，宁缺毋滥\n\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 学术标准的文本标准化函数
academic_normalize_text <- function(text) {
  if (is.na(text) || text == "") return("")
  
  # 转换为小写
  text <- tolower(text)
  
  # 移除标点符号但保留空格结构
  text <- gsub("[[:punct:]]", " ", text)
  
  # 标准化空格
  text <- gsub("\\s+", " ", text)
  text <- trimws(text)
  
  # 移除常见停用词
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  words <- strsplit(text, "\\s+")[[1]]
  words <- words[!words %in% stop_words & nchar(words) > 2]
  
  return(paste(words, collapse = " "))
}

# 学术标准的相似度计算
academic_similarity <- function(text1, text2) {
  if (is.na(text1) || is.na(text2) || text1 == "" || text2 == "") return(0)
  
  norm1 <- academic_normalize_text(text1)
  norm2 <- academic_normalize_text(text2)
  
  if (norm1 == "" || norm2 == "") return(0)
  
  # 使用Jaro-Winkler算法
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  return(similarity)
}

# 作者姓名匹配函数
match_authors <- function(authors1, authors2) {
  if (is.na(authors1) || is.na(authors2) || authors1 == "" || authors2 == "") return(0)
  
  # 提取姓氏（通常是第一个词或最后一个词）
  extract_surnames <- function(author_string) {
    # 分割作者
    authors <- strsplit(author_string, "[;,]")[[1]]
    surnames <- c()
    
    for (author in authors[1:min(3, length(authors))]) {  # 只检查前3个作者
      author <- trimws(author)
      words <- strsplit(author, "\\s+")[[1]]
      if (length(words) > 0) {
        # 取第一个词作为姓氏（常见格式：SMITH J）
        surname <- toupper(words[1])
        if (nchar(surname) > 2) {  # 至少3个字符
          surnames <- c(surnames, surname)
        }
      }
    }
    return(unique(surnames))
  }
  
  surnames1 <- extract_surnames(authors1)
  surnames2 <- extract_surnames(authors2)
  
  if (length(surnames1) == 0 || length(surnames2) == 0) return(0)
  
  # 计算匹配的姓氏比例
  matches <- sum(surnames1 %in% surnames2)
  total <- max(length(surnames1), length(surnames2))
  
  return(matches / total)
}

# 期刊匹配函数（学术标准）
match_journals <- function(journal1, journal2) {
  if (is.na(journal1) || is.na(journal2) || journal1 == "" || journal2 == "") return(0)
  
  # 期刊名称标准化
  normalize_journal <- function(journal) {
    journal <- tolower(journal)
    journal <- gsub("^the\\s+", "", journal)
    journal <- gsub("\\s*\\(.*\\)$", "", journal)
    journal <- gsub("[[:punct:]]", " ", journal)
    journal <- gsub("\\s+", " ", journal)
    return(trimws(journal))
  }
  
  norm1 <- normalize_journal(journal1)
  norm2 <- normalize_journal(journal2)
  
  # 完全匹配
  if (norm1 == norm2) return(1.0)
  
  # 相似度匹配
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  return(similarity)
}

# 学术标准的DOI查询函数
academic_doi_search <- function(title, authors, year, journal) {
  tryCatch({
    # 构建保守的查询策略
    clean_title <- academic_normalize_text(title)
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    
    # 只使用最重要的关键词（长度>4的词，最多3个）
    keywords <- title_words[nchar(title_words) > 4]
    if (length(keywords) > 3) keywords <- keywords[1:3]
    if (length(keywords) == 0) return(NULL)
    
    query_string <- paste(keywords, collapse = " ")
    
    # 构建API查询，限制年份范围
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=10", 
                   URLencode(query_string), as.numeric(year)-1, as.numeric(year)+1)
    
    response <- GET(url, user_agent("AcademicDOI/1.0"), timeout(30))
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$message$items) || length(content$message$items) == 0) return(NULL)
    
    items <- content$message$items
    best_match <- NULL
    best_score <- 0
    
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      # 提取候选文献信息
      candidate_title <- if (!is.null(item$title) && length(item$title) > 0) item$title[[1]] else ""
      candidate_authors <- ""
      candidate_journal <- ""
      candidate_year <- ""
      
      # 提取作者信息
      if (!is.null(item$author) && nrow(item$author) > 0) {
        author_names <- paste(item$author$family, item$author$given, sep = " ")
        candidate_authors <- paste(author_names[1:min(3, length(author_names))], collapse = "; ")
      }
      
      # 提取期刊信息
      if (!is.null(item$`container-title`) && length(item$`container-title`) > 0) {
        candidate_journal <- item$`container-title`[[1]]
      }
      
      # 提取年份信息
      if (!is.null(item$`published-print`$`date-parts`)) {
        candidate_year <- item$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(item$`published-online`$`date-parts`)) {
        candidate_year <- item$`published-online`$`date-parts`[[1]][[1]]
      }
      
      # 学术标准的多重验证
      title_sim <- academic_similarity(title, candidate_title)
      author_sim <- match_authors(authors, candidate_authors)
      journal_sim <- match_journals(journal, candidate_journal)
      year_match <- if (candidate_year == year) 1.0 else 0.0
      
      # 学术标准评分系统（严格）
      academic_score <- (title_sim * 0.4) + (author_sim * 0.25) + (journal_sim * 0.25) + (year_match * 0.1)
      
      # 严格的接受标准
      if (title_sim >= 0.85 &&           # 标题相似度至少85%
          author_sim >= 0.5 &&           # 至少50%作者匹配
          journal_sim >= 0.7 &&          # 期刊相似度至少70%
          year_match == 1.0 &&           # 年份必须完全匹配
          academic_score > best_score) {
        
        best_score <- academic_score
        best_match <- list(
          doi = item$DOI,
          title = candidate_title,
          authors = candidate_authors,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          author_similarity = author_sim,
          journal_similarity = journal_sim,
          year_match = year_match,
          academic_score = academic_score,
          crossref_score = item$score
        )
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    return(NULL)
  })
}

# 学术标准的批量处理函数
academic_batch_process <- function(input_file, output_dir) {
  cat("开始学术标准DOI补全处理...\n")
  
  # 读取数据
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  cat(sprintf("加载数据: %d条记录\n", nrow(data)))
  
  # 初始化结果
  results <- data.frame(
    UT = data$UT,
    原始标题 = data$TI,
    原始作者 = data$AU,
    原始年份 = data$PY,
    原始期刊 = data$SO,
    补全DOI = NA,
    匹配标题 = NA,
    匹配作者 = NA,
    匹配期刊 = NA,
    匹配年份 = NA,
    标题相似度 = NA,
    作者匹配度 = NA,
    期刊匹配度 = NA,
    年份匹配 = NA,
    学术评分 = NA,
    Crossref评分 = NA,
    补全状态 = "待处理",
    验证级别 = NA,
    stringsAsFactors = FALSE
  )
  
  # 统计变量
  total_count <- nrow(data)
  success_count <- 0
  high_confidence_count <- 0
  
  cat("开始处理，采用学术标准严格验证...\n")
  
  for (i in 1:total_count) {
    if (i %% 25 == 0 || i == 1) {
      cat(sprintf("进度: %d/%d (%.1f%%), 成功: %d, 高置信度: %d\n", 
                  i, total_count, 100*i/total_count, success_count, high_confidence_count))
    }
    
    # 执行学术标准DOI搜索
    match_result <- academic_doi_search(
      title = data$TI[i],
      authors = data$AU[i], 
      year = data$PY[i],
      journal = data$SO[i]
    )
    
    if (!is.null(match_result)) {
      # 记录匹配结果
      results$补全DOI[i] <- match_result$doi
      results$匹配标题[i] <- match_result$title
      results$匹配作者[i] <- match_result$authors
      results$匹配期刊[i] <- match_result$journal
      results$匹配年份[i] <- match_result$year
      results$标题相似度[i] <- round(match_result$title_similarity, 3)
      results$作者匹配度[i] <- round(match_result$author_similarity, 3)
      results$期刊匹配度[i] <- round(match_result$journal_similarity, 3)
      results$年份匹配[i] <- match_result$year_match
      results$学术评分[i] <- round(match_result$academic_score, 3)
      results$Crossref评分[i] <- round(match_result$crossref_score, 1)
      results$补全状态[i] <- "成功"
      
      # 确定验证级别
      if (match_result$title_similarity >= 0.95 && 
          match_result$author_similarity >= 0.7 && 
          match_result$journal_similarity >= 0.9) {
        results$验证级别[i] <- "A级-极高置信度"
        high_confidence_count <- high_confidence_count + 1
      } else if (match_result$title_similarity >= 0.9 && 
                 match_result$author_similarity >= 0.6 && 
                 match_result$journal_similarity >= 0.8) {
        results$验证级别[i] <- "B级-高置信度"
      } else {
        results$验证级别[i] <- "C级-可接受"
      }
      
      success_count <- success_count + 1
    } else {
      results$补全状态[i] <- "未找到符合学术标准的匹配"
      results$验证级别[i] <- "未补全"
    }
    
    # 每50条记录保存中间结果
    if (i %% 50 == 0) {
      temp_file <- file.path(output_dir, sprintf("academic_doi_temp_%d.csv", i))
      write.csv(results[1:i, ], temp_file, row.names = FALSE)
    }
    
    # API限制
    Sys.sleep(1.5)
  }
  
  # 最终统计
  success_rate <- 100 * success_count / total_count
  high_confidence_rate <- 100 * high_confidence_count / total_count
  
  cat(sprintf("\n=== 学术标准DOI补全结果 ===\n"))
  cat(sprintf("总记录数: %d\n", total_count))
  cat(sprintf("成功补全: %d (%.2f%%)\n", success_count, success_rate))
  cat(sprintf("A级极高置信度: %d (%.2f%%)\n", high_confidence_count, high_confidence_rate))
  cat(sprintf("B级高置信度: %d (%.2f%%)\n", 
              sum(results$验证级别 == "B级-高置信度", na.rm = TRUE),
              100 * sum(results$验证级别 == "B级-高置信度", na.rm = TRUE) / total_count))
  cat(sprintf("C级可接受: %d (%.2f%%)\n", 
              sum(results$验证级别 == "C级-可接受", na.rm = TRUE),
              100 * sum(results$验证级别 == "C级-可接受", na.rm = TRUE) / total_count))
  
  # 保存最终结果
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  final_file <- file.path(output_dir, "academic_doi_completion_results.csv")
  write.csv(results, final_file, row.names = FALSE)
  
  cat(sprintf("\n学术标准DOI补全结果已保存: %s\n", final_file))
  
  return(results)
}

# 主执行函数
main_academic_process <- function() {
  input_file <- "data_repository/04_enhancement_reports/missing_doi_records.csv"
  output_dir <- "data_repository/04_enhancement_reports"
  
  if (file.exists(input_file)) {
    results <- academic_batch_process(input_file, output_dir)
    return(results)
  } else {
    stop("输入文件不存在")
  }
}

# 创建学术审核报告
create_academic_review_report <- function(results_file, output_dir) {
  cat("创建学术审核报告...\n")

  results <- read.csv(results_file, stringsAsFactors = FALSE)
  successful_results <- results[results$补全状态 == "成功", ]

  if (nrow(successful_results) == 0) {
    cat("没有成功的DOI补全记录\n")
    return(NULL)
  }

  # 创建HTML审核报告
  html_file <- file.path(output_dir, "academic_doi_review_report.html")

  html_content <- sprintf('
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学术标准DOI补全审核报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary { background-color: #e8f5e8; padding: 10px; border-radius: 5px; margin-bottom: 20px; }
        .record { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .grade-a { background-color: #e8f5e8; }
        .grade-b { background-color: #fff3cd; }
        .grade-c { background-color: #f8d7da; }
        .field { margin: 5px 0; }
        .label { font-weight: bold; color: #333; }
        .value { margin-left: 10px; }
        .metrics { background-color: #f8f9fa; padding: 8px; border-radius: 3px; margin: 5px 0; }
        table { border-collapse: collapse; width: 100%%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>学术标准DOI补全审核报告</h1>
        <p>生成时间: %s</p>
        <p>审核标准: 多重验证（标题+作者+期刊+年份），严格阈值控制</p>
    </div>

    <div class="summary">
        <h2>补全统计摘要</h2>
        <table>
            <tr><th>指标</th><th>数量</th><th>比例</th></tr>
            <tr><td>总记录数</td><td>%d</td><td>100.0%%</td></tr>
            <tr><td>成功补全</td><td>%d</td><td>%.1f%%</td></tr>
            <tr><td>A级-极高置信度</td><td>%d</td><td>%.1f%%</td></tr>
            <tr><td>B级-高置信度</td><td>%d</td><td>%.1f%%</td></tr>
            <tr><td>C级-可接受</td><td>%d</td><td>%.1f%%</td></tr>
        </table>
    </div>
',
    format(Sys.time(), "%%Y-%%m-%%d %%H:%%M:%%S"),
    nrow(results),
    nrow(successful_results), 100 * nrow(successful_results) / nrow(results),
    sum(successful_results$验证级别 == "A级-极高置信度", na.rm = TRUE),
    100 * sum(successful_results$验证级别 == "A级-极高置信度", na.rm = TRUE) / nrow(results),
    sum(successful_results$验证级别 == "B级-高置信度", na.rm = TRUE),
    100 * sum(successful_results$验证级别 == "B级-高置信度", na.rm = TRUE) / nrow(results),
    sum(successful_results$验证级别 == "C级-可接受", na.rm = TRUE),
    100 * sum(successful_results$验证级别 == "C级-可接受", na.rm = TRUE) / nrow(results)
  )

  # 添加详细记录
  html_content <- paste0(html_content, '<h2>详细审核记录</h2>')

  for (i in 1:min(30, nrow(successful_results))) {  # 显示前30条
    record <- successful_results[i, ]

    grade_class <- switch(record$验证级别,
                         "A级-极高置信度" = "grade-a",
                         "B级-高置信度" = "grade-b",
                         "C级-可接受" = "grade-c",
                         "")

    record_html <- sprintf('
    <div class="record %s">
        <h3>记录 %d - %s</h3>
        <div class="field"><span class="label">UT:</span><span class="value">%s</span></div>
        <div class="field"><span class="label">原始标题:</span><span class="value">%s</span></div>
        <div class="field"><span class="label">匹配标题:</span><span class="value">%s</span></div>
        <div class="field"><span class="label">补全DOI:</span><span class="value"><a href="https://doi.org/%s" target="_blank">%s</a></span></div>

        <div class="metrics">
            <strong>验证指标:</strong><br>
            标题相似度: %.3f | 作者匹配度: %.3f | 期刊匹配度: %.3f | 年份匹配: %.0f | 学术评分: %.3f
        </div>

        <div class="field"><span class="label">原始期刊:</span><span class="value">%s</span></div>
        <div class="field"><span class="label">匹配期刊:</span><span class="value">%s</span></div>
        <div class="field"><span class="label">原始作者:</span><span class="value">%s</span></div>
        <div class="field"><span class="label">匹配作者:</span><span class="value">%s</span></div>
    </div>
    ',
      grade_class, i, record$验证级别,
      record$UT,
      substr(record$原始标题, 1, 100),
      substr(record$匹配标题, 1, 100),
      record$补全DOI, record$补全DOI,
      record$标题相似度, record$作者匹配度, record$期刊匹配度, record$年份匹配, record$学术评分,
      substr(record$原始期刊, 1, 50),
      substr(record$匹配期刊, 1, 50),
      substr(record$原始作者, 1, 60),
      substr(record$匹配作者, 1, 60)
    )

    html_content <- paste0(html_content, record_html)
  }

  html_content <- paste0(html_content, '</body></html>')

  # 保存HTML文件
  writeLines(html_content, html_file, useBytes = TRUE)
  cat(sprintf("学术审核报告已保存: %s\n", html_file))

  return(html_file)
}

# 执行学术标准处理
if (!interactive()) {
  results <- main_academic_process()

  # 创建审核报告
  results_file <- "data_repository/04_enhancement_reports/academic_doi_completion_results.csv"
  if (file.exists(results_file)) {
    create_academic_review_report(results_file, "data_repository/04_enhancement_reports")
  }
}

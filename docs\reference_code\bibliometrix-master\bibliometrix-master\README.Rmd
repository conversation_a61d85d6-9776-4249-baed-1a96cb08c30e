---
output: github_document
---

<!-- README.md is generated from README.Rmd. Please edit that file -->

```{r, include = FALSE}
knitr::opts_chunk$set(
  collapse = TRUE,
  comment = "#>",
  fig.path = "man/figures/README-",
  out.width = "100%",
  dpi = 300
)
```


bibliometrix
====================================

## An R-tool for comprehensive science mapping analysis. 

[![bibliometrix: An R-tool for comprehensive science mapping analysis.](https://www.bibliometrix.org/JOI-badge.svg)](https://doi.org/10.1016/j.joi.2017.08.007)
[![Project Status: Active - The project has reached a stable, usable state and is being actively developed.](http://www.repostatus.org/badges/latest/active.svg)](http://www.repostatus.org/#active)
[![R-CMD-check](https://github.com/massimoaria/e2tree/actions/workflows/R-CMD-check.yaml/badge.svg)](https://github.com/massimoaria/bibliometrix/actions/workflows/R-CMD-check.yaml)
[![cran version](http://www.r-pkg.org/badges/version/bibliometrix)](https://cran.r-project.org/package=bibliometrix)
[![rstudio mirror downloads](https://cranlogs.r-pkg.org/badges/bibliometrix)](https://github.com/metacran/cranlogs.app)
`r badger::badge_cran_download("bibliometrix", "grand-total")` 



<p align="center">
<img src="https://www.bibliometrix.org/logo_new.png" width="400"  />
</p>

## Overview

**bibliometrix** package provides a set of tools for quantitative research in bibliometrics and scientometrics.

Bibliometrics turns the main tool of science, quantitative analysis, on itself. Essentially, bibliometrics is the application of quantitative analysis and statistics to publications such as journal articles and their accompanying citation counts. Quantitative evaluation of publication and citation data is now used in almost all scientific fields to evaluate growth, maturity, leading authors, conceptual and intellectual maps, trends of a scientific community.

Bibliometrics is also used in research performance evaluation, especially in university and government labs, and also by policymakers, research directors and administrators, information specialists and librarians, and scholars themselves.

**bibliometrix** supports scholars in three key phases of analysis:

* Data importing and conversion to R format;

* Bibliometric analysis of a publication dataset;

* Building and plotting matrices for co-citation, coupling, collaboration, and co-word analysis. Matrices are the input data for performing network analysis, multiple correspondence analysis, and any other data reduction techniques.


## biblioshiny

**bibliometrix** includes **biblioshiny: bibliometrix for no-coders**

**biblioshiny** is a *shiny app providing a web-interface for bibliometrix*.

It supports scholars in easy use of the main features of bibliometrix:

* Data importing and conversion to data frame collection

* Data filtering

* Analytics and Plots for three different level metrics:

    - Sources  

    - Authors  

    - Documents   

* Analysis of three structures of Knowledge (K-structures):  

    - Conceptual Structure  

    - Intellectual Structure  

    - Social Structure



### How to use biblioshiny

Please follow the biblioshiny tutorial at the section tutorial of bibliometrix website https://www.bibliometrix.org/

## How to cite bibliometrix

If you use this package for your research, you must cite it.

To cite bibliometrix in publications, please use:

Aria, M. & Cuccurullo, C. (2017) **bibliometrix: An R-tool for comprehensive science mapping analysis**, *Journal of Informetrics*, 11(4), pp 959-975, Elsevier.



## Community

Official website:   https://www.bibliometrix.org

CRAN page:          https://cran.r-project.org/package=bibliometrix
    
GitHub repository:  https://github.com/massimoaria/bibliometrix

Tutorials

How to use:         https://www.bibliometrix.org/vignettes/Introduction_to_bibliometrix.html

Data importing 
and converting:     https://www.bibliometrix.org/vignettes/Data-Importing-and-Converting.html



## Installation


Stable version from CRAN

```{r eval=FALSE, include=FALSE}
install.packages("bibliometrix")
```


Developers version from GitHub


```{r eval=FALSE, include=FALSE}
install.packages("remotes")
remotes::install_github("massimoaria/bibliometrix")
```

Load `bibliometrix`


```{r echo=TRUE}
library('bibliometrix')
```




## Data loading and converting

The export file can be read and converted using by R using the function *convert2df*:

**convert2df**(*file*, *dbsource*, *format*)

The argument *file* is a character vector containing the name of export files downloaded from SCOPUS, Clarivate Analytics WoS, OpenAlex, Digital Science Dimensions, PubMed or Cochrane CDSR website. *file* can also contains the name of a json/xlm object download using OpenAlex, Digital Science Dimensions or PubMed APIs (through the packages *openalexR*, *dimensionsR* and *pubmedR*.

es. file <- c("file1.txt","file2.txt", ...)


```{r Data loading and Converting}
## An example from bibliometrix vignettes

file <- c("https://www.bibliometrix.org/datasets/management1.txt","https://www.bibliometrix.org/datasets/management2.txt")

M <- convert2df(file = file, dbsource = "wos", format = "plaintext")
```

*convert2df* creates a bibliographic data frame with cases corresponding to manuscripts and variables to Field Tag in the original export file.

Each manuscript contains several elements, such as authors' names, title, keywords and other information. All these elements constitute the bibliographic attributes of a document, also called metadata.

Data frame columns are named using the standard Clarivate Analytics WoS Field Tag codify [(https://www.bibliometrix.org/documents/Field_Tags_bibliometrix.pdf)](https://www.bibliometrix.org/documents/Field_Tags_bibliometrix.pdf). 


## Check completeness of metadata included in the bibliographic data frame

After importing a bibliographic data frame, we can check the completeness of the metadata included in it through **missingData()**.

**missingData**(*M*)

The argument *M* is a bibliographic data frame obtained by **convert2df** function.

```{r Completeness of metadata}
## An example from bibliometrix vignettes

com <- missingData(M)

com$mandatoryTags
```

**missingData** returns a list containing two data frame. The first one, *allTags* includes the results for all metadata in M. The latter, *mandatoryTags*,	reports the results only for the metadata needed to perform analyses with bibliometrix or biblioshiny.

The column *status* classifies the percentage of missing value in 5 categories: "Excellent" (0%), "Good" (0.01% to 10.00%), "Acceptable" (from 10.01% to 20.00%), "Poor" (from 20.01% to 50.00%), "Critical" (from 50.01% to 99.99%), "Completely missing" (100%).


## Bibliometric Analysis

The first step is to perform a descriptive analysis of the bibliographic data frame.

The function *biblioAnalysis* calculates main bibliometric measures using this syntax:
 
```{r biblioAnalysis}
results <- biblioAnalysis(M, sep = ";")
```

The function *biblioAnalysis* returns an object of class "bibliometrix".


To summarize main results of the bibliometric analysis, use the generic function *summary*.
It displays main information about the bibliographic data frame and several tables, such as annual scientific production, top manuscripts per number of citations, most productive authors, most productive countries, total citation per country, most relevant sources (journals) and most relevant keywords.

*summary* accepts two additional arguments. *k* is a formatting value that indicates the number of rows of each table. *pause* is a logical value (TRUE or FALSE) used to allow (or not) pause in screen scrolling.
Choosing k=10 you decide to see the first 10 Authors, the first 10 sources, etc.

```{r summary generic function}
S <- summary(object = results, k = 10, pause = FALSE)
```

Some basic plots can be drawn using the generic function plot:

```{r plot generic function, fig.width=7}
plot(x = results, k = 10, pause = FALSE)
```




## Bibliographic network matrices

Manuscript's attributes are connected to each other through the manuscript itself: author(s) to journal, keywords to publication date, etc.

These connections of different attributes generate bipartite networks that can be represented as rectangular matrices (Manuscripts x Attributes).

Furthermore, scientific publications regularly contain references to
other scientific works. This generates a further network, namely, co-citation or coupling network.

These networks are analyzed in order to capture meaningful properties of the underlying research system, and in particular to determine the influence of bibliometric units such as scholars and journals.



### biblioNetwork function

The function *biblioNetwork* calculates, starting from a bibliographic data frame, the most frequently used networks: Coupling, Co-citation, Co-occurrences, and Collaboration.

*biblioNetwork* uses two arguments to define the network to compute:

* *analysis* argument can be "co-citation", "coupling", "collaboration",  or "co-occurrences".

* *network* argument can be "authors", "references", "sources", "countries", "universities", "keywords", "author_keywords", "titles" and "abstracts".

i.e. the following code calculates a classical co-citation network:

```
NetMatrix <- biblioNetwork(M, analysis = "co-citation", network = "references", sep = ";")
```




## Visualizing bibliographic networks


All bibliographic networks can be graphically visualized or modeled.

Using the function *networkPlot*, you can plot a network created by *biblioNetwork* using R routines.

The main argument of *networkPlot* is type. It indicates the network map layout: circle, kamada-kawai, mds, etc.

In the following, we propose some examples.



### Country Scientific Collaboration

```{r Country collaboration, fig.height=7, fig.width=7, warning=FALSE}
# Create a country collaboration network

M <- metaTagExtraction(M, Field = "AU_CO", sep = ";")
NetMatrix <- biblioNetwork(M, analysis = "collaboration", network = "countries", sep = ";")

# Plot the network
net=networkPlot(NetMatrix, n = dim(NetMatrix)[1], Title = "Country Collaboration", type = "circle", size=TRUE, remove.multiple=FALSE,labelsize=0.8)

```



### Co-Citation Network

```{r Co-citation network, fig.height=7, fig.width=7, warning=FALSE}
# Create a co-citation network

NetMatrix <- biblioNetwork(M, analysis = "co-citation", network = "references", n=30, sep = ";")

# Plot the network
net=networkPlot(NetMatrix, Title = "Co-Citation Network", type = "fruchterman", size=T, remove.multiple=FALSE, labelsize=0.7,edgesize = 5)

```



### Keyword co-occurrences

```{r Keyword c-occurrences, fig.height=7, fig.width=7, warning=FALSE}
# Create keyword co-occurrences network

NetMatrix <- biblioNetwork(M, analysis = "co-occurrences", network = "keywords", sep = ";")

# Plot the network
net=networkPlot(NetMatrix, normalize="association", weighted=T, n = 30, Title = "Keyword Co-occurrences", type = "fruchterman", size=T,edgesize = 5,labelsize=0.7)

```




## Co-Word Analysis: The conceptual structure of a field 

The aim of the co-word analysis is to map the conceptual structure of a framework using the word co-occurrences in a bibliographic collection.

The analysis can be performed through dimensionality reduction techniques such as Multidimensional Scaling (MDS), Correspondence Analysis (CA) or Multiple Correspondence Analysis (MCA). 

Here, we show an example using the function *conceptualStructure* that performs a CA or MCA to draw a conceptual structure of the field and K-means clustering to identify clusters of documents which express common concepts. Results are plotted on a two-dimensional map.

*conceptualStructure* includes natural language processing (NLP) routines (see the function *termExtraction*) to extract terms from titles and abstracts.  In addition, it implements the Porter's stemming algorithm to reduce inflected (or sometimes derived) words to their word stem, base or root form.


```{r Co-Word Analysis, fig.height=9, fig.width=9, warning=FALSE}

# Conceptual Structure using keywords (method="CA")

CS <- conceptualStructure(M,field="ID", method="MCA", minDegree=10, clust=5, stemming=FALSE, labelsize=15, documents=20, graph=FALSE)
plot(CS$graph_terms)
plot(CS$graph_dendogram)

```




## Historical Direct Citation Network

The historiographic map is a graph proposed by E. Garfield to represent a chronological network map of most relevant direct citations resulting from a bibliographic collection.

The function histNetwork generates a chronological direct citation network matrix which can be plotted using *histPlot*:

```{r Historical Co-citation network, fig.height=9, fig.width=9, warning=FALSE}
# Create a historical citation network

histResults <- histNetwork(M, sep = ";")

# Plot a historical co-citation network
net <- histPlot(histResults, n=20, size = FALSE,label="short")

```




## Main Authors' references (about bibliometrics)


Aria, M. & Cuccurullo, C. (2017). bibliometrix: An R-tool for comprehensive science mapping analysis, *Journal of Informetrics*, 11(4), pp 959-975, Elsevier, DOI: 10.1016/j.joi.2017.08.007 (https://doi.org/10.1016/j.joi.2017.08.007)

Aria, M., Cuccurullo, C., D’Aniello, L., Misuraca, M., & Spano, M. (2022). Thematic Analysis as a New Culturomic Tool: The Social Media Coverage on COVID-19 Pandemic in Italy. *Sustainability*, 14(6), 3643, (https://doi.org/10.3390/su14063643)

Aria M., Misuraca M., Spano M. (2020) Mapping the evolution of social research and data science on 30 years of Social Indicators Research, *Social Indicators Research*. (DOI: https://doi.org/10.1007/s11205-020-02281-3)

Aria M., Alterisio A., Scandurra A, Pinelli C., D’Aniello B, (2021) The scholar’s best friend: research trends in dog cognitive and behavioural studies, *Animal Cognition*. (https://doi.org/10.1007/s10071-020-01448-2)

Belfiore, A., Cuccurullo, C., & Aria, M. (2022). IoT in healthcare: A scientometric analysis. *Technological Forecasting and Social Change*, 184, 122001. (https://doi.org/10.1016/j.techfore.2022.122001)

Belfiore, A., Salatino, A., & Osborne, F. (2022). Characterising Research Areas in the field of AI. *arXiv preprint arXiv:2205.13471*.(https://doi.org/10.48550/arXiv.2205.13471)

Ciavolino, E., Aria, M., Cheah, J. H., & Roldán, J. L. (2022). A tale of PLS structural equation modelling: episode I—a bibliometrix citation analysis. *Social Indicators Research*, 164(3), 1323-1348 (https://doi.org/10.1007/s11205-022-02994-7).

Cuccurullo, C., Aria, M., & Sarto, F. (2016). Foundations and trends in performance management. A twenty-five years bibliometric analysis in business and public administration domains, *Scientometrics*, DOI: 10.1007/s11192-016-1948-8 (https://doi.org/10.1007/s11192-016-1948-8)

Cuccurullo, C., Aria, M., & Sarto, F. (2015). Twenty years of research on performance management in business and public administration domains. Presentation at the *Correspondence Analysis and Related Methods conference (CARME 2015)* in September 2015 (https://www.bibliometrix.org/documents/2015Carme_cuccurulloetal.pdf)

Cuccurullo, C., Aria, M., & Sarto, F. (2013). Twenty years of research on performance management in business and public administration domains. In *Academy of Management Proceedings* (Vol. 2013, No. 1, p. 14270). Academy of Management (https://doi.org/10.5465/AMBPP.2013.14270abstract)

D'Aniello, L., Spano, M., Cuccurullo, C., & Aria, M. (2022). Academic Health Centers’ configurations, scientific productivity, and impact: insights from the Italian setting. *Health Policy*. (https://doi.org/10.1016/j.healthpol.2022.09.007)

Sarto, F., Cuccurullo, C., & Aria, M. (2014). Exploring healthcare governance literature: systematic review and paths for future research. *Mecosan* (https://www.francoangeli.it/Riviste/Scheda_Rivista.aspx?IDarticolo=52780&lingua=en)

Scarano, A., Aria, M., Mauriello, F., Riccardi, M. R., & Montella, A. (2023). Systematic literature review of 10 years of cyclist safety research. *Accident Analysis & Prevention*, 184, 106996 (https://doi.org/10.1016/j.aap.2023.106996).







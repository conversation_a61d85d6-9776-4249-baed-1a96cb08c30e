# 批量DOI补全脚本 - 优化版本
# 基于成功的测试版本，应用到完整数据集

# 加载必要的包
suppressMessages({
  library(tidyverse)
  library(here)
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 设置日志函数
log_message <- function(message, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[%s] [%s] %s\n", timestamp, toupper(type), message))
}

# 网络连接检查函数
check_network_connection <- function() {
  tryCatch({
    response <- GET("https://api.crossref.org/", timeout(10))
    status_code(response) == 200
  }, error = function(e) {
    FALSE
  })
}

# 带重试机制的API请求函数
safe_api_request <- function(url, max_retries = 3, retry_delay = 5) {
  for (attempt in 1:max_retries) {
    tryCatch({
      response <- GET(url, user_agent("BiblioEnhancer/1.0"), timeout(30))
      if (status_code(response) == 200) {
        return(response)
      }
    }, error = function(e) {
      log_message(sprintf("网络请求错误: %s，尝试 %d/%d", 
                         e$message, attempt, max_retries), "warning")
    })
    
    if (attempt < max_retries) {
      Sys.sleep(retry_delay)
    }
  }
  return(NULL)
}

# 数据标准化函数
normalize_title <- function(title) {
  if (is.na(title)) return(NA)
  title <- tolower(title)
  title <- gsub("[[:punct:]]", " ", title)
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  title <- gsub(paste0("\\b(", paste(stop_words, collapse="|"), ")\\b"), "", title)
  title <- gsub("\\s+", " ", title)
  trimws(title)
}

# 改进相似度计算
calculate_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  1 - stringdist(str1, str2, method="jw")
}

# DOI查询函数
get_doi_enhanced <- function(title, year, debug = FALSE) {
  # 检查网络连接
  if (!check_network_connection()) {
    if (debug) log_message("网络连接检查失败", "warning")
    return(list(doi = NA, score = 0, similarity = 0))
  }
  
  # 构建查询URL
  clean_title <- normalize_title(title)
  title_words <- strsplit(clean_title, "\\s+")[[1]]
  keywords <- title_words[nchar(title_words) > 3]
  if (length(keywords) > 5) keywords <- keywords[1:5]
  
  if (length(keywords) == 0) return(list(doi = NA, score = 0, similarity = 0))
  
  query_string <- paste(keywords, collapse = " ")
  url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=5", 
                 URLencode(query_string), as.numeric(year)-1, as.numeric(year)+1)
  
  response <- safe_api_request(url)
  if (is.null(response)) return(list(doi = NA, score = 0, similarity = 0))
  
  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) { 
    return(list(doi = NA, score = 0, similarity = 0)) 
  })
  
  if (!is.null(content$message$items) && length(content$message$items) > 0) {
    items <- content$message$items
    
    # 寻找最佳匹配
    best_match_idx <- NULL
    best_similarity <- 0
    
    for (i in 1:nrow(items)) {
      item <- items[i,]
      if (!is.null(item$title) && length(item$title) > 0) {
        candidate_title <- item$title[[1]]
        current_similarity <- calculate_similarity(normalize_title(title), normalize_title(candidate_title))
        
        if (current_similarity > best_similarity) {
          best_similarity <- current_similarity
          best_match_idx <- i
        }
      }
    }
    
    # 使用较低的阈值
    similarity_threshold <- 0.3
    
    if (!is.null(best_match_idx) && best_similarity > similarity_threshold) {
      doi_found <- items$DOI[best_match_idx]
      crossref_score <- items$score[best_match_idx]
      
      if (!is.na(doi_found) && doi_found != "") {
        return(list(doi = doi_found, score = crossref_score, similarity = best_similarity))
      }
    }
  }
  
  return(list(doi = NA, score = 0, similarity = 0))
}

# 批量处理函数
process_batch <- function(batch_data, batch_num, total_batches) {
  log_message(sprintf("开始处理批次 %d/%d (%d条记录)", batch_num, total_batches, nrow(batch_data)))
  
  results <- data.frame(
    UT = batch_data$UT,
    TI = batch_data$TI,
    AU = batch_data$AU,
    PY = batch_data$PY,
    SO = batch_data$SO,
    DOI = NA,
    confidence = NA,
    similarity = NA,
    quality_score = NA,
    review_needed = FALSE,
    status = "pending",
    stringsAsFactors = FALSE
  )
  
  for (i in 1:nrow(batch_data)) {
    if (i %% 10 == 0) {
      log_message(sprintf("批次 %d: 处理第 %d/%d 条记录", batch_num, i, nrow(batch_data)))
    }
    
    # 查询DOI
    doi_result <- get_doi_enhanced(batch_data$TI[i], batch_data$PY[i], debug = FALSE)
    
    if (!is.na(doi_result$doi)) {
      results$DOI[i] <- doi_result$doi
      results$confidence[i] <- doi_result$score
      results$similarity[i] <- doi_result$similarity
      results$status[i] <- "success"
      
      # 计算质量分数
      quality_score <- doi_result$similarity * 0.7 + (doi_result$score / 100) * 0.3
      results$quality_score[i] <- quality_score
      
      # 判断是否需要人工审核
      if (quality_score < 0.6 || doi_result$similarity < 0.5 || doi_result$score < 30) {
        results$review_needed[i] <- TRUE
      }
    } else {
      results$status[i] <- "failed"
    }
    
    # 防止API限制
    Sys.sleep(1.5)
  }
  
  return(results)
}

# 主函数
main <- function() {
  log_message("开始批量DOI补全处理")
  
  # 1. 检查网络连接
  if (!check_network_connection()) {
    stop("网络连接失败，无法继续处理")
  }
  log_message("网络连接正常")
  
  # 2. 加载数据
  input_file <- here("data_repository", "04_enhancement_reports", "missing_doi_records.csv")
  if (!file.exists(input_file)) {
    stop(sprintf("输入文件不存在: %s", input_file))
  }
  
  missing_doi_records <- read_csv(input_file, show_col_types = FALSE)
  log_message(sprintf("成功加载数据: %d行", nrow(missing_doi_records)))
  
  # 3. 分批处理
  batch_size <- 50  # 每批处理50条记录
  total_records <- nrow(missing_doi_records)
  total_batches <- ceiling(total_records / batch_size)
  
  log_message(sprintf("将分 %d 批处理，每批 %d 条记录", total_batches, batch_size))
  
  all_results <- data.frame()
  
  for (batch_num in 1:total_batches) {
    start_idx <- (batch_num - 1) * batch_size + 1
    end_idx <- min(batch_num * batch_size, total_records)
    
    batch_data <- missing_doi_records[start_idx:end_idx, ]
    batch_results <- process_batch(batch_data, batch_num, total_batches)
    
    all_results <- rbind(all_results, batch_results)
    
    # 保存中间结果
    temp_file <- here("data_repository", "04_enhancement_reports", 
                      sprintf("doi_completion_batch_%d.csv", batch_num))
    write_csv(batch_results, temp_file)
    
    log_message(sprintf("批次 %d 完成，中间结果已保存", batch_num))
    
    # 批次间休息
    if (batch_num < total_batches) {
      log_message("批次间休息 10 秒...")
      Sys.sleep(10)
    }
  }
  
  # 4. 统计和保存最终结果
  success_count <- sum(all_results$status == "success")
  success_rate <- 100 * success_count / total_records
  high_quality_count <- sum(!is.na(all_results$quality_score) & all_results$quality_score >= 0.7, na.rm = TRUE)
  review_needed_count <- sum(all_results$review_needed, na.rm = TRUE)
  
  log_message(sprintf("\n=== 最终统计结果 ==="))
  log_message(sprintf("总记录数: %d", total_records))
  log_message(sprintf("成功补全: %d (%.2f%%)", success_count, success_rate))
  log_message(sprintf("高质量匹配: %d (%.2f%%)", high_quality_count, 100 * high_quality_count / total_records))
  log_message(sprintf("需要人工审核: %d (%.2f%%)", review_needed_count, 100 * review_needed_count / total_records))
  
  # 保存最终结果
  output_file <- here("data_repository", "04_enhancement_reports", "doi_completion_batch_final.csv")
  write_csv(all_results, output_file)
  log_message(sprintf("最终结果已保存: %s", output_file))
  
  return(all_results)
}

# 运行主函数
if (interactive()) {
  results <- main()
} else {
  results <- main()
}

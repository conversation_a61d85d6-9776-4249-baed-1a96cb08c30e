# 00 研究框架总览

## 1. 研究背景与核心挑战
文献计量学在科学评价中扮演关键角色，但面临工具多样化与结果不一致的困境。CiteSpace、Bibliometrix、VOSviewer等工具因机制和算法差异，常对同一领域给出不同解读。**核心挑战**在于：
*   工具多样化与结果不一致问题。
*   输入数据质量和标准化是影响分析可靠性的关键瓶颈，其影响常被忽视。
*   传统分析侧重描述性统计，亟需挖掘深层因果机制、预测趋势，并引入先进计算方法。

## 2. 研究宗旨与目标
本研究旨在构建系统化分析框架，通过**两个数据处理路径**与**两个核心分析层面**来应对上述挑战：

*   **两个数据处理路径：**
    1.  **常规基线数据构建：**
        * 工具官方处理结果：
          - CiteSpace官方处理 → `datac` (对应 `data/baseline/citespace/datac.rds`)
          - VOSviewer官方处理 → `datav` (对应 `data/baseline/vosviewer/datav.rds`)
          - biblioshiny官方处理 → `datay` (对应 `data/processed/bibliometrix/stage1/datay.rds`)
        * bibliometrix极限处理 → `datax` (对应 `data/processed/bibliometrix/stage2/datax.rds`)按上述方案批量替换并优化
        * 目的：评估各工具的原始处理能力
    
    2.  **增强数据构建：**
        * 独立于常规基线数据
        * 采用更好的处理方法
        * 目的：探索更优的数据处理方案

*   **两个核心分析层面：**
    *   **层面一：验证数据增强的必要性**
        * 对比增强数据与常规基线数据（`datac`, `datav`, `datay`, `datax`）
        * 明确数据增强带来的改进
        * 证明高质量数据处理的必要性
    
    *   **层面二：明确工具间差异**
        * 初步处理阶段：
          - 评估各工具官方处理能力
          - 分析bibliometrix极限处理效果
        * 增强数据处理阶段：
          - 使用各个工具处理增强数据
          - 比较不同工具的处理效果
          - 分析工具间的差异和特点

**最终目标：**
*   提供数据处理必要性的实证依据。
*   提供选择和应用文献计量工具的科学指导。
*   提供解释和整合不同工具结果的一致性框架。
*   提供进行高质量文献计量分析的操作指南。

## 3. 整体研究框架流程概述
本研究设计了一个多阶段、目标驱动的研究流程，其核心在于**两个数据处理路径**和**两个核心分析层面**。主要流程包括：

1.  **数据基础构建与准备 (详见 `01_Data_Foundation_and_Preparation.md`):**
    *   获取"原始数据"
    *   构建"常规基线数据集"：
      - 使用各工具官方处理流程
      - 使用bibliometrix极限处理
    *   构建"增强数据集"：
      - 采用更好的处理方法
      - 完全独立于常规基线数据

2.  **核心比较分析方法论 (详见 `02_Comparative_Analysis_Methodology.md`):**
    *   执行"层面一"分析：验证数据增强的必要性
    *   执行"层面二"分析：明确工具间差异

3.  **分析模块详解与执行 (详见 `03_Analytical_Modules_and_Execution.md`):**
    *   定义并运用文献计量分析模块
    *   支持两个层面的分析需求

4.  **结果输出、标准化与应用 (详见 `04_Output_Standardization_and_Application.md`):**
    *   标准化分析结果
    *   指导研究成果的应用与解读

**框架优势：** 系统性、双层对比设计、数据质量核心、模块化分析、实践导向。

## 4. 预期研究创新与价值概要
*   **方法论创新：** 统一比较框架、多层次差异归因、集成分析范式。
*   **技术创新：** 高质量数据处理流程、参数等效映射、结果互操作框架。
*   **应用价值：** 方法选择指南、结果解释方法论、跨学科适用性。
*   **学术贡献：** 填补研究空白、推动标准化、促进方法发展。

## 5. 总结与展望
本框架通过创新的"两个数据处理路径"和"两个核心分析层面"设计，系统性地分离和评估了数据处理流程与分析工具本身对文献计量结果的影响，为研究者提供了从数据准备到高级分析的全面解决方案，旨在提升文献计量分析的科学性、可靠性和深度。 
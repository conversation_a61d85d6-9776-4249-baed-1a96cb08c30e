# DOI补全主流程脚本（参数化多模式）
# 支持批量补全、断点续跑、UT/元数据补全、Crossref查找
# mode参数：batch（批量）、resume（断点续跑）、by_ut（UT/元数据）、crossref（Crossref查找）
# 其余参数见脚本说明

suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
  library(tidyverse)
  library(openxlsx)
})

# --- 公共函数 ---
normalize_title <- function(title) {
  if (is.na(title)) return(NA)
  title <- tolower(title)
  title <- gsub("[[:punct:]]", " ", title)
  title <- gsub("\\s+", " ", title)
  trimws(title)
}

calculate_similarity <- function(str1, str2) {
  if (is.na(str1) || is.na(str2)) return(0)
  1 - stringdist(str1, str2, method="jw")
}

get_doi_from_crossref <- function(title, year) {
  tryCatch({
    clean_title <- normalize_title(title)
    title_words <- strsplit(clean_title, " \\s+")[[1]]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 5) keywords <- keywords[1:5]
    if (length(keywords) == 0) return(list(doi = NA, score = 0, similarity = 0))
    query_string <- paste(keywords, collapse = " ")
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=5", 
                   URLencode(query_string), as.numeric(year)-1, as.numeric(year)+1)
    response <- GET(url, user_agent("BiblioEnhancer/1.0"), timeout(30))
    if (status_code(response) != 200) return(list(doi = NA, score = 0, similarity = 0))
    content <- fromJSON(rawToChar(response$content))
    if (!is.null(content$message$items) && length(content$message$items) > 0) {
      items <- content$message$items
      best_match_idx <- NULL
      best_similarity <- 0
      for (i in 1:nrow(items)) {
        item <- items[i,]
        if (!is.null(item$title) && length(item$title) > 0) {
          candidate_title <- item$title[[1]]
          current_similarity <- calculate_similarity(normalize_title(title), normalize_title(candidate_title))
          if (current_similarity > best_similarity) {
            best_similarity <- current_similarity
            best_match_idx <- i
          }
        }
      }
      if (!is.null(best_match_idx) && best_similarity > 0.3) {
        doi_found <- items$DOI[best_match_idx]
        crossref_score <- items$score[best_match_idx]
        if (!is.na(doi_found) && doi_found != "") {
          return(list(doi = doi_found, score = crossref_score, similarity = best_similarity))
        }
      }
    }
    return(list(doi = NA, score = 0, similarity = 0))
  }, error = function(e) {
    return(list(doi = NA, score = 0, similarity = 0))
  })
}

get_doi_by_metadata <- function(title, author, year) {
  url <- sprintf("https://api.crossref.org/works?query.bibliographic=%s&query.author=%s&query.published=%s&rows=1", 
                 URLencode(title), URLencode(author), year)
  response <- tryCatch({
    GET(url, user_agent("BiblioEnhancer/1.0 (mailto:<EMAIL>)"))
  }, error = function(e) { return(NULL) })
  if (is.null(response) || status_code(response) != 200) return(NA)
  content <- tryCatch({ fromJSON(rawToChar(response$content)) }, error = function(e) { return(NULL) })
  if (!is.null(content) && length(content$message$items) > 0) {
    doi <- content$message$items$DOI[1]
    if (!is.null(doi) && doi != "") return(doi)
  }
  return(NA)
}

# --- 主流程 ---
args <- commandArgs(trailingOnly = TRUE)
mode <- ifelse(length(args) >= 1, args[[1]], "batch")
input_file <- ifelse(length(args) >= 2, args[[2]], "data_repository/04_enhancement_reports/missing_doi_records.csv")
output_dir <- ifelse(length(args) >= 3, args[[3]], "data_repository/04_enhancement_reports")

if (mode == "batch") {
  cat("=== 批量DOI补全模式 ===\n")
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  results <- data.frame(
    UT = data$UT, TI = data$TI, AU = data$AU, PY = data$PY, SO = data$SO, VL = data$VL, IS = data$IS, PG = data$PG,
    DOI = NA, confidence = NA, similarity = NA, quality_score = NA, review_needed = FALSE, match_details = "", status = "pending",
    stringsAsFactors = FALSE
  )
  for (i in 1:nrow(data)) {
    doi_result <- get_doi_from_crossref(results$TI[i], results$PY[i])
    if (!is.na(doi_result$doi)) {
      results$DOI[i] <- doi_result$doi
      results$confidence[i] <- doi_result$score
      results$similarity[i] <- doi_result$similarity
      results$status[i] <- "success"
      quality_score <- doi_result$similarity * 0.7 + (doi_result$score / 100) * 0.3
      results$quality_score[i] <- quality_score
      if (quality_score < 0.6 || doi_result$similarity < 0.5 || doi_result$score < 30) {
        results$review_needed[i] <- TRUE
        results$match_details[i] <- sprintf("低置信度匹配 (相似度:%.2f, Crossref分数:%.1f)", doi_result$similarity, doi_result$score)
      } else {
        results$match_details[i] <- sprintf("高置信度匹配 (相似度:%.2f, Crossref分数:%.1f)", doi_result$similarity, doi_result$score)
      }
    } else {
      results$status[i] <- "failed"
      results$match_details[i] <- "未找到匹配的DOI"
    }
    if (i %% 100 == 0) {
      temp_file <- file.path(output_dir, sprintf("doi_completion_temp_%d.csv", i))
      write.csv(results[1:i, ], temp_file, row.names = FALSE)
    }
    Sys.sleep(1.5)
  }
  write.csv(results, file.path(output_dir, "doi_completion_final_results.csv"), row.names = FALSE)
  cat("批量DOI补全完成，结果已保存。\n")
}

if (mode == "resume") {
  cat("=== 断点续跑模式 ===\n")
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  temp_file <- file.path(output_dir, "doi_completion_temp_100.csv")
  if (file.exists(temp_file)) {
    processed_results <- read.csv(temp_file, stringsAsFactors = FALSE)
    start_index <- nrow(processed_results) + 1
  } else {
    start_index <- 1
    processed_results <- NULL
  }
  results <- data.frame(
    UT = data$UT, TI = data$TI, AU = data$AU, PY = data$PY, SO = data$SO, VL = data$VL, IS = data$IS, PG = data$PG,
    DOI = NA, confidence = NA, similarity = NA, quality_score = NA, review_needed = FALSE, match_details = "", status = "pending",
    stringsAsFactors = FALSE
  )
  if (!is.null(processed_results)) {
    for (i in 1:nrow(processed_results)) {
      results[i, ] <- processed_results[i, ]
    }
  }
  for (i in start_index:nrow(data)) {
    doi_result <- get_doi_from_crossref(results$TI[i], results$PY[i])
    if (!is.na(doi_result$doi)) {
      results$DOI[i] <- doi_result$doi
      results$confidence[i] <- doi_result$score
      results$similarity[i] <- doi_result$similarity
      results$status[i] <- "success"
      quality_score <- doi_result$similarity * 0.7 + (doi_result$score / 100) * 0.3
      results$quality_score[i] <- quality_score
      if (quality_score < 0.6 || doi_result$similarity < 0.5 || doi_result$score < 30) {
        results$review_needed[i] <- TRUE
        results$match_details[i] <- sprintf("低置信度匹配 (相似度:%.2f, 分数:%.1f)", doi_result$similarity, doi_result$score)
      } else {
        results$match_details[i] <- sprintf("高置信度匹配 (相似度:%.2f, 分数:%.1f)", doi_result$similarity, doi_result$score)
      }
    } else {
      results$status[i] <- "failed"
      results$match_details[i] <- "未找到匹配的DOI"
    }
    if (i %% 100 == 0) {
      temp_file_new <- file.path(output_dir, sprintf("doi_completion_temp_%d.csv", i))
      write.csv(results[1:i, ], temp_file_new, row.names = FALSE)
    }
    Sys.sleep(1.5)
  }
  write.csv(results, file.path(output_dir, "doi_completion_full_final.csv"), row.names = FALSE)
  cat("断点续跑完成，结果已保存。\n")
}

if (mode == "by_ut") {
  cat("=== 基于UT/元数据DOI补全模式 ===\n")
  missing_doi_records <- read_csv(input_file, show_col_types = FALSE)
  results <- data.frame(
    UT = missing_doi_records$UT, TI = missing_doi_records$TI, AU = missing_doi_records$AU, PY = missing_doi_records$PY, SO = missing_doi_records$SO,
    DOI = NA, status = "pending", stringsAsFactors = FALSE
  )
  for (i in 1:nrow(results)) {
    doi <- get_doi_by_metadata(results$TI[i], results$AU[i], results$PY[i])
    results$DOI[i] <- doi
    results$status[i] <- ifelse(is.na(doi), "failed", "success")
    Sys.sleep(1)
  }
  saveRDS(results, file.path(output_dir, "doi_completion_by_metadata.rds"))
  wb <- createWorkbook(); addWorksheet(wb, "补全结果"); writeData(wb, "补全结果", results)
  saveWorkbook(wb, file.path(output_dir, "doi_completion_by_metadata.xlsx"), overwrite = TRUE)
  cat("UT/元数据DOI补全完成，结果已保存。\n")
}

if (mode == "crossref") {
  cat("=== Crossref查找模式（批量） ===\n")
  # 这里只做简单批量查找示例
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  results <- data.frame(
    title = data$TI, authors = data$AU, year = data$PY, found_doi = NA_character_, lookup_status = "未处理", lookup_score = NA_real_,
    stringsAsFactors = FALSE
  )
  for (i in 1:nrow(results)) {
    results$found_doi[i] <- get_doi_from_crossref(results$title[i], results$year[i])$doi
    results$lookup_status[i] <- ifelse(is.na(results$found_doi[i]), "未找到", "成功")
    Sys.sleep(1)
  }
  write.csv(results, file.path(output_dir, "crossref_doi_lookup_results.csv"), row.names = FALSE)
  cat("Crossref查找批量完成，结果已保存。\n")
} 
# 数据处理流程指南

## 📋 标准处理流程

按照数据处理的逻辑顺序，重新整理了R脚本的编号和命名：

### 阶段1: 数据导入与转换 (01-03)
```
01_import_wos_data.R           # WoS原始数据导入与bibliometrix转换
02_import_citespace_data.R     # CiteSpace数据导入与处理  
03_import_vosviewer_data.R     # VOSviewer数据导入与处理
```

### 阶段2: 数据验证与清理 (04)
```
04_validate_and_clean_data.R   # 数据验证、异常检测与基础清理
```

### 阶段3: 去重处理 (05-06)
```
05_deduplicate_records.R       # 增强去重处理 (主要版本)
06_deduplicate_advanced.R      # 高级多轮去重处理 (可选版本)
```

### 阶段4: 数据增强与补全 (07-09)
```
07_enhance_data_comprehensive.R  # 综合数据增强处理
08_complete_missing_dois.R       # DOI补全处理
09_integrate_enhanced_data.R     # 增强数据整合
```

### 阶段5: 质量控制与报告 (10)
```
10_quality_control_and_report.R  # 质量控制与最终报告生成
```

## 🔄 处理顺序说明

### 为什么这样排序？

1. **数据导入优先** (01-03): 必须先有数据才能进行后续处理
2. **验证在去重前** (04): 确保数据质量，避免在脏数据上去重
3. **去重在增强前** (05-06): 减少数据量，提高增强效率
4. **DOI补全在数据增强中** (08): DOI补全是数据增强的重要组成部分
5. **质量控制在最后** (10): 对最终结果进行全面评估

### 依赖关系

```
01-03 (导入) → 04 (验证) → 05-06 (去重) → 07-09 (增强) → 10 (质量控制)
```

## 🎯 使用方法

### 完整流程执行
```r
# 按顺序执行所有脚本
source("R/01_import_wos_data.R")
source("R/02_import_citespace_data.R") 
source("R/03_import_vosviewer_data.R")
source("R/04_validate_and_clean_data.R")
source("R/05_deduplicate_records.R")
source("R/07_enhance_data_comprehensive.R")
source("R/08_complete_missing_dois.R")
source("R/09_integrate_enhanced_data.R")
source("R/10_quality_control_and_report.R")
```

### 批量执行
```r
# 使用config.R中的批量执行函数
source("R/config.R")
execute_pipeline("data_processing")
```

### 部分流程执行
```r
# 只执行数据增强部分
source("R/07_enhance_data_comprehensive.R")
source("R/08_complete_missing_dois.R")
source("R/09_integrate_enhanced_data.R")
```

## 📁 文件对应关系

### 重命名映射
```
旧文件名                        → 新文件名
01_data_import_wos.R           → 01_import_wos_data.R
02_data_import_citespace.R     → 02_import_citespace_data.R
03_data_import_vosviewer.R     → 03_import_vosviewer_data.R
04_data_validation.R           → 04_validate_and_clean_data.R
05_deduplication_enhanced.R    → 05_deduplicate_records.R
06_doi_completion.R            → 08_complete_missing_dois.R
07_data_enhancement.R          → 07_enhance_data_comprehensive.R
08_data_integration.R          → 09_integrate_enhanced_data.R
09_quality_control.R           → 10_quality_control_and_report.R
```

### 新增文件
```
06_deduplicate_advanced.R      # 来自enhanced/02_deduplication_enhanced_advanced.R
```

## ⚠️ 重要说明

1. **备份保护**: 所有原始文件已备份到 `BACKUP_BEFORE_REORGANIZATION/`
2. **功能保持**: 重新整理只改变文件名和顺序，不改变核心功能
3. **配置兼容**: 新脚本与现有配置文件兼容
4. **渐进迁移**: 可以逐步迁移到新的命名体系

## 🎉 优势

1. **逻辑清晰**: 文件名直接反映处理步骤
2. **顺序明确**: 编号与实际处理顺序一致
3. **易于理解**: 新用户可以快速理解处理流程
4. **便于维护**: 清晰的结构便于代码维护和扩展

重新整理后的脚本结构更加符合数据处理的实际流程！


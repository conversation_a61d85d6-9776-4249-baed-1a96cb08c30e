% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/normalizeCItationScore.R
\name{normalizeCitationScore}
\alias{normalizeCitationScore}
\title{Calculate the normalized citation score metric}
\usage{
normalizeCitationScore(M, field = "documents", impact.measure = "local")
}
\arguments{
\item{M}{is a bibliographic data frame obtained by \code{\link{convert2df}} function.}

\item{field}{is a character. It indicates the unit of analysis on which calculate the NCS. It can be equal to \code{field = c("documents", "authors", "sources")}. Default is \code{field = "documents"}.}

\item{impact.measure}{is a character. It indicates the impact measure used to rank cluster elements (documents, authors or sources).
It can be \code{impact.measure = c("local", "global")}.\\
With \code{impact.measure = "local"}, \link{normalizeCitationScore} calculates elements impact using the Normalized Local Citation Score while 
using \code{impact.measure = "global"}, the function uses the Normalized Global Citation Score to measure elements impact.}
}
\value{
a dataframe.
}
\description{
It calculates the normalized citation score for documents, authors and sources using both global and local citations.
}
\details{
The document Normalized Citation Score (NCS) of a document is calculated by dividing the actual count of citing items by the expected 
citation rate for documents with the same year of publication. 

The MNCS of a set of documents, for example the collected works of an individual, or published on a journal, is the average of the NCS values for all the documents in the set.

The NGCS is the NCS calculated using the global citations (total citations that a document received considering the whole bibliographic database).

The NLCS is the NCS calculated using the local citations (total citations that a document received from a set of documents included in the same collection).
}
\examples{

\dontrun{
data(management, package = "bibliometrixData")
NCS <- normalizeCitationScore(management, field = "authors", impact.measure = "local")
}

}

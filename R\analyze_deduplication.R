# 加载必要的包
library(bibliometrix)
library(dplyr)

# 读取数据
datay <- readRDS("data_repository/01_baseline_datasets/bibliometrix_processed/datay_bibliometrix_biblioshiny.rds")
datax <- readRDS("data_repository/01_baseline_datasets/bibliometrix_processed/datax_bibliometrix_advanced.rds")

# 基本统计
cat("=== 基本统计 ===\n")
cat("datay记录数:", nrow(datay), "\n")
cat("datax记录数:", nrow(datax), "\n")
cat("差异记录数:", nrow(datay) - nrow(datax), "\n")

# 找出被去重的记录
removed_records <- anti_join(datay, datax, by = "UT")
cat("\n=== 被去重记录分析 ===\n")
cat("被去重记录数:", nrow(removed_records), "\n")

# 分析被去重记录的特征
if(nrow(removed_records) > 0) {
  # 年份分布
  year_dist <- table(removed_records$PY)
  cat("\n被去重记录的年份分布:\n")
  print(year_dist)
  
  # 文献类型分布
  type_dist <- table(removed_records$DT)
  cat("\n被去重记录的文献类型分布:\n")
  print(type_dist)
  
  # 保存被去重记录
  write.csv(removed_records, 
            "data_repository/06_data_reports/removed_records_analysis.csv", 
            row.names = FALSE)
}

# 保存分析报告
sink("data_repository/06_data_reports/deduplication_comparison_report.txt")
cat("=== 去重效果对比分析报告 ===\n\n")
cat("生成时间:", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n")
cat("1. 基本统计\n")
cat("   datay记录数:", nrow(datay), "\n")
cat("   datax记录数:", nrow(datax), "\n")
cat("   差异记录数:", nrow(datay) - nrow(datax), "\n\n")
if(nrow(removed_records) > 0) {
  cat("2. 被去重记录分析\n")
  cat("   被去重记录数:", nrow(removed_records), "\n")
  cat("   年份分布:\n")
  print(year_dist)
  cat("\n   文献类型分布:\n")
  print(type_dist)
}
sink() 
# 简单的多引擎DOI补全测试

cat("=== 简单多引擎DOI补全测试 ===\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 加载多引擎系统
tryCatch({
  source("doi_completion_multi_engine.R")
  cat("✅ 多引擎系统加载成功\n")
}, error = function(e) {
  cat("❌ 加载多引擎系统失败:", e$message, "\n")
  quit(status = 1)
})

# 测试用例
test_title <- "Machine learning in healthcare"
test_authors <- "Johnson A"
test_year <- 2020
test_journal <- "Nature Medicine"

cat(sprintf("\n🔍 测试DOI搜索:\n"))
cat(sprintf("标题: %s\n", test_title))
cat(sprintf("作者: %s | 年份: %s | 期刊: %s\n\n", test_authors, test_year, test_journal))

# 执行搜索
cat("开始搜索...\n")
result <- search_doi_multi_engine(
  title = test_title,
  authors = test_authors,
  year = test_year,
  journal = test_journal,
  prefer_crossref = TRUE
)

# 显示结果
if (!is.null(result)) {
  quality <- assess_quality(result$title_similarity, result$final_score)
  cat(sprintf("\n✅ 成功找到DOI!\n"))
  cat(sprintf("DOI: %s\n", result$doi))
  cat(sprintf("来源: %s\n", result$source))
  cat(sprintf("质量等级: %s\n", quality))
  cat(sprintf("最终评分: %.3f\n", result$final_score))
  cat(sprintf("标题相似度: %.3f\n", result$title_similarity))
  cat(sprintf("期刊相似度: %.3f\n", result$journal_similarity))
  cat(sprintf("年份相似度: %.3f\n", result$year_similarity))
  cat(sprintf("学科相关性: %.3f\n", result$subject_relevance))
  
  cat(sprintf("\n📋 匹配详情:\n"))
  cat(sprintf("匹配标题: %s\n", result$title))
  cat(sprintf("匹配期刊: %s\n", result$journal))
  cat(sprintf("匹配年份: %s\n", result$year))
} else {
  cat("\n❌ 未找到匹配的DOI\n")
}

cat(sprintf("\n✅ 测试完成\n"))

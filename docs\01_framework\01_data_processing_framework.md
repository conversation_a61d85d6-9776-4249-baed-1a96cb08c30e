# 文献计量分析数据处理框架

## 1. 数据处理层次

### 1.1 数据层次划分
- **原始数据层**：直接从数据源获取的未经处理的原始数据
- **常规基线数据层**：通过官方工具（如bibliometrix::convert2df）生成的基础数据
- **增强数据层**：采用更严格和多维度处理后的数据

### 1.2 数据文件命名规范
- 原始数据：`dataa_*.rds`
- 常规基线数据：`datay_*.rds`
- 增强数据：`datax_*.rds`

## 2. 数据处理流程

### 2.1 数据导入与转换
- 使用`01_wos_convert2df.R`进行WoS数据格式转换
- 基于UT字段进行基础去重
- 生成字段缺失值统计报告

### 2.2 去重策略
#### 2.2.1 官方去重（Biblioshiny标准）
- 基于UT字段的精确去重
- 基于标题的模糊匹配（阈值：0.95）

#### 2.2.2 自定义去重（增强版）
- DOI精确匹配
- 标题多轮模糊匹配（阈值：0.98, 0.95, 0.90）
- 作者模糊匹配（阈值：0.95）
- 摘要模糊匹配（阈值：0.90）

#### 2.2.3 极限去重（资源密集型）
- 更严格的匹配阈值
- 更复杂的匹配规则
- 需要更多的计算资源

## 3. 内存管理与性能优化

### 3.1 内存管理策略
#### 3.1.1 数据预处理优化
```r
# 统一数据类型，避免运行时转换
M_before_step[[field]] <- as.character(M_before_step[[field]])
M_before_step[[field]][is.na(M_before_step[[field]])] <- ""

# 分块处理数据
M_to_process <- M_before_step
M_skipped_due_to_empty_field <- data.frame()
M_skipped_due_to_no_doi <- data.frame()
```

#### 3.1.2 空值处理优化
```r
# 预处理空字段，减少计算量
empty_target_field_rows <- which(M_before_step[[field]] == "")
if (length(empty_target_field_rows) > 0) {
    M_to_process <- M_before_step[-empty_target_field_rows, , drop = FALSE]
    M_skipped_due_to_empty_field <- M_before_step[empty_target_field_rows, , drop = FALSE]
}
```

### 3.2 性能优化策略
#### 3.2.1 相似度计算优化
```r
# 使用矩阵运算代替循环
D <- as.matrix(stringdistmatrix(a))
Dn <- 1-(D/C)
Dn[Dn>tol] <- 2
M <- M[!duplicated(Dn),]
```

#### 3.2.2 渐进式去重策略
```r
# 配置中的去重阈值设置
deduplication = list(
    title_tolerances = c(0.98, 0.95, 0.90),  # 从严格到宽松
    author_tolerance = 0.95,
    abstract_tolerance = 0.90
)
```

### 3.3 资源限制处理
#### 3.3.1 Windows系统
```r
# 增加内存限制
memory.limit(size = 16000)  # 设置16GB内存限制
options(expressions = 500000)  # 增加R表达式递归限制
```

#### 3.3.2 Unix系统
```r
# 设置栈大小
system("ulimit -s 65536")  # 设置64MB栈大小
```

## 4. 日志与监控

### 4.1 日志记录
- 记录每个去重步骤的开始和结束
- 记录数据量变化
- 记录处理时间

### 4.2 性能监控
- 记录内存使用情况
- 记录处理时间
- 记录去重效果

## 5. 质量控制

### 5.1 数据验证
- 检查必要字段的存在性
- 验证数据类型的正确性
- 检查去重结果的完整性

### 5.2 结果评估
- 比较不同去重策略的效果
- 评估去重对后续分析的影响
- 生成去重效果报告

## 6. 文档规范

### 6.1 代码文档
- 函数说明
- 参数说明
- 返回值说明
- 使用示例

### 6.2 处理报告
- 数据概况
- 处理步骤
- 结果统计
- 异常记录

## 7. 维护与更新

### 7.1 代码维护
- 定期检查代码效率
- 优化内存使用
- 更新处理策略

### 7.2 文档维护
- 更新处理流程
- 补充新的优化策略
- 记录问题解决方案 
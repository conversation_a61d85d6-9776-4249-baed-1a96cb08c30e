# 文献计量分析数据处理框架 (v2.0)

## 1. 框架概述

本框架建立了一个标准化、模块化的文献计量分析数据处理流程，采用分层数据管理和统一配置系统，确保数据质量和分析结果的可靠性。

### 1.1 设计原则
- **模块化设计**：每个处理阶段独立，便于维护和扩展
- **配置驱动**：统一的配置文件管理所有参数
- **质量优先**：严格的质量控制和验证机制
- **可追溯性**：完整的处理日志和报告

## 2. 项目结构

### 2.1 数据目录结构
```
data_repository/
├── 01_raw_data/              # 原始数据
│   ├── wos_files/            # WoS原始文件
│   └── metadata/             # 元数据信息
├── 02_baseline_data/         # 基线数据
│   ├── bibliometrix/         # bibliometrix处理结果
│   ├── citespace/            # CiteSpace处理结果
│   └── vosviewer/            # VOSviewer处理结果
├── 03_enhanced_data/         # 增强数据
│   ├── deduplication/        # 去重结果
│   ├── doi_completion/       # DOI补全结果
│   └── validation/           # 验证结果
├── 04_analysis_outputs/      # 分析输出
│   ├── networks/             # 网络分析
│   ├── trends/               # 趋势分析
│   └── collaboration/        # 合作分析
├── 05_reports/               # 报告文件
│   ├── processing/           # 处理报告
│   ├── quality/              # 质量报告
│   └── final/                # 最终报告
├── 06_cache/                 # 缓存文件
│   ├── api_cache/            # API缓存
│   └── temp/                 # 临时文件
└── 07_logs/                  # 日志文件
    ├── processing/           # 处理日志
    └── errors/               # 错误日志
```

### 2.2 R脚本结构
```
R/
├── 01_data_import.R          # 数据导入与转换
├── 04_deduplication.R        # 去重处理
├── 05_doi_completion.R       # DOI补全
├── 07_quality_control.R      # 质量控制
├── config.R                  # 项目配置
├── utils/                    # 工具函数
└── archive/                  # 归档脚本
```

### 2.3 数据文件命名规范
- **原始数据**: `dataa_*.rds` (Raw data)
- **基线数据**: `datay_*.rds` (Baseline data)
- **增强数据**: `datax_*.rds` (Enhanced data)

## 3. 数据处理流程

### 3.1 阶段1: 数据导入 (01_data_import.R)
**功能**: 将WoS原始文件转换为标准化格式
- 使用bibliometrix::convert2df进行格式转换
- 基于UT字段进行基础去重
- 生成字段缺失值统计报告
- 数据验证和质量检查

**输入**: `01_raw_data/wos_files/*.txt`
**输出**: `02_baseline_data/bibliometrix/datay_bibliometrix_initial.rds`

### 3.2 阶段2: 去重处理 (04_deduplication.R)
**功能**: 多维度去重，提高数据质量
- DOI精确匹配去重
- 标题多轮模糊匹配（阈值：0.98, 0.95, 0.90）
- 作者模糊匹配（阈值：0.95）
- 摘要模糊匹配（阈值：0.90）

**输入**: `02_baseline_data/bibliometrix/datay_*.rds`
**输出**: `03_enhanced_data/deduplication/datax_*.rds`

### 3.3 阶段3: DOI补全 (05_doi_completion.R)
**功能**: 基于学术标准的DOI补全系统
- Crossref API文献检索
- 多维度相似度验证（标题+期刊+年份+学科）
- 严格的质量控制阈值
- 四级质量评估体系

**输入**: `03_enhanced_data/deduplication/datax_*.rds`
**输出**: `03_enhanced_data/doi_completion/COMPLETE_DOI_RESULTS.csv`

### 3.4 阶段4: 质量控制 (07_quality_control.R)
**功能**: 全面的数据质量评估和报告
- 数据完整性检查
- 字段质量评估
- 处理效果统计
- 质量报告生成

**输入**: 各阶段处理结果
**输出**: `05_reports/quality/*.md`

## 4. 配置管理

### 4.1 统一配置文件 (config.R)
```r
PROJECT_CONFIG <- list(
  # 路径配置
  paths = list(
    raw_data = "data_repository/01_raw_data",
    baseline_data = "data_repository/02_baseline_data",
    enhanced_data = "data_repository/03_enhanced_data",
    analysis_outputs = "data_repository/04_analysis_outputs",
    reports = "data_repository/05_reports",
    cache = "data_repository/06_cache",
    logs = "data_repository/07_logs"
  ),

  # 处理参数
  processing = list(
    deduplication = list(
      title_tolerances = c(0.98, 0.95, 0.90),
      author_tolerance = 0.95,
      abstract_tolerance = 0.90
    ),
    doi_completion = list(
      title_threshold = 0.75,
      journal_threshold = 0.4,
      year_threshold = 0.5,
      subject_threshold = 0.8,
      final_threshold = 0.65
    )
  )
)
```

### 4.2 配置使用方法
```r
# 加载配置
source("R/config.R")

# 获取路径
raw_data_path <- get_path("raw_data")

# 获取参数
dedup_params <- get_config("processing", "deduplication")
```

## 5. 质量控制体系

### 5.1 数据验证
- **字段完整性检查**: 验证必需字段的存在性
- **数据类型验证**: 确保数据类型的正确性
- **值域检查**: 验证数据值的合理性
- **一致性检查**: 检查数据间的逻辑一致性

### 5.2 处理质量评估
- **去重效果评估**: 统计去重前后的数据变化
- **DOI补全质量**: 四级质量评估体系
- **数据完整性**: 字段缺失率统计
- **处理效率**: 时间和资源消耗统计

### 5.3 质量报告
- **处理报告**: 每个阶段的详细处理记录
- **质量报告**: 数据质量评估结果
- **异常报告**: 处理过程中的异常记录
- **最终报告**: 整体处理效果总结

## 6. 性能优化

### 6.1 内存管理
- **分批处理**: 大数据集分批处理，避免内存溢出
- **数据预处理**: 提前过滤无效数据，减少计算量
- **垃圾回收**: 及时清理临时变量，释放内存

### 6.2 计算优化
- **矩阵运算**: 使用向量化操作代替循环
- **并行处理**: 利用多核CPU进行并行计算
- **缓存机制**: API调用结果缓存，避免重复请求

### 6.3 资源管理
- **API限制**: 控制API调用频率，避免被限制
- **临时文件**: 及时清理临时文件，节省存储空间
- **日志管理**: 合理的日志级别，避免日志文件过大

## 7. 使用指南

### 7.1 快速开始
```r
# 1. 加载配置
source("R/config.R")

# 2. 数据导入
source("R/01_data_import.R")

# 3. 去重处理
source("R/04_deduplication.R")

# 4. DOI补全
source("R/05_doi_completion.R")

# 5. 质量控制
source("R/07_quality_control.R")
```

### 7.2 自定义配置
- 修改`R/config.R`中的参数设置
- 调整处理阈值和算法参数
- 配置输出路径和文件格式

### 7.3 扩展开发
- 在`R/utils/`目录下添加工具函数
- 创建新的处理模块
- 更新配置文件以支持新功能

## 8. 维护与更新

### 8.1 定期维护
- **代码审查**: 定期检查代码质量和效率
- **性能监控**: 监控处理时间和资源使用
- **文档更新**: 保持文档与代码同步

### 8.2 版本管理
- **版本控制**: 使用Git进行版本管理
- **变更记录**: 记录重要的变更和改进
- **向后兼容**: 保持配置文件的向后兼容性

### 8.3 问题解决
- **日志分析**: 通过日志文件诊断问题
- **错误处理**: 完善的错误处理和恢复机制
- **用户支持**: 提供详细的使用文档和示例

## 4. 日志与监控

### 4.1 日志记录
- 记录每个去重步骤的开始和结束
- 记录数据量变化
- 记录处理时间

### 4.2 性能监控
- 记录内存使用情况
- 记录处理时间
- 记录去重效果

## 5. 质量控制

### 5.1 数据验证
- 检查必要字段的存在性
- 验证数据类型的正确性
- 检查去重结果的完整性

### 5.2 结果评估
- 比较不同去重策略的效果
- 评估去重对后续分析的影响
- 生成去重效果报告

## 6. 文档规范

### 6.1 代码文档
- 函数说明
- 参数说明
- 返回值说明
- 使用示例

### 6.2 处理报告
- 数据概况
- 处理步骤
- 结果统计
- 异常记录

## 7. 维护与更新

### 7.1 代码维护
- 定期检查代码效率
- 优化内存使用
- 更新处理策略

### 7.2 文档维护
- 更新处理流程
- 补充新的优化策略
- 记录问题解决方案 
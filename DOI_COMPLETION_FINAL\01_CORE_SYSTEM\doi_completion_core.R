# DOI补全核心算法 - 最终版本
# 基于学术标准的高精度DOI补全系统

library(httr)
library(jsonlite)
library(stringdist)

# 文本标准化
normalize_text <- function(text) {
  if (is.na(text) || text == "") return("")
  text <- tolower(text)
  text <- gsub("[[:punct:]]", " ", text)
  text <- gsub("\s+", " ", text)
  text <- trimws(text)
  
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "study", "analysis")
  words <- strsplit(text, "\s+")[[1]]
  words <- words[!words %in% stop_words & nchar(words) > 2]
  
  return(paste(words, collapse = " "))
}

# 标题相似度计算
calculate_title_similarity <- function(text1, text2) {
  norm1 <- normalize_text(text1)
  norm2 <- normalize_text(text2)
  
  if (norm1 == "" || norm2 == "") return(0)
  
  # J<PERSON><PERSON><PERSON>相似度
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  
  # 关键词匹配奖励
  words1 <- strsplit(norm1, "\s+")[[1]]
  words2 <- strsplit(norm2, "\s+")[[1]]
  
  important_words1 <- words1[nchar(words1) > 4]
  important_words2 <- words2[nchar(words2) > 4]
  
  if (length(important_words1) > 0 && length(important_words2) > 0) {
    common_important <- sum(important_words1 %in% important_words2)
    keyword_bonus <- common_important / max(length(important_words1), length(important_words2))
    
    if (common_important == 0 && similarity < 0.9) {
      similarity <- similarity * 0.7
    } else {
      similarity <- min(1.0, similarity + keyword_bonus * 0.1)
    }
  }
  
  return(similarity)
}

# 期刊匹配度计算
calculate_journal_similarity <- function(journal1, journal2) {
  if (is.na(journal1) || is.na(journal2) || journal1 == "" || journal2 == "") return(0.3)
  
  # 期刊名称标准化
  normalize_journal <- function(journal) {
    journal <- tolower(journal)
    journal <- gsub("^the\s+", "", journal)
    journal <- gsub("\s*\(.*\)$", "", journal)
    journal <- gsub("[[:punct:]]", " ", journal)
    journal <- gsub("\s+", " ", journal)
    return(trimws(journal))
  }
  
  norm1 <- normalize_journal(journal1)
  norm2 <- normalize_journal(journal2)
  
  if (norm1 == norm2) return(1.0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  
  # 关键词匹配
  words1 <- strsplit(norm1, "\s+")[[1]][nchar(strsplit(norm1, "\s+")[[1]]) > 3]
  words2 <- strsplit(norm2, "\s+")[[1]][nchar(strsplit(norm2, "\s+")[[1]]) > 3]
  
  if (length(words1) > 0 && length(words2) > 0) {
    common_words <- sum(words1 %in% words2)
    if (common_words == 0) {
      similarity <- similarity * 0.5
    } else {
      keyword_similarity <- common_words / max(length(words1), length(words2))
      similarity <- max(similarity, keyword_similarity)
    }
  }
  
  return(similarity)
}

# 年份匹配度计算
calculate_year_similarity <- function(year1, year2) {
  if (is.na(year1) || is.na(year2)) return(0.3)
  
  year1 <- as.numeric(year1)
  year2 <- as.numeric(year2)
  
  if (year1 == year2) return(1.0)
  if (abs(year1 - year2) == 1) return(0.8)
  if (abs(year1 - year2) == 2) return(0.5)
  return(0.0)
}

# 学科相关性检查
check_subject_relevance <- function(title1, title2) {
  medical_keywords <- c("muscle", "anatomy", "medical", "clinical", "patient", "treatment", "therapy", "disease", "pain", "jaw", "dental", "oral", "mandibular", "temporomandibular", "masticatory", "orthodontic")
  biology_keywords <- c("animal", "rat", "mouse", "rabbit", "guinea", "pig", "cell", "tissue", "bone", "development", "growth", "physiology")
  
  title1_lower <- tolower(title1)
  title2_lower <- tolower(title2)
  
  medical1 <- any(sapply(medical_keywords, function(x) grepl(x, title1_lower)))
  medical2 <- any(sapply(medical_keywords, function(x) grepl(x, title2_lower)))
  
  biology1 <- any(sapply(biology_keywords, function(x) grepl(x, title1_lower)))
  biology2 <- any(sapply(biology_keywords, function(x) grepl(x, title2_lower)))
  
  if ((medical1 && !medical2 && !biology2) || (medical2 && !medical1 && !biology1)) {
    return(0.3)
  }
  
  return(1.0)
}

# DOI搜索主函数
search_doi <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, "\s+")[[1]]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    query_string <- paste(keywords, collapse = " ")
    
    # Crossref API查询
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=15", 
                   URLencode(query_string), as.numeric(year)-2, as.numeric(year)+2)
    
    response <- GET(url, user_agent("DOI_Completion/1.0"), timeout(30))
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$message$items) || length(content$message$items) == 0) return(NULL)
    
    items <- content$message$items
    best_match <- NULL
    best_score <- 0
    
    # 评估每个候选结果
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      candidate_title <- if (!is.null(item$title) && length(item$title) > 0) item$title[[1]] else ""
      candidate_journal <- if (!is.null(item$`container-title`) && length(item$`container-title`) > 0) item$`container-title`[[1]] else ""
      candidate_year <- ""
      
      if (!is.null(item$`published-print`$`date-parts`)) {
        candidate_year <- item$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(item$`published-online`$`date-parts`)) {
        candidate_year <- item$`published-online`$`date-parts`[[1]][[1]]
      }
      
      # 计算各维度相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      journal_sim <- calculate_journal_similarity(journal, candidate_journal)
      year_sim <- calculate_year_similarity(year, candidate_year)
      subject_rel <- check_subject_relevance(title, candidate_title)
      
      # 综合评分 (权重: 标题50%, 期刊25%, 年份15%, 学科10%)
      final_score <- (title_sim * 0.5) + (journal_sim * 0.25) + (year_sim * 0.15) + (subject_rel * 0.1)
      
      # 严格的接受条件
      if (title_sim >= 0.75 &&           # 标题相似度阈值
          journal_sim >= 0.4 &&          # 期刊匹配度阈值
          year_sim >= 0.5 &&             # 年份匹配度阈值
          subject_rel >= 0.8 &&          # 学科相关性阈值
          final_score >= 0.65 &&         # 综合评分阈值
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = item$DOI,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          subject_relevance = subject_rel,
          final_score = final_score
        )
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    return(NULL)
  })
}

# 质量评级函数
assess_quality <- function(title_sim, final_score) {
  if (title_sim >= 0.95 && final_score >= 0.85) {
    return("卓越")
  } else if (title_sim >= 0.85 && final_score >= 0.75) {
    return("优秀")
  } else if (title_sim >= 0.75 && final_score >= 0.65) {
    return("良好")
  } else {
    return("可接受")
  }
}

# 使用示例:
# result <- search_doi("MR imaging of muscles of mastication", "Smith J", 1995, "American Journal of Neuroradiology")
# if (!is.null(result)) {
#   quality <- assess_quality(result$title_similarity, result$final_score)
#   cat(sprintf("DOI: %s (质量: %s, 评分: %.3f)\n", result$doi, quality, result$final_score))
# }


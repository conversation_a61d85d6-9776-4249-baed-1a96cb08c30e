% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/histPlot.R
\name{histPlot}
\alias{histPlot}
\title{Plotting historical co-citation network}
\usage{
histPlot(
  histResults,
  n = 20,
  size = 5,
  labelsize = 5,
  remove.isolates = TRUE,
  title_as_label = FALSE,
  label = "short",
  verbose = TRUE
)
}
\arguments{
\item{histResults}{is an object of \code{class} "list" containing the following components:

\tabular{lll}{
NetMatrix \tab  \tab the historical citation network matrix\cr
Degree \tab       \tab the min degree of the network\cr
histData \tab      \tab the set of n most cited references\cr
M \tab      \tab the bibliographic data frame}

is a network matrix obtained by the function \code{\link{histNetwork}}.}

\item{n}{is integer. It defines the number of vertices to plot.}

\item{size}{is an integer. It defines the point size of the vertices. Default value is 5.}

\item{labelsize}{is an integer. It indicates the label size in the plot. Default is \code{labelsize=5}.}

\item{remove.isolates}{is logical. If TRUE isolates vertices are not plotted.}

\item{title_as_label}{is a logical. DEPRECATED}

\item{label}{is a character. It indicates which label type to use as node id in the historiograph. It can be \code{label=c("short", "title", "keywords", "keywordsplus")}. 
Default is \code{label = "short"}.}

\item{verbose}{is logical. If TRUE, results and plots are printed on screen.}
}
\value{
It is list containing: a network object of the class \code{igraph} and a plot object of the class \code{ggraph}.
}
\description{
\code{histPlot} plots a historical co-citation network.
}
\details{
The function \code{\link{histPlot}} can plot a historical co-citation network previously created by \code{\link{histNetwork}}.
}
\examples{
# EXAMPLE Citation network
\dontrun{
data(management, package = "bibliometrixData")

histResults <- histNetwork(management, sep = ";")

net <- histPlot(histResults, n=20, labelsize = 5)
}

}
\seealso{
\code{\link{histNetwork}} to compute a historical co-citation network.

\code{\link{cocMatrix}} to compute a co-occurrence matrix.

\code{\link{biblioAnalysis}} to perform a bibliometric analysis.
}

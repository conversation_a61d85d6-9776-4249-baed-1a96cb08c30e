% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/conceptualStructure.R
\name{conceptualStructure}
\alias{conceptualStructure}
\title{Creating and plotting conceptual structure map of a scientific field}
\usage{
conceptualStructure(
  M,
  field = "ID",
  ngrams = 1,
  method = "MCA",
  quali.supp = NULL,
  quanti.supp = NULL,
  minDegree = 2,
  clust = "auto",
  k.max = 5,
  stemming = FALSE,
  labelsize = 10,
  documents = 2,
  graph = TRUE,
  remove.terms = NULL,
  synonyms = NULL
)
}
\arguments{
\item{M}{is a data frame obtained by the converting function
\code{\link{convert2df}}. It is a data matrix with cases corresponding to
articles and variables to Field Tag in the original ISI or SCOPUS file.}

\item{field}{is a character object. It indicates one of the field tags of the
standard ISI WoS Field Tag codify. 
field can be equal to one of these tags:
\tabular{lll}{ 
\code{ID}\tab   \tab Keywords Plus associated by ISI or SCOPUS database\cr 
\code{DE}\tab   \tab Author's keywords\cr 
\code{ID_TM}\tab   \tab Keywords Plus stemmed through the Porter's stemming algorithm\cr
\code{DE_TM}\tab   \tab Author's Keywords stemmed through the Porter's stemming algorithm\cr
\code{TI}\tab   \tab Terms extracted from titles\cr
\code{AB}\tab   \tab Terms extracted from abstracts}}

\item{ngrams}{is an integer between 1 and 3. It indicates the type of n-gram to extract from texts. 
An n-gram is a contiguous sequence of n terms. The function can extract n-grams composed by 1, 2, 3 or 4 terms. Default value is \code{ngrams=1}.}

\item{method}{is a character object. It indicates the factorial method used to create the factorial map. Use \code{method="CA"} for Correspondence Analysis,
\code{method="MCA"} for Multiple Correspondence Analysis or \code{method="MDS"} for Metric Multidimensional Scaling. The default is \code{method="MCA"}}

\item{quali.supp}{is a vector indicating the indexes of the categorical supplementary variables. It is used only for CA and MCA.}

\item{quanti.supp}{is a vector indicating the indexes of the quantitative supplementary variables. It is used only for CA and MCA.}

\item{minDegree}{is an integer. It indicates the minimum occurrences of terms to analyze and plot. The default value is 2.}

\item{clust}{is an integer or a character. If clust="auto", the number of cluster is chosen automatically, otherwise clust can be an integer between 2 and 8.}

\item{k.max}{is an integer. It indicates the maximum number of cluster to keep. The default value is 5. The max value is 20.}

\item{stemming}{is logical. If TRUE the Porter's Stemming algorithm is applied to all extracted terms. The default is \code{stemming = FALSE}.}

\item{labelsize}{is an integer. It indicates the label size in the plot. Default is \code{labelsize=10}}

\item{documents}{is an integer. It indicates the number of documents per cluster to plot in the factorial map. The default value is 2. It is used only for CA and MCA.}

\item{graph}{is logical. If TRUE the function plots the maps otherwise they are saved in the output object. Default value is TRUE}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}
}
\value{
It is an object of the class \code{list} containing the following components:

\tabular{lll}{
net \tab  \tab bipartite network\cr
res \tab       \tab Results of CA, MCA or MDS method\cr
km.res \tab      \tab Results of cluster analysis\cr
graph_terms \tab      \tab Conceptual structure map (class "ggplot2")\cr
graph_documents_Contrib \tab      \tab Factorial map of the documents with the highest contributes (class "ggplot2")\cr
graph_docuemnts_TC \tab      \tab Factorial map of the most cited documents (class "ggplot2")}
}
\description{
The function \code{conceptualStructure} creates a conceptual structure map of 
a scientific field performing Correspondence Analysis (CA), Multiple Correspondence Analysis (MCA) or Metric Multidimensional Scaling (MDS) and Clustering 
of a bipartite network of terms extracted from keyword, title or abstract fields.
}
\examples{
# EXAMPLE Conceptual Structure using Keywords Plus

data(scientometrics, package = "bibliometrixData")

CS <- conceptualStructure(scientometrics, field="ID", method="CA", 
             stemming=FALSE, minDegree=3, k.max = 5)

}
\seealso{
\code{\link{termExtraction}} to extract terms from a textual field (abstract, title, 
author's keywords, etc.) of a bibliographic data frame.

\code{\link{biblioNetwork}} to compute a bibliographic network.

\code{\link{cocMatrix}} to compute a co-occurrence matrix.

\code{\link{biblioAnalysis}} to perform a bibliometric analysis.
}

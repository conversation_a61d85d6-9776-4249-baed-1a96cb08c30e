% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/idByAuthor.R
\name{idByAuthor}
\alias{idByAuthor}
\title{Get Complete Author Information and ID from Scopus}
\usage{
idByAuthor(df, api_key)
}
\arguments{
\item{df}{is a dataframe composed of three columns:
\tabular{lll}{
\code{lastname}\tab   \tab author's last name\cr
\code{firstname}\tab   \tab author's first name\cr
\code{affiliation}\tab   \tab Part of the affiliation name (university name, city, etc.)}
i.e. df[1,1:3]<-c("aria","massimo","naples")
When affiliation is not specified, the field df$affiliation have to be NA.
i.e. df[2,1:3]<-c("cuccurullo","corrado", NA)}

\item{api_key}{is a character. It contains the Elsevier API key. Information about how to obtain an API Key \href{https://dev.elsevier.com/sc_apis.html}{Elsevier API website}}
}
\value{
a data frame with cases corresponding to authors and variables to author's information and ID got from SCOPUS.
}
\description{
Uses SCOPUS API author search to identify author identification information.
}
\examples{
## Request a personal API Key to Elsevier web page https://dev.elsevier.com/sc_apis.html
#
# api_key="your api key"

## create a data frame with the list of authors to get information and IDs
# i.e. df[1,1:3]<-c("aria","massimo","naples")
#      df[2,1:3]<-c("cuccurullo","corrado", NA)

## run idByAuthor function
#
# authorsID <- idByAuthor(df, api_key)

}
\seealso{
\code{\link{retrievalByAuthorID}} for downloading the complete author bibliographic collection from SCOPUS
}

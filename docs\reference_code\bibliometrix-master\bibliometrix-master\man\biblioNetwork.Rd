% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/biblioNetwork.R
\name{biblioNetwork}
\alias{biblioNetwork}
\title{Creating Bibliographic networks}
\usage{
biblioNetwork(
  M,
  analysis = "coupling",
  network = "authors",
  n = NULL,
  sep = ";",
  short = FALSE,
  shortlabel = TRUE,
  remove.terms = NULL,
  synonyms = NULL
)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function
\code{\link{convert2df}}. It is a data matrix with cases corresponding to
manuscripts and variables to Field Tag in the original SCOPUS and Clarivate Analytics WoS file.}

\item{analysis}{is a character object. It indicates the type of analysis can be performed.
\code{analysis} argument can be \code{"collaboration"}, \code{"coupling"}, \code{"co-occurrences"}  or \code{"co-citation"}.
Default is \code{analysis = "coupling"}.}

\item{network}{is a character object. It indicates the network typology. The \code{network} argument can be
\code{"authors"}, \code{"references"}, \code{"sources"}, \code{"countries"},\code{"keywords"}, \code{"author_keywords"}, \code{"titles"}, or \code{"abstracts"}.
Default is \code{network = "authors"}.}

\item{n}{is an integer. It indicates the number of items to select. If \code{N = NULL}, all items are selected.}

\item{sep}{is the field separator character. This character separates strings in each column of the data frame. The default is \code{sep = ";"}.}

\item{short}{is a logical. If TRUE all items with frequency<2 are deleted to reduce the matrix size.}

\item{shortlabel}{is logical. IF TRUE, reference labels are stored in a short format. Default is \code{shortlabel=TRUE}.}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}
}
\value{
It is a squared network matrix. It is an object of class \code{dgMatrix} of the package \code{Matrix}.
}
\description{
\code{biblioNetwork} creates different bibliographic networks from a bibliographic data frame.
}
\details{
The function \code{\link{biblioNetwork}} can create a collection of bibliographic networks 
following the approach proposed by Batagelj & Cerinsek (2013) and Aria & cuccurullo (2017).\cr\cr
Typical networks output of \code{biblioNetwork} are:\cr\cr
#### Collaboration Networks ############\cr
-- Authors collaboration (analysis = "collaboration", network = "authors")\cr
-- University collaboration (analysis = "collaboration", network = universities")\cr
-- Country collaboration (analysis = "collaboration", network = "countries")\cr\cr
#### Co-citation Networks ##############\cr
-- Authors co-citation (analysis = "co-citation", network = "authors")\cr
-- Reference co-citation (analysis = "co-citation", network = "references")\cr
-- Source co-citation (analysis = "co-citation", network = "sources")\cr\cr
#### Coupling Networks ################\cr
-- Manuscript coupling (analysis = "coupling", network = "references")\cr
-- Authors coupling (analysis = "coupling", network = "authors")\cr
-- Source coupling (analysis = "coupling", network = "sources")\cr
-- Country coupling (analysis = "coupling", network = "countries")\cr\cr
#### Co-occurrences Networks ################\cr
-- Authors co-occurrences (analysis = "co-occurrences", network = "authors")\cr
-- Source co-occurrences (analysis = "co-occurrences", network = "sources")\cr
-- Keyword co-occurrences (analysis = "co-occurrences", network = "keywords")\cr
-- Author-Keyword co-occurrences (analysis = "co-occurrences", network = "author_keywords")\cr
-- Title content co-occurrences (analysis = "co-occurrences", network = "titles")\cr
-- Abstract content co-occurrences (analysis = "co-occurrences", network = "abstracts")\cr\cr

References:\cr
Batagelj, V., & Cerinsek, M. (2013). On bibliographic networks. Scientometrics, 96(3), 845-864.\cr
Aria, M., & Cuccurullo, C. (2017). bibliometrix: An R-tool for comprehensive science mapping analysis. Journal of Informetrics, 11(4), 959-975.\cr
}
\examples{
# EXAMPLE 1: Authors collaboration network

# data(scientometrics, package = "bibliometrixData")

# NetMatrix <- biblioNetwork(scientometrics, analysis = "collaboration", 
# network = "authors", sep = ";")

# net <- networkPlot(NetMatrix, n = 30, type = "kamada", Title = "Collaboration",labelsize=0.5) 


# EXAMPLE 2: Co-citation network

data(scientometrics, package = "bibliometrixData")

NetMatrix <- biblioNetwork(scientometrics, analysis = "co-citation", 
network = "references", sep = ";")

net <- networkPlot(NetMatrix, n = 30, type = "kamada", Title = "Co-Citation",labelsize=0.5) 

}
\seealso{
\code{\link{convert2df}} to import and convert a SCOPUS and Thomson 
  Reuters' ISI Web of Knowledge export file in a data frame.

\code{\link{cocMatrix}} to compute a co-occurrence matrix.

\code{\link{biblioAnalysis}} to perform a bibliometric analysis.
}

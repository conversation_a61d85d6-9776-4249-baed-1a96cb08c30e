# 最终学术标准DOI补全系统
# 基于诊断分析的优化版本，平衡准确性与实用性
# 作者: Augment Agent (Final Academic Version)
# 日期: 2025-06-19

cat("=== 最终学术标准DOI补全系统 ===\n")
cat("基于诊断分析优化，确保学术可信度\n\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 优化的文本标准化函数
final_normalize_text <- function(text) {
  if (is.na(text) || text == "") return("")
  
  text <- tolower(text)
  text <- gsub("[[:punct:]]", " ", text)
  text <- gsub("\\s+", " ", text)
  text <- trimws(text)
  
  # 移除停用词
  stop_words <- c("the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by")
  words <- strsplit(text, "\\s+")[[1]]
  words <- words[!words %in% stop_words & nchar(words) > 2]
  
  return(paste(words, collapse = " "))
}

# 优化的相似度计算
final_similarity <- function(text1, text2) {
  if (is.na(text1) || is.na(text2) || text1 == "" || text2 == "") return(0)
  
  norm1 <- final_normalize_text(text1)
  norm2 <- final_normalize_text(text2)
  
  if (norm1 == "" || norm2 == "") return(0)
  
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  return(similarity)
}

# 期刊匹配函数（考虑期刊名称变更）
final_match_journals <- function(journal1, journal2) {
  if (is.na(journal1) || is.na(journal2) || journal1 == "" || journal2 == "") return(0.5)
  
  # 期刊名称标准化
  normalize_journal <- function(journal) {
    journal <- tolower(journal)
    journal <- gsub("^the\\s+", "", journal)
    journal <- gsub("\\s*\\(.*\\)$", "", journal)
    journal <- gsub("[[:punct:]]", " ", journal)
    journal <- gsub("\\s+", " ", journal)
    return(trimws(journal))
  }
  
  norm1 <- normalize_journal(journal1)
  norm2 <- normalize_journal(journal2)
  
  # 完全匹配
  if (norm1 == norm2) return(1.0)
  
  # 相似度匹配
  similarity <- 1 - stringdist(norm1, norm2, method = "jw")
  
  # 检查关键词匹配
  words1 <- strsplit(norm1, "\\s+")[[1]]
  words2 <- strsplit(norm2, "\\s+")[[1]]
  
  words1 <- words1[nchar(words1) > 3]
  words2 <- words2[nchar(words2) > 3]
  
  if (length(words1) > 0 && length(words2) > 0) {
    common_words <- sum(words1 %in% words2)
    keyword_similarity <- common_words / max(length(words1), length(words2))
    similarity <- max(similarity, keyword_similarity)
  }
  
  return(similarity)
}

# 年份匹配函数
final_match_year <- function(year1, year2) {
  if (is.na(year1) || is.na(year2)) return(0.5)
  
  year1 <- as.numeric(year1)
  year2 <- as.numeric(year2)
  
  if (year1 == year2) return(1.0)
  if (abs(year1 - year2) <= 1) return(0.8)
  return(0.0)
}

# 最终DOI查询函数
final_doi_search <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询策略
    clean_title <- final_normalize_text(title)
    title_words <- strsplit(clean_title, "\\s+")[[1]]
    
    # 使用重要关键词
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    query_string <- paste(keywords, collapse = " ")
    
    # API查询
    url <- sprintf("https://api.crossref.org/works?query=%s&filter=from-pub-date:%s,until-pub-date:%s&rows=15", 
                   URLencode(query_string), as.numeric(year)-2, as.numeric(year)+2)
    
    response <- GET(url, user_agent("FinalAcademicDOI/1.0"), timeout(30))
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$message$items) || length(content$message$items) == 0) return(NULL)
    
    items <- content$message$items
    best_match <- NULL
    best_score <- 0
    
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      # 提取候选文献信息
      candidate_title <- if (!is.null(item$title) && length(item$title) > 0) item$title[[1]] else ""
      candidate_journal <- ""
      candidate_year <- ""
      
      # 提取期刊信息
      if (!is.null(item$`container-title`) && length(item$`container-title`) > 0) {
        candidate_journal <- item$`container-title`[[1]]
      }
      
      # 提取年份信息
      if (!is.null(item$`published-print`$`date-parts`)) {
        candidate_year <- item$`published-print`$`date-parts`[[1]][[1]]
      } else if (!is.null(item$`published-online`$`date-parts`)) {
        candidate_year <- item$`published-online`$`date-parts`[[1]][[1]]
      }
      
      # 多重验证
      title_sim <- final_similarity(title, candidate_title)
      journal_sim <- final_match_journals(journal, candidate_journal)
      year_sim <- final_match_year(year, candidate_year)
      
      # 最终评分系统
      final_score <- (title_sim * 0.6) + (journal_sim * 0.3) + (year_sim * 0.1)
      
      # 基于诊断分析的接受标准
      if (title_sim >= 0.65 &&             # 标题相似度至少65%
          final_score >= 0.55 &&           # 综合评分至少55%
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = item$DOI,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          final_score = final_score,
          crossref_score = item$score
        )
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    return(NULL)
  })
}

# 最终测试处理函数
final_test_process <- function() {
  cat("开始最终学术标准DOI补全测试...\n")
  
  input_file <- "data_repository/04_enhancement_reports/missing_doi_records.csv"
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  
  # 测试前20条记录
  test_count <- min(20, nrow(data))
  cat(sprintf("测试前%d条记录\n", test_count))
  
  results <- data.frame(
    序号 = 1:test_count,
    UT = data$UT[1:test_count],
    原始标题 = data$TI[1:test_count],
    原始年份 = data$PY[1:test_count],
    原始期刊 = data$SO[1:test_count],
    补全DOI = NA,
    匹配标题 = NA,
    匹配期刊 = NA,
    匹配年份 = NA,
    标题相似度 = NA,
    期刊匹配度 = NA,
    年份匹配度 = NA,
    最终评分 = NA,
    学术可信度 = NA,
    补全状态 = "待处理",
    stringsAsFactors = FALSE
  )
  
  success_count <- 0
  
  for (i in 1:test_count) {
    cat(sprintf("处理第%d/%d条记录: %s\n", i, test_count, substr(data$TI[i], 1, 50)))
    
    match_result <- final_doi_search(
      title = data$TI[i],
      authors = data$AU[i], 
      year = data$PY[i],
      journal = data$SO[i]
    )
    
    if (!is.null(match_result)) {
      results$补全DOI[i] <- match_result$doi
      results$匹配标题[i] <- match_result$title
      results$匹配期刊[i] <- match_result$journal
      results$匹配年份[i] <- match_result$year
      results$标题相似度[i] <- round(match_result$title_similarity, 3)
      results$期刊匹配度[i] <- round(match_result$journal_similarity, 3)
      results$年份匹配度[i] <- round(match_result$year_similarity, 3)
      results$最终评分[i] <- round(match_result$final_score, 3)
      results$补全状态[i] <- "成功"
      
      # 确定学术可信度
      if (match_result$title_similarity >= 0.9 && match_result$final_score >= 0.8) {
        results$学术可信度[i] <- "极高可信度"
      } else if (match_result$title_similarity >= 0.8 && match_result$final_score >= 0.7) {
        results$学术可信度[i] <- "高可信度"
      } else if (match_result$title_similarity >= 0.7 && match_result$final_score >= 0.6) {
        results$学术可信度[i] <- "中等可信度"
      } else {
        results$学术可信度[i] <- "需要人工验证"
      }
      
      success_count <- success_count + 1
      cat(sprintf("  ✓ 成功匹配 (最终评分: %.3f, 可信度: %s)\n", 
                  match_result$final_score, results$学术可信度[i]))
    } else {
      results$补全状态[i] <- "未找到匹配"
      results$学术可信度[i] <- "未补全"
      cat("  ✗ 未找到匹配\n")
    }
    
    Sys.sleep(1.5)
  }
  
  # 统计结果
  success_rate <- 100 * success_count / test_count
  
  cat(sprintf("\n=== 最终学术标准测试结果 ===\n"))
  cat(sprintf("测试记录数: %d\n", test_count))
  cat(sprintf("成功补全: %d (%.1f%%)\n", success_count, success_rate))
  cat(sprintf("极高可信度: %d\n", sum(results$学术可信度 == "极高可信度", na.rm = TRUE)))
  cat(sprintf("高可信度: %d\n", sum(results$学术可信度 == "高可信度", na.rm = TRUE)))
  cat(sprintf("中等可信度: %d\n", sum(results$学术可信度 == "中等可信度", na.rm = TRUE)))
  cat(sprintf("需要人工验证: %d\n", sum(results$学术可信度 == "需要人工验证", na.rm = TRUE)))
  
  # 保存测试结果
  output_file <- "data_repository/04_enhancement_reports/final_academic_doi_test.csv"
  write.csv(results, output_file, row.names = FALSE)
  cat(sprintf("\n测试结果已保存: %s\n", output_file))
  
  # 显示成功的匹配示例
  successful_results <- results[results$补全状态 == "成功", ]
  if (nrow(successful_results) > 0) {
    cat(sprintf("\n=== 成功匹配示例 ===\n"))
    for (i in 1:min(nrow(successful_results), 5)) {
      record <- successful_results[i, ]
      cat(sprintf("\n示例 %d (%s):\n", i, record$学术可信度))
      cat(sprintf("原始标题: %s\n", substr(record$原始标题, 1, 80)))
      cat(sprintf("匹配标题: %s\n", substr(record$匹配标题, 1, 80)))
      cat(sprintf("DOI: %s\n", record$补全DOI))
      cat(sprintf("评分: 标题%.3f, 期刊%.3f, 年份%.3f, 最终%.3f\n", 
                  record$标题相似度, record$期刊匹配度, record$年份匹配度, record$最终评分))
    }
  }
  
  return(results)
}

# 执行最终测试
results <- final_test_process()

---
description: 
globs: 
alwaysApply: false
---
 语言设定：所有输出均需要以简体中文呈现
- 偏好编程语言：R语言
- 输出风格：简洁、易懂且易维护的代码
- 代码结构建议：
  - 使用清晰、简洁的变量和函数命名
  - 使用注释来解释复杂的逻辑和算法
  - 避免重复代码，使用函数封装复用的逻辑
  - 数据处理：鼓励简化数据操作
- 附加规则：
  - 在代码示例中优先使用较少的行数和简单的语法结构，在某处代码修改后需要全局查看代码及内容，检查是否有其他类似的问题和相关的问题。在使用者提供代码后，需要认真阅读代码，首先明确代码的意义及工作流程，鉴于使用者可能是一位初学者，所有需要帮助对项目整体进行协助如结构、是否合适等。
中对本部分代码增强或细化等方面，对于提供的代码与本阶段代码有冲突或不一致的部分，需要列出请使用者决定是否采纳。
请专注于目前工作，同时保证项目中的代码是经过我确认的，在生成新的脚本时多考虑能不能在旧的脚本上修改，而不是直接另起炉灶，在代码编写过程中可以使用fix、new、run等标识，在确定了代码可以良好执行后需将更改应用到原代码中，删除fix、new、run等标识的文件。

要体现一个有全局观的专家的素养！
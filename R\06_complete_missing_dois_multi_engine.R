# 多引擎DOI补全系统 - Crossref + OpenAlex
# 基于双引擎的大规模DOI补全与评估系统

cat("=== 多引擎DOI补全系统 (Crossref + OpenAlex) ===\n")
cat("开始处理所有缺失DOI的文献记录...\n\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 加载多引擎核心系统
source("DOI_COMPLETION_FINAL/01_CORE_SYSTEM/doi_completion_multi_engine.R")

# 多引擎全量DOI补全处理函数
process_all_missing_dois_multi_engine <- function(prefer_crossref = TRUE, use_both_engines = TRUE) {
  cat("开始多引擎全量DOI补全处理...\n")
  cat(sprintf("配置: 优先引擎=%s, 使用双引擎=%s\n\n", 
              ifelse(prefer_crossref, "Crossref", "OpenAlex"), 
              ifelse(use_both_engines, "是", "否")))
  
  input_file <- "data_repository/04_enhancement_reports/missing_doi_records.csv"
  
  if (!file.exists(input_file)) {
    cat("错误: 找不到输入文件", input_file, "\n")
    return(NULL)
  }
  
  data <- read.csv(input_file, stringsAsFactors = FALSE)
  
  total_count <- nrow(data)
  cat(sprintf("总记录数: %d\n", total_count))
  cat("预计处理时间: 约 %.1f 小时\n\n", total_count * 2.0 / 3600)  # 双引擎稍慢
  
  # 初始化结果数据框
  results <- data.frame(
    序号 = 1:total_count,
    UT = data$UT,
    原始标题 = data$TI,
    原始作者 = data$AU,
    原始年份 = data$PY,
    原始期刊 = data$SO,
    补全DOI = NA,
    匹配标题 = NA,
    匹配期刊 = NA,
    匹配年份 = NA,
    标题相似度 = NA,
    期刊匹配度 = NA,
    年份匹配度 = NA,
    学科相关性 = NA,
    最终评分 = NA,
    质量等级 = NA,
    数据源 = NA,  # 新增: 记录数据来源
    补全状态 = "待处理",
    stringsAsFactors = FALSE
  )
  
  # 统计变量
  success_count <- 0
  crossref_success <- 0
  openalex_success <- 0
  excellent_count <- 0
  good_count <- 0
  acceptable_count <- 0
  
  # 分批处理
  batch_size <- 50  # 减小批次大小，因为双引擎可能更慢
  total_batches <- ceiling(total_count / batch_size)
  
  for (batch in 1:total_batches) {
    start_idx <- (batch - 1) * batch_size + 1
    end_idx <- min(batch * batch_size, total_count)
    
    cat(sprintf("\n=== 批次 %d/%d (记录 %d-%d) ===\n", batch, total_batches, start_idx, end_idx))
    
    batch_start_time <- Sys.time()
    
    for (i in start_idx:end_idx) {
      if ((i - start_idx + 1) %% 10 == 0 || i == start_idx) {
        elapsed <- as.numeric(Sys.time() - batch_start_time, units = "mins")
        remaining_in_batch <- end_idx - i
        estimated_batch_time <- ifelse(i == start_idx, 0, elapsed / (i - start_idx + 1) * (end_idx - start_idx + 1))
        
        cat(sprintf("批次进度: %d/%d (总进度: %.1f%%) | 已用时: %.1f分钟 | 预计批次总时间: %.1f分钟\n", 
                    i - start_idx + 1, end_idx - start_idx + 1, 100 * i / total_count, 
                    elapsed, estimated_batch_time))
      }
      
      # 使用多引擎搜索
      if (use_both_engines) {
        match_result <- search_doi_multi_engine(
          title = data$TI[i],
          authors = data$AU[i], 
          year = data$PY[i],
          journal = data$SO[i],
          prefer_crossref = prefer_crossref
        )
      } else {
        # 仅使用首选引擎
        if (prefer_crossref) {
          match_result <- search_doi_crossref(
            title = data$TI[i],
            authors = data$AU[i], 
            year = data$PY[i],
            journal = data$SO[i]
          )
          if (!is.null(match_result)) match_result$source <- "crossref"
        } else {
          match_result <- search_doi_openalex(
            title = data$TI[i],
            authors = data$AU[i], 
            year = data$PY[i],
            journal = data$SO[i]
          )
          if (!is.null(match_result)) match_result$source <- "openalex"
        }
      }
      
      if (!is.null(match_result)) {
        results$补全DOI[i] <- match_result$doi
        results$匹配标题[i] <- match_result$title
        results$匹配期刊[i] <- match_result$journal
        results$匹配年份[i] <- match_result$year
        results$标题相似度[i] <- round(match_result$title_similarity, 3)
        results$期刊匹配度[i] <- round(match_result$journal_similarity, 3)
        results$年份匹配度[i] <- round(match_result$year_similarity, 3)
        results$学科相关性[i] <- round(match_result$subject_relevance, 3)
        results$最终评分[i] <- round(match_result$final_score, 3)
        results$数据源[i] <- match_result$source
        results$补全状态[i] <- "成功"
        
        # 统计数据源
        if (match_result$source == "crossref") {
          crossref_success <- crossref_success + 1
        } else if (match_result$source == "openalex") {
          openalex_success <- openalex_success + 1
        }
        
        # 质量等级评定
        if (match_result$title_similarity >= 0.95 && match_result$final_score >= 0.85) {
          results$质量等级[i] <- "卓越"
          excellent_count <- excellent_count + 1
        } else if (match_result$title_similarity >= 0.85 && match_result$final_score >= 0.75) {
          results$质量等级[i] <- "优秀"
          good_count <- good_count + 1
        } else if (match_result$title_similarity >= 0.75 && match_result$final_score >= 0.65) {
          results$质量等级[i] <- "良好"
          acceptable_count <- acceptable_count + 1
        } else {
          results$质量等级[i] <- "可接受"
        }
        
        success_count <- success_count + 1
      } else {
        results$补全状态[i] <- "未找到匹配"
        results$质量等级[i] <- "未补全"
        results$数据源[i] <- "无"
      }
      
      Sys.sleep(1.2)  # 稍微增加API调用间隔
    }
    
    # 每批次后保存中间结果和统计
    temp_file <- file.path("data_repository/04_enhancement_reports", sprintf("multi_engine_batch_%d.csv", batch))
    write.csv(results[1:end_idx, ], temp_file, row.names = FALSE)
    
    batch_time <- as.numeric(Sys.time() - batch_start_time, units = "mins")
    current_success_rate <- 100 * success_count / end_idx
    crossref_rate <- 100 * crossref_success / success_count
    openalex_rate <- 100 * openalex_success / success_count
    
    cat(sprintf("批次 %d 完成 | 用时: %.1f分钟 | 当前成功率: %.2f%% (%d/%d)\n", 
                batch, batch_time, current_success_rate, success_count, end_idx))
    cat(sprintf("数据源分布: Crossref=%.1f%% (%d), OpenAlex=%.1f%% (%d)\n", 
                crossref_rate, crossref_success, openalex_rate, openalex_success))
    cat(sprintf("当前质量分布: 卓越=%d, 优秀=%d, 良好=%d\n", excellent_count, good_count, acceptable_count))
    
    # 批次间休息
    if (batch < total_batches) {
      cat("批次间休息 30 秒...\n")
      Sys.sleep(30)
    }
  }
  
  return(list(
    results = results,
    stats = list(
      total_count = total_count,
      success_count = success_count,
      crossref_success = crossref_success,
      openalex_success = openalex_success,
      excellent_count = excellent_count,
      good_count = good_count,
      acceptable_count = acceptable_count
    )
  ))
}

# 多引擎结果分析评估函数
auto_analyze_multi_engine_results <- function(results, stats) {
  cat("\n=== 多引擎结果分析评估 ===\n")
  
  total_count <- stats$total_count
  success_count <- stats$success_count
  success_rate <- 100 * success_count / total_count
  
  crossref_success <- stats$crossref_success
  openalex_success <- stats$openalex_success
  excellent_count <- stats$excellent_count
  good_count <- stats$good_count
  acceptable_count <- stats$acceptable_count
  
  # 基本统计
  cat(sprintf("总记录数: %d\n", total_count))
  cat(sprintf("成功补全: %d (%.2f%%)\n", success_count, success_rate))
  cat(sprintf("  - Crossref: %d (%.1f%%)\n", crossref_success, 100 * crossref_success / success_count))
  cat(sprintf("  - OpenAlex: %d (%.1f%%)\n", openalex_success, 100 * openalex_success / success_count))
  cat(sprintf("卓越质量: %d (%.2f%%)\n", excellent_count, 100 * excellent_count / total_count))
  cat(sprintf("优秀质量: %d (%.2f%%)\n", good_count, 100 * good_count / total_count))
  cat(sprintf("良好质量: %d (%.2f%%)\n", acceptable_count, 100 * acceptable_count / total_count))
  
  # 质量评估
  high_quality_count <- excellent_count + good_count
  high_quality_rate <- 100 * high_quality_count / success_count
  
  cat(sprintf("\n=== 质量评估 ===\n"))
  cat(sprintf("高质量匹配率: %.1f%% (%d/%d成功记录)\n", high_quality_rate, high_quality_count, success_count))
  
  # 多引擎优势分析
  cat(sprintf("\n=== 多引擎优势分析 ===\n"))
  if (crossref_success > 0 && openalex_success > 0) {
    cat(sprintf("✅ 多引擎互补: Crossref和OpenAlex都有贡献\n"))
    cat(sprintf("   Crossref独有优势: 权威性和数据质量\n"))
    cat(sprintf("   OpenAlex独有优势: 覆盖面和现代文献\n"))
  } else if (crossref_success > 0) {
    cat(sprintf("📊 主要依赖Crossref: 建议优化OpenAlex查询策略\n"))
  } else if (openalex_success > 0) {
    cat(sprintf("📊 主要依赖OpenAlex: 建议优化Crossref查询策略\n"))
  }
  
  # 自动评估结论
  if (success_rate >= 20 && high_quality_rate >= 75) {
    assessment <- "优秀"
    recommendation <- "多引擎系统表现优秀，可直接用于学术研究"
  } else if (success_rate >= 15 && high_quality_rate >= 70) {
    assessment <- "良好"
    recommendation <- "多引擎系统表现良好，建议对良好质量记录进行人工验证"
  } else if (success_rate >= 10) {
    assessment <- "可接受"
    recommendation <- "多引擎系统表现可接受，建议仔细验证所有匹配记录"
  } else {
    assessment <- "需要改进"
    recommendation <- "成功率偏低，建议调整算法参数或查询策略"
  }
  
  cat(sprintf("\n=== 自动评估结论 ===\n"))
  cat(sprintf("整体评估: %s\n", assessment))
  cat(sprintf("使用建议: %s\n", recommendation))
  
  return(list(
    assessment = assessment,
    recommendation = recommendation,
    success_rate = success_rate,
    high_quality_rate = high_quality_rate,
    crossref_contribution = 100 * crossref_success / success_count,
    openalex_contribution = 100 * openalex_success / success_count,
    stats = stats
  ))
}

# 主执行函数
main_multi_engine_execution <- function(prefer_crossref = TRUE, use_both_engines = TRUE) {
  cat("开始多引擎DOI补全与自动评估...\n\n")
  
  # 执行全量处理
  processing_result <- process_all_missing_dois_multi_engine(prefer_crossref, use_both_engines)
  
  if (is.null(processing_result)) {
    cat("❌ 处理失败\n")
    return(NULL)
  }
  
  results <- processing_result$results
  stats <- processing_result$stats
  
  # 保存最终结果
  final_file <- "data_repository/04_enhancement_reports/COMPLETE_DOI_RESULTS_MULTI_ENGINE.csv"
  write.csv(results, final_file, row.names = FALSE)
  cat(sprintf("\n✅ 完整结果已保存: %s\n", final_file))
  
  # 自动分析评估
  analysis <- auto_analyze_multi_engine_results(results, stats)
  
  # 保存分析报告
  analysis_file <- "data_repository/04_enhancement_reports/MULTI_ENGINE_ANALYSIS_REPORT.txt"
  
  report_content <- sprintf('多引擎DOI补全自动分析报告
生成时间: %s

=== 处理统计 ===
总记录数: %d
成功补全: %d (%.2f%%)
  - Crossref: %d (%.1f%%)
  - OpenAlex: %d (%.1f%%)
卓越质量: %d (%.2f%%)
优秀质量: %d (%.2f%%)
良好质量: %d (%.2f%%)

=== 质量评估 ===
整体评估: %s
高质量匹配率: %.1f%%
Crossref贡献度: %.1f%%
OpenAlex贡献度: %.1f%%
使用建议: %s

=== 文件清单 ===
- COMPLETE_DOI_RESULTS_MULTI_ENGINE.csv: 完整补全结果
- MULTI_ENGINE_ANALYSIS_REPORT.txt: 本分析报告
', 
    format(Sys.time(), "%Y-%m-%d %H:%M:%S"),
    stats$total_count, stats$success_count, analysis$success_rate,
    stats$crossref_success, analysis$crossref_contribution,
    stats$openalex_success, analysis$openalex_contribution,
    stats$excellent_count, 100 * stats$excellent_count / stats$total_count,
    stats$good_count, 100 * stats$good_count / stats$total_count,
    stats$acceptable_count, 100 * stats$acceptable_count / stats$total_count,
    analysis$assessment, analysis$high_quality_rate,
    analysis$crossref_contribution, analysis$openalex_contribution,
    analysis$recommendation
  )
  
  writeLines(report_content, analysis_file)
  cat(sprintf("✅ 分析报告已保存: %s\n", analysis_file))
  
  cat(sprintf("\n🎉 多引擎DOI补全与评估完成！\n"))
  cat(sprintf("整体评估: %s\n", analysis$assessment))
  cat(sprintf("成功率: %.2f%% | 高质量率: %.1f%%\n", analysis$success_rate, analysis$high_quality_rate))
  cat(sprintf("数据源分布: Crossref %.1f%% | OpenAlex %.1f%%\n", 
              analysis$crossref_contribution, analysis$openalex_contribution))
  
  return(list(results = results, analysis = analysis, stats = stats))
}

# 使用说明
cat("✅ 多引擎DOI补全系统已加载\n")
cat("📋 使用方法:\n")
cat("  # 优先Crossref，使用双引擎\n")
cat("  result <- main_multi_engine_execution(prefer_crossref = TRUE, use_both_engines = TRUE)\n\n")
cat("  # 优先OpenAlex，使用双引擎\n")
cat("  result <- main_multi_engine_execution(prefer_crossref = FALSE, use_both_engines = TRUE)\n\n")
cat("  # 仅使用Crossref\n")
cat("  result <- main_multi_engine_execution(prefer_crossref = TRUE, use_both_engines = FALSE)\n\n")

# 如果直接运行此脚本，执行默认配置
if (interactive() == FALSE) {
  cat("🚀 开始执行多引擎DOI补全 (默认配置: 优先Crossref + 双引擎)\n")
  final_output <- main_multi_engine_execution(prefer_crossref = TRUE, use_both_engines = TRUE)
}

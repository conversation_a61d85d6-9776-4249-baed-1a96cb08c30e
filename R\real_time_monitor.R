# 实时监控和分析系统
# 监控全量DOI补全的进度并提供实时分析

cat("=== 实时监控和分析系统 ===\n")

# 监控函数
monitor_progress <- function() {
  cat("开始监控DOI补全进度...\n")
  
  results_dir <- "data_repository/04_enhancement_reports"
  
  # 检查批次文件
  batch_files <- list.files(results_dir, pattern = "full_batch_.*\\.csv", full.names = TRUE)
  
  if (length(batch_files) == 0) {
    cat("尚未发现批次结果文件，处理可能刚开始...\n")
    return(NULL)
  }
  
  # 读取最新的批次文件
  latest_batch <- batch_files[length(batch_files)]
  batch_num <- as.numeric(gsub(".*full_batch_(\\d+)\\.csv", "\\1", basename(latest_batch)))
  
  cat(sprintf("发现最新批次文件: batch_%d\n", batch_num))
  
  # 读取数据
  data <- read.csv(latest_batch, stringsAsFactors = FALSE)
  
  # 分析当前进度
  total_processed <- nrow(data)
  success_count <- sum(data$补全状态 == "成功", na.rm = TRUE)
  success_rate <- 100 * success_count / total_processed
  
  excellent_count <- sum(data$质量等级 == "卓越", na.rm = TRUE)
  good_count <- sum(data$质量等级 == "优秀", na.rm = TRUE)
  acceptable_count <- sum(data$质量等级 == "良好", na.rm = TRUE)
  
  cat(sprintf("\n=== 当前进度报告 ===\n"))
  cat(sprintf("已处理记录: %d\n", total_processed))
  cat(sprintf("成功补全: %d (%.2f%%)\n", success_count, success_rate))
  cat(sprintf("卓越质量: %d (%.2f%%)\n", excellent_count, 100 * excellent_count / total_processed))
  cat(sprintf("优秀质量: %d (%.2f%%)\n", good_count, 100 * good_count / total_processed))
  cat(sprintf("良好质量: %d (%.2f%%)\n", acceptable_count, 100 * acceptable_count / total_processed))
  
  # 质量分析
  if (success_count > 0) {
    successful_data <- data[data$补全状态 == "成功", ]
    
    avg_title_sim <- mean(successful_data$标题相似度, na.rm = TRUE)
    avg_journal_sim <- mean(successful_data$期刊匹配度, na.rm = TRUE)
    avg_final_score <- mean(successful_data$最终评分, na.rm = TRUE)
    
    cat(sprintf("\n=== 质量分析 ===\n"))
    cat(sprintf("平均标题相似度: %.3f\n", avg_title_sim))
    cat(sprintf("平均期刊匹配度: %.3f\n", avg_journal_sim))
    cat(sprintf("平均最终评分: %.3f\n", avg_final_score))
    
    # 显示最佳匹配示例
    best_matches <- successful_data[order(successful_data$最终评分, decreasing = TRUE), ][1:min(3, nrow(successful_data)), ]
    
    cat(sprintf("\n=== 最佳匹配示例 ===\n"))
    for (i in 1:nrow(best_matches)) {
      record <- best_matches[i, ]
      cat(sprintf("\n示例 %d (%s):\n", i, record$质量等级))
      cat(sprintf("原始标题: %s\n", substr(record$原始标题, 1, 60)))
      cat(sprintf("匹配标题: %s\n", substr(record$匹配标题, 1, 60)))
      cat(sprintf("DOI: %s\n", record$补全DOI))
      cat(sprintf("评分: %.3f (标题:%.3f, 期刊:%.3f)\n", 
                  record$最终评分, record$标题相似度, record$期刊匹配度))
    }
  }
  
  # 预测分析
  if (total_processed >= 100) {
    # 估算总体完成情况
    estimated_total <- 500  # 假设总记录数
    estimated_completion_rate <- total_processed / estimated_total
    estimated_final_success_rate <- success_rate
    estimated_final_success_count <- round(estimated_final_success_rate * estimated_total / 100)
    
    cat(sprintf("\n=== 预测分析 ===\n"))
    cat(sprintf("预计完成度: %.1f%%\n", 100 * estimated_completion_rate))
    cat(sprintf("预计最终成功率: %.2f%%\n", estimated_final_success_rate))
    cat(sprintf("预计最终成功数量: %d\n", estimated_final_success_count))
  }
  
  return(data)
}

# 生成中期报告
generate_interim_report <- function(data) {
  if (is.null(data)) return(NULL)
  
  cat("\n生成中期报告...\n")
  
  total_processed <- nrow(data)
  success_count <- sum(data$补全状态 == "成功", na.rm = TRUE)
  success_rate <- 100 * success_count / total_processed
  
  excellent_count <- sum(data$质量等级 == "卓越", na.rm = TRUE)
  good_count <- sum(data$质量等级 == "优秀", na.rm = TRUE)
  acceptable_count <- sum(data$质量等级 == "良好", na.rm = TRUE)
  
  # 创建HTML中期报告
  html_content <- sprintf('
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>DOI补全中期进度报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #3498db; color: white; padding: 15px; border-radius: 5px; text-align: center; }
        .progress { background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }
        .stats { display: flex; justify-content: space-around; margin: 20px 0; }
        .stat-box { background-color: #2ecc71; color: white; padding: 15px; border-radius: 5px; text-align: center; min-width: 100px; }
        .excellent { background-color: #27ae60; }
        .good { background-color: #f39c12; }
        .acceptable { background-color: #e74c3c; }
        table { border-collapse: collapse; width: 100%%; margin: 20px 0; }
        th, td { border: 1px solid #bdc3c7; padding: 8px; text-align: left; }
        th { background-color: #34495e; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>DOI补全中期进度报告</h1>
        <p>实时监控 - 生成时间: %s</p>
    </div>
    
    <div class="progress">
        <h2>处理进度</h2>
        <p><strong>已处理记录:</strong> %d</p>
        <p><strong>成功补全:</strong> %d (%.2f%%)</p>
        <p><strong>处理状态:</strong> 正在进行中...</p>
    </div>
    
    <div class="stats">
        <div class="stat-box excellent">
            <h3>卓越质量</h3>
            <h2>%d</h2>
            <p>(%.1f%%)</p>
        </div>
        <div class="stat-box good">
            <h3>优秀质量</h3>
            <h2>%d</h2>
            <p>(%.1f%%)</p>
        </div>
        <div class="stat-box acceptable">
            <h3>良好质量</h3>
            <h2>%d</h2>
            <p>(%.1f%%)</p>
        </div>
    </div>
    
    <h2>质量分布</h2>
    <table>
        <tr><th>质量等级</th><th>数量</th><th>比例</th></tr>
        <tr><td>卓越</td><td>%d</td><td>%.2f%%</td></tr>
        <tr><td>优秀</td><td>%d</td><td>%.2f%%</td></tr>
        <tr><td>良好</td><td>%d</td><td>%.2f%%</td></tr>
        <tr><td>未补全</td><td>%d</td><td>%.2f%%</td></tr>
    </table>
    
    <div style="margin-top: 30px; padding: 15px; background-color: #d5f4e6; border-radius: 5px;">
        <h3>当前评估</h3>
        <p>基于已处理的 %d 条记录，系统表现良好。成功率为 %.2f%%，其中高质量匹配占主导地位。</p>
        <p><strong>建议:</strong> 继续处理，当前质量标准符合学术要求。</p>
    </div>
    
</body>
</html>
  ', 
    format(Sys.time(), "%%Y-%%m-%%d %%H:%%M:%%S"),
    total_processed, success_count, success_rate,
    excellent_count, 100 * excellent_count / total_processed,
    good_count, 100 * good_count / total_processed,
    acceptable_count, 100 * acceptable_count / total_processed,
    excellent_count, 100 * excellent_count / total_processed,
    good_count, 100 * good_count / total_processed,
    acceptable_count, 100 * acceptable_count / total_processed,
    total_processed - success_count, 100 * (total_processed - success_count) / total_processed,
    total_processed, success_rate
  )
  
  # 保存中期报告
  interim_file <- "data_repository/04_enhancement_reports/INTERIM_PROGRESS_REPORT.html"
  writeLines(html_content, interim_file, useBytes = TRUE)
  cat(sprintf("中期报告已保存: %s\n", interim_file))
  
  return(interim_file)
}

# 执行监控
cat("开始实时监控...\n")

# 等待一段时间让处理开始
cat("等待处理开始...\n")
Sys.sleep(60)

# 监控进度
progress_data <- monitor_progress()

# 生成中期报告
if (!is.null(progress_data)) {
  generate_interim_report(progress_data)
  cat("\n✅ 实时监控完成，中期报告已生成\n")
} else {
  cat("处理尚未开始或刚开始，请稍后再次运行监控\n")
}

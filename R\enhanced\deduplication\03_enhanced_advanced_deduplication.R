# --- 0. 配置管理 ---
# 首先加载必要的包
required_packages <- c("bibliometrix", "here", "dplyr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保here包正确初始化
if (!requireNamespace("here", quietly = TRUE)) {
  stop("无法加载here包，请手动安装：install.packages('here')")
}

config <- list(
  # 输入输出路径配置
  paths = list(
    input = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    output = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    logs = here("data_repository", "05_execution_logs", "bibliometrix_logs"),
    reports = here("data_repository", "06_data_reports", "biblioshiny_reports")
  ),
  # 文件命名配置
  files = list(
    input = "datay_bibliometrix_initial.rds",
    output = "datax_bibliometrix_advanced.rds",
    log = "deduplication.log"
  ),
  # 去重配置
  deduplication = list(
    # 标题去重阈值（从严格到宽松）
    title_tolerances = c(0.98, 0.95, 0.90),
    # 作者去重阈值
    author_tolerance = 0.95,
    # 摘要去重阈值
    abstract_tolerance = 0.90
  )
)

# --- 1. 设置环境与加载库 ---
# 注意：包已经在配置管理部分加载，这里不需要重复加载

# --- 2. 定义输入和输出路径 ---
input_file <- file.path(config$paths$input, config$files$input)
output_file <- file.path(config$paths$output, config$files$output)
log_file <- file.path(config$paths$logs, config$files$log)

# 创建必要的目录（使用suppressMessages避免重复输出）
suppressMessages({
  for (dir_path in c(config$paths$output, config$paths$logs, config$paths$reports)) {
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = TRUE)
      message("创建目录:", dir_path)
    }
  }
})

# --- 3. 加载导入的数据 ---
if (!file.exists(input_file)) {
  stop("导入的数据文件不存在: ", input_file, "\n请先运行 01_data_import.R 脚本。")
}
M <- readRDS(input_file)
cat("成功加载导入的数据。初始记录数:", nrow(M), "\n", file = stderr())

# --- 4. 初始化日志文件连接 ---
log_con <- file(log_file, "w")
sink(log_con, append = TRUE, type = "output")
sink(log_con, append = TRUE, type = "message")

# 记录开始时间
start_time <- Sys.time()
cat("去重开始时间:", format(start_time), "\n")
cat("输入文件:", basename(input_file), "\n")
cat("初始记录数:", nrow(M), "\n\n")

# --- 辅助函数：执行去重并记录 ---
perform_deduplication_step <- function(M_current, field, exact, tol = 0.95, step_name, log_connection) {
  step_intro_msg <- paste0("
--- 开始去重步骤: ", step_name, " (字段: ", field, ", Exact: ", exact, if(!exact) paste0(", Tol: ", tol) else "", ") ---\n")
  cat(step_intro_msg, file = log_connection)
  cat(step_intro_msg, file = stderr())

  log_step_header <- paste0("--- ", step_name, " (字段: ", field, ", Exact: ", exact, if(!exact) paste0(", Tol: ", tol) else "", ") ---\n")
  log_before_count <- paste0("步骤开始前记录数: ", nrow(M_current), "\n")
  cat(log_step_header, file = log_connection)
  cat(log_before_count, file = log_connection)
  cat(log_before_count, file = stderr())

  M_before_step <- M_current
  
  if (! (field %in% names(M_before_step)) ){
    warn_msg <- paste0("警告: 字段 '", field, "' 在数据中不存在，跳过此步骤。
")
    cat(warn_msg, file = log_connection)
    cat(warn_msg, file = stderr())
    return(M_current)
  }
  
  # 将目标字段转换为字符，并将NA视为空字符串，以便后续处理
  M_before_step[[field]] <- as.character(M_before_step[[field]])
  M_before_step[[field]][is.na(M_before_step[[field]])] <- ""

  M_to_process <- M_before_step
  M_skipped_due_to_empty_field <- data.frame() # 用于因目标字段为空而跳过模糊匹配的记录
  M_skipped_due_to_no_doi <- data.frame() # 用于因没有DOI而跳过DOI精确匹配的记录

  if (exact && field == "DI") {
    # 特殊处理DOI精确匹配：只对有DOI的记录进行去重
    has_doi <- M_before_step[[field]] != ""
    M_with_doi <- M_before_step[has_doi, , drop = FALSE]
    M_skipped_due_to_no_doi <- M_before_step[!has_doi, , drop = FALSE]
    
    cat(paste0("DOI精确匹配: ", nrow(M_with_doi), " 条记录有DOI, ", nrow(M_skipped_due_to_no_doi), " 条记录无DOI (将不参与此轮DOI去重)。
"), file = log_connection)
    cat(paste0("DOI精确匹配: ", nrow(M_with_doi), " 条记录有DOI, ", nrow(M_skipped_due_to_no_doi), " 条记录无DOI (将不参与此轮DOI去重)。
"), file = stderr())
    
    M_to_process <- M_with_doi # 实际参与DOI去重的是这部分
  } else if (!exact) {
    # 对于其他字段的模糊匹配，如果字段为空，则跳过该记录的模糊匹配
    empty_target_field_rows <- which(M_before_step[[field]] == "")
    if (length(empty_target_field_rows) > 0) {
        M_to_process <- M_before_step[-empty_target_field_rows, , drop = FALSE]
        M_skipped_due_to_empty_field <- M_before_step[empty_target_field_rows, , drop = FALSE]
        info_msg <- paste0("注意: 有 ", nrow(M_skipped_due_to_empty_field), " 条记录因目标字段 '", field, "' 为空，未参与此轮模糊匹配。
")
        cat(info_msg, file = log_connection)
        cat(info_msg, file = stderr())
    }
  } else {
    # 对于其他字段的精确匹配 (非DOI)，所有记录都参与
    M_to_process <- M_before_step
  }
  
M_processed_deduped <- M_to_process # 默认情况下，如果没有记录或只有一条记录，则不发生变化
  if (nrow(M_to_process) > 1) { 
      if (exact) {
          M_processed_deduped <- duplicatedMatching(M_to_process, Field = field, exact = TRUE)
      } else { # Fuzzy matching
          if (!"UT" %in% names(M_to_process)) stop("UT column is required for deduplication logging but not found.")
          M_processed_deduped <- duplicatedMatching(M_to_process, Field = field, exact = FALSE, tol = tol)
          
          if (nrow(M_to_process) > nrow(M_processed_deduped)) {
            Removed_From_M_to_process <- dplyr::anti_join(M_to_process, M_processed_deduped, by = "UT")
            ab_header_msg <- "--- 识别出的潜在重复项 (供人工审核):\n"
            cat(ab_header_msg, file = log_connection)
            cat(ab_header_msg, file = stderr())
            for (i in 1:nrow(Removed_From_M_to_process)) {
              R_removed <- Removed_From_M_to_process[i, ]
              removed_UT <- ifelse("UT" %in% names(R_removed), R_removed$UT, "N/A")
              removed_TI <- ifelse("TI" %in% names(R_removed), R_removed$TI, "N/A")
              removed_AB_snippet <- ifelse("AB" %in% names(R_removed), substr(R_removed$AB, 1, 100), "N/A")
              log_removed_B <- paste0("  * 被移除 (B): UT=", removed_UT, "\n    TI_B='", removed_TI, "'\n    AB_B_snip='", removed_AB_snippet, "...'\n")
              cat(log_removed_B, file = log_connection)
              cat(log_removed_B, file = stderr())
              found_A_partner <- FALSE
              for (j in 1:nrow(M_processed_deduped)) {
                R_kept <- M_processed_deduped[j, ]
                str_removed_field_val <- as.character(R_removed[[field]])
                str_kept_field_val <- as.character(R_kept[[field]])
                if (nchar(str_removed_field_val) == 0 || nchar(str_kept_field_val) == 0) next
                dist_val <- adist(str_removed_field_val, str_kept_field_val, ignore.case = TRUE)
                max_len <- max(nchar(str_removed_field_val), nchar(str_kept_field_val))
                similarity <- if (max_len == 0) 1 else (1 - (dist_val[1,1] / max_len))
                if (similarity >= tol) {
                  kept_UT <- ifelse("UT" %in% names(R_kept), R_kept$UT, "N/A")
                  kept_TI <- ifelse("TI" %in% names(R_kept), R_kept$TI, "N/A")
                  kept_AB_snippet <- ifelse("AB" %in% names(R_kept), substr(R_kept$AB, 1, 100), "N/A")
                  log_kept_A <- paste0("    - 匹配到的保留项 (A): UT=", kept_UT, " (近似Levenshtein相似度: ", round(similarity, 4), ")\n      TI_A=\'", kept_TI, "\'\n      AB_A_snip=\'", kept_AB_snippet, "...'\n")
                  cat(log_kept_A, file = log_connection)
                  cat(log_kept_A, file = stderr())
                  found_A_partner <- TRUE
                }
              }
              if (!found_A_partner) {
                no_partner_msg <- "    - 未在保留记录中通过 adist 找到相似度 >= tol 的明确匹配项 (A) (基于Levenshtein近似计算).\n"
                cat(no_partner_msg, file = log_connection)
                cat(no_partner_msg, file = stderr())
              }
              cat("\n", file = log_connection) 
              cat("\n", file = stderr())
            }
            ab_footer_msg <- "--- 潜在重复项列表结束 ---\n"
            cat(ab_footer_msg, file = log_connection)
            cat(ab_footer_msg, file = stderr())
          }
      }
  } else {
      info_msg_skip <- paste0("字段 '", field, "' (处理后)的有效记录不足2条 (仅 ", nrow(M_to_process), " 条)，跳过 duplicatedMatching。
")
      cat(info_msg_skip, file = log_connection)
      cat(info_msg_skip, file = stderr())
  }

  # 合并回之前因为特定原因跳过的记录
  M_after_step <- M_processed_deduped 
  # 首先合并因目标字段为空而跳过模糊匹配的记录
  if (nrow(M_skipped_due_to_empty_field) > 0) {
      if (nrow(M_after_step) == 0) { M_after_step <- M_skipped_due_to_empty_field }
      else { 
          common_cols <- intersect(names(M_after_step), names(M_skipped_due_to_empty_field))
          if (length(common_cols) > 0) {
              M_after_step_char <- M_after_step[, common_cols, drop = FALSE]
              M_skipped_char <- M_skipped_due_to_empty_field[, common_cols, drop = FALSE]
              for(col_name in common_cols){ M_after_step_char[[col_name]] <- as.character(M_after_step_char[[col_name]]); M_skipped_char[[col_name]] <- as.character(M_skipped_char[[col_name]]) }
              M_after_step <- rbind(M_after_step_char, M_skipped_char)
          } else { cat("警告: 合并M_skipped_due_to_empty_field时无共同列名\n", file=log_connection) }
      }
  }
  # 然后，如果是DOI精确匹配，合并回那些没有DOI的记录
  if (exact && field == "DI" && nrow(M_skipped_due_to_no_doi) > 0) {
      if (nrow(M_after_step) == 0) { M_after_step <- M_skipped_due_to_no_doi }
      else {
          common_cols <- intersect(names(M_after_step), names(M_skipped_due_to_no_doi))
          if (length(common_cols) > 0) {
              M_after_step_char <- M_after_step[, common_cols, drop = FALSE]
              M_skipped_char <- M_skipped_due_to_no_doi[, common_cols, drop = FALSE]
              for(col_name in common_cols){ M_after_step_char[[col_name]] <- as.character(M_after_step_char[[col_name]]); M_skipped_char[[col_name]] <- as.character(M_skipped_char[[col_name]]) }
              M_after_step <- rbind(M_after_step_char, M_skipped_char)
          } else { cat("警告: 合并M_skipped_due_to_no_doi时无共同列名\n", file=log_connection) }
      }
  }

  removed_count <- nrow(M_before_step) - nrow(M_after_step)
  summary_msg <- paste0("步骤完成后记录数: ", nrow(M_after_step), " (此步骤移除了 ", removed_count, " 条)
")
  log_summary_msg_file <- paste0("步骤完成后记录数: ", nrow(M_after_step), " (此步骤移除了 ", removed_count, " 条)\n\n")
  cat(summary_msg, file = stderr())
  cat(log_summary_msg_file, file = log_connection)
  
  return(M_after_step)
}

# --- 5. 执行去重步骤 ---
M_dedup <- M

# 5.1 DOI精确匹配
if ("DI" %in% names(M_dedup)) {
  M_dedup <- perform_deduplication_step(
    M_dedup, 
    field = "DI", 
    exact = TRUE, 
    step_name = "DOI精确匹配", 
    log_connection = log_con
  )
} else {
  warning("未找到DOI字段 ('DI')，跳过基于DOI的去重。")
}

# 5.2 标题多轮模糊匹配
if ("TI" %in% names(M_dedup)) {
  M_before_TI <- M_dedup
  for (tol in config$deduplication$title_tolerances) {
    M_dedup <- perform_deduplication_step(
      M_dedup,
      field = "TI",
      exact = FALSE,
      tol = tol,
      step_name = paste0("标题模糊匹配 (tol=", tol, ")"),
      log_connection = log_con
    )
  }
} else {
  warning("未找到标题字段 ('TI')，跳过基于标题的去重。")
}

# 5.3 作者模糊匹配
if ("AU" %in% names(M_dedup)) {
  M_dedup <- perform_deduplication_step(
    M_dedup,
    field = "AU",
    exact = FALSE,
    tol = config$deduplication$author_tolerance,
    step_name = "作者模糊匹配",
    log_connection = log_con
  )
} else {
  warning("未找到作者字段 ('AU')，跳过基于作者的去重。")
}

# 5.4 摘要模糊匹配
if ("AB" %in% names(M_dedup)) {
  M_dedup <- perform_deduplication_step(
    M_dedup,
    field = "AB",
    exact = FALSE,
    tol = config$deduplication$abstract_tolerance,
    step_name = "摘要模糊匹配",
    log_connection = log_con
  )
} else {
  warning("未找到摘要字段 ('AB')，跳过基于摘要的去重。")
}

# --- 6. 保存去重结果 ---
saveRDS(M_dedup, file = output_file)
cat("\n去重后的数据已保存至:", output_file, "\n")

# --- 7. 清理和日志记录 ---
end_time <- Sys.time()
duration <- difftime(end_time, start_time, units = "mins")
cat("\n去重完成时间:", format(end_time), "\n")
cat("总耗时:", round(as.numeric(duration), 2), "分钟\n")
cat("初始记录数:", nrow(M), "\n")
cat("最终记录数:", nrow(M_dedup), "\n")
cat("移除重复数:", nrow(M) - nrow(M_dedup), "\n")

# 关闭日志
sink(type = "message")
sink(type = "output")
close(log_con)

cat("\n脚本 '02_deduplication.R' 执行完毕。\n", file = stderr()) 
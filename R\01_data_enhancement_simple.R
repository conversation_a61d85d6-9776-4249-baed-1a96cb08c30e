# 01_data_enhancement_simple.R
# 简化版数据增强流程
# 专门处理enhanced_data_initial.rds文件

# 加载必要的包
library(here)
library(dplyr)
library(stringr)
library(bibliometrix)

# 日志函数
log_message <- function(msg) {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  message(sprintf("[%s] %s", timestamp, msg))
}

# 主函数
main <- function() {
  log_message("开始数据增强流程")
  
  # 1. 加载数据
  input_file <- here("data_repository", "02_enhanced_dataset", "enhanced_data_initial.rds")
  
  if (!file.exists(input_file)) {
    stop(sprintf("输入文件不存在: %s", input_file))
  }
  
  M <- readRDS(input_file)
  log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
  
  # 2. 字段标准化
  log_message("开始字段标准化")
  
  # 标准化作者信息
  if ("AU" %in% colnames(M)) {
    M$AU_standardized <- sapply(M$AU, function(x) {
      if (is.na(x) || x == "") return(NA_character_)
      authors <- str_split(x, ";")[[1]]
      authors <- trimws(authors)
      authors <- gsub("\\s+", " ", authors)  # 移除多余空格
      paste(authors, collapse = "; ")
    })
    log_message("作者信息标准化完成")
  }
  
  # 标准化机构信息
  if ("C1" %in% colnames(M)) {
    M$C1_standardized <- sapply(M$C1, function(x) {
      if (is.na(x) || x == "") return(NA_character_)
      institutions <- str_split(x, ";")[[1]]
      institutions <- trimws(institutions)
      institutions <- gsub("\\s+", " ", institutions)  # 移除多余空格
      paste(institutions, collapse = "; ")
    })
    log_message("机构信息标准化完成")
  }
  
  # 标准化关键词
  if ("DE" %in% colnames(M)) {
    M$DE_standardized <- sapply(M$DE, function(x) {
      if (is.na(x) || x == "") return(NA_character_)
      keywords <- str_split(x, ";")[[1]]
      keywords <- trimws(keywords)
      keywords <- tolower(keywords)  # 转换为小写
      keywords <- gsub("\\s+", " ", keywords)  # 移除多余空格
      paste(keywords, collapse = "; ")
    })
    log_message("关键词标准化完成")
  }
  
  # 3. 数据质量评估
  log_message("开始数据质量评估")
  
  # 计算字段完整性
  field_completeness <- sapply(colnames(M), function(col) {
    sum(!is.na(M[[col]]) & M[[col]] != "") / nrow(M)
  })
  
  # 显示关键字段的完整性
  key_fields <- c("TI", "AU", "PY", "SO", "DI", "AB", "DE", "TC")
  for (field in key_fields) {
    if (field %in% names(field_completeness)) {
      log_message(sprintf("%s字段完整性: %.1f%%", field, 100 * field_completeness[field]))
    }
  }
  
  # 4. 保存增强数据
  output_file <- here("data_repository", "02_enhanced_dataset", "enhanced_data_optimized.rds")
  saveRDS(M, output_file)
  log_message(sprintf("增强数据已保存: %s", output_file))
  
  # 5. 生成简单报告
  report_file <- here("data_repository", "04_enhancement_reports", "enhancement_summary.txt")
  
  # 确保报告目录存在
  dir.create(dirname(report_file), recursive = TRUE, showWarnings = FALSE)
  
  sink(report_file)
  cat("=== 数据增强报告 ===\n")
  cat("生成时间:", format(Sys.time()), "\n\n")
  cat("数据统计:\n")
  cat("- 总记录数:", nrow(M), "\n")
  cat("- 总字段数:", ncol(M), "\n")
  cat("- 新增字段数:", sum(grepl("_standardized$", colnames(M))), "\n\n")
  
  cat("字段完整性:\n")
  for (field in key_fields) {
    if (field %in% names(field_completeness)) {
      cat(sprintf("- %s: %.1f%%\n", field, 100 * field_completeness[field]))
    }
  }
  
  cat("\n新增字段:\n")
  new_fields <- colnames(M)[grepl("_standardized$", colnames(M))]
  for (field in new_fields) {
    cat("-", field, "\n")
  }
  sink()
  
  log_message(sprintf("增强报告已保存: %s", report_file))
  log_message("数据增强流程完成")
  
  return(M)
}

# 运行主函数
if (!interactive()) {
  enhanced_data <- main()
} 
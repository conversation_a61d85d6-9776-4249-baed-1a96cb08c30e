% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/summary.bibliometrix.R
\name{summary.bibliometrix}
\alias{summary.bibliometrix}
\title{Summarizing bibliometric analysis results}
\usage{
\method{summary}{bibliometrix}(object, ...)
}
\arguments{
\item{object}{is the object for which a summary is desired.}

\item{...}{can accept two arguments:\cr
\code{k} integer, used for table formatting (number of rows). Default value is 10.\cr
\code{pause} logical, used to allow pause in screen scrolling of results. Default value is \code{pause = FALSE}.\cr
\code{width} integer, used to define screen output width. Default value is \code{width = 120}.
\code{verbose} logical, used to allow screen output. Default is TRUE.}
}
\value{
The function \code{summary} computes and returns a list of summary statistics of the object of class \code{bibliometrics}.

the list contains the following objects:
\tabular{lll}{
\code{MainInformation}   \tab   \tab Main Information about Data\cr
\code{AnnualProduction}  \tab   \tab Annual Scientific Production\cr
\code{AnnualGrowthRate}  \tab   \tab Annual Percentage Growth Rate\cr
\code{MostProdAuthors}   \tab   \tab Most Productive Authors\cr
\code{MostCitedPapers}   \tab   \tab Top manuscripts per number of citations\cr
\code{MostProdCountries} \tab   \tab Corresponding Author's Countries\cr
\code{TCperCountries}    \tab   \tab Total Citation per Countries\cr
\code{MostRelSources}    \tab   \tab Most Relevant Sources\cr
\code{MostRelKeywords}   \tab   \tab Most Relevant Keywords}
}
\description{
\code{summary} method for class '\code{bibliometrix}'
}
\examples{
data(scientometrics, package = "bibliometrixData")

results <- biblioAnalysis(scientometrics)

summary(results)

}
\seealso{
\code{\link{biblioAnalysis}} function for bibliometric analysis

\code{\link{plot}} to draw some useful plots of the results.
}

# 加载必要的包
library(tidyverse)
library(here)
library(httr)
library(jsonlite)
library(openxlsx)

# 设置日志函数
log_message <- function(message, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  cat(sprintf("[%s] [%s] %s\n", timestamp, toupper(type), message))
}

# 通过标题、作者、年份获取DOI的函数
get_doi_by_metadata <- function(title, author, year) {
  # 构建API请求URL（使用Crossref API）
  url <- sprintf("https://api.crossref.org/works?query.bibliographic=%s&query.author=%s&query.published=%s&rows=1", 
                 URLencode(title), URLencode(author), year)
  
  # 发送请求，添加User-Agent
  response <- tryCatch({
    GET(url, user_agent("BiblioEnhancer/1.0 (mailto:<EMAIL>)"))
  }, error = function(e) {
    log_message(sprintf("请求失败: %s", e$message), "error")
    return(NULL)
  })
  
  # 检查响应
  if (is.null(response) || status_code(response) != 200) {
    log_message(sprintf("API请求失败，状态码: %s", 
                       ifelse(is.null(response), "NULL", status_code(response))), 
                "error")
    return(NA)
  }
  
  # 解析响应
  content <- tryCatch({
    fromJSON(rawToChar(response$content))
  }, error = function(e) {
    log_message(sprintf("解析响应失败: %s", e$message), "error")
    return(NULL)
  })
  
  # 提取DOI
  if (!is.null(content) && length(content$message$items) > 0) {
    doi <- content$message$items$DOI[1]
    if (!is.null(doi) && doi != "") {
      return(doi)
    }
  }
  
  return(NA)
}

# 主函数
complete_doi_by_metadata <- function(input_file) {
  log_message("开始通过标题、作者、年份补全DOI")
  
  # 1. 加载数据
  log_message("步骤1: 加载数据")
  if (file.exists(input_file)) {
    missing_doi_records <- read_csv(input_file, show_col_types = FALSE)
    log_message(sprintf("成功加载数据: %d行", nrow(missing_doi_records)))
  } else {
    stop(sprintf("输入文件不存在: %s", input_file))
  }
  
  # 2. 创建结果数据框
  results <- data.frame(
    UT = missing_doi_records$UT,
    TI = missing_doi_records$TI,
    AU = missing_doi_records$AU,
    PY = missing_doi_records$PY,
    SO = missing_doi_records$SO,
    DOI = NA,
    status = "pending",
    stringsAsFactors = FALSE
  )
  
  # 3. 批量处理
  # 只处理前5条记录用于测试
  results <- results[1:min(5, nrow(results)), ]
  total_records <- nrow(results)
  log_message(sprintf("开始处理 %d 条记录", total_records))
  
  for (i in 1:total_records) {
    log_message(sprintf("处理第 %d/%d 条记录", i, total_records))
    
    # 获取DOI
    doi <- get_doi_by_metadata(results$TI[i], results$AU[i], results$PY[i])
    
    # 更新结果
    results$DOI[i] <- doi
    results$status[i] <- ifelse(is.na(doi), "failed", "success")
    
    # 添加延时避免API限制
    Sys.sleep(1)
  }
  
  # 4. 统计结果
  success_count <- sum(results$status == "success")
  success_rate <- 100 * success_count / total_records
  
  log_message(sprintf("\n补全结果统计:"))
  log_message(sprintf("总记录数: %d", total_records))
  log_message(sprintf("成功补全: %d (%.2f%%)", success_count, success_rate))
  log_message(sprintf("补全失败: %d (%.2f%%)", 
                     total_records - success_count, 
                     100 - success_rate))
  
  # 5. 保存结果
  output_dir <- here("data_repository", "04_enhancement_reports")
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  
  # 保存为RDS
  saveRDS(results, file.path(output_dir, "doi_completion_by_metadata.rds"))
  
  # 保存为Excel
  wb <- createWorkbook()
  addWorksheet(wb, "补全结果")
  writeData(wb, "补全结果", results)
  
  # 添加统计信息
  addWorksheet(wb, "统计信息")
  stats <- data.frame(
    metric = c("总记录数", "成功补全", "补全失败", "成功率"),
    value = c(total_records, success_count, total_records - success_count, 
              sprintf("%.2f%%", success_rate))
  )
  writeData(wb, "统计信息", stats)
  
  # 保存Excel文件
  saveWorkbook(wb, file.path(output_dir, "doi_completion_by_metadata.xlsx"), 
               overwrite = TRUE)
  
  log_message(sprintf("\n结果已保存至: %s", output_dir))
  
  return(results)
}

# 执行补全
input_file <- here("data_repository", "04_enhancement_reports", 
                   "missing_doi_records.csv")
results <- complete_doi_by_metadata(input_file) 
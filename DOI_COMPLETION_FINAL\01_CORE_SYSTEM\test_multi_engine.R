# DOI补全多引擎系统测试脚本
# 测试Crossref + OpenAlex双引擎DOI补全功能

cat("=== DOI补全多引擎系统测试 ===\n\n")

# 加载多引擎系统
source("doi_completion_multi_engine.R")

# 测试用例
test_cases <- list(
  list(
    title = "MR imaging of muscles of mastication",
    authors = "Smith J",
    year = 1995,
    journal = "American Journal of Neuroradiology",
    description = "经典医学影像学论文"
  ),
  list(
    title = "Machine learning applications in healthcare",
    authors = "Johnson A",
    year = 2020,
    journal = "Nature Medicine",
    description = "机器学习医疗应用"
  ),
  list(
    title = "Deep learning for natural language processing",
    authors = "Brown C",
    year = 2019,
    journal = "Journal of Machine Learning Research",
    description = "深度学习NLP"
  ),
  list(
    title = "Climate change impacts on biodiversity",
    authors = "Wilson D",
    year = 2021,
    journal = "Science",
    description = "气候变化生物多样性"
  ),
  list(
    title = "Quantum computing algorithms",
    authors = "Davis E",
    year = 2022,
    journal = "Physical Review Letters",
    description = "量子计算算法"
  )
)

# 执行测试
test_results <- list()

cat("开始测试多引擎DOI补全系统...\n\n")

for (i in 1:length(test_cases)) {
  test_case <- test_cases[[i]]
  
  cat(sprintf("=== 测试案例 %d: %s ===\n", i, test_case$description))
  cat(sprintf("标题: %s\n", test_case$title))
  cat(sprintf("作者: %s | 年份: %s | 期刊: %s\n\n", 
              test_case$authors, test_case$year, test_case$journal))
  
  # 测试多引擎搜索 (优先Crossref)
  cat("🔍 测试1: 多引擎搜索 (优先Crossref)\n")
  start_time <- Sys.time()
  
  result_crossref_first <- search_doi_multi_engine(
    title = test_case$title,
    authors = test_case$authors,
    year = test_case$year,
    journal = test_case$journal,
    prefer_crossref = TRUE
  )
  
  time_crossref_first <- as.numeric(Sys.time() - start_time, units = "secs")
  
  if (!is.null(result_crossref_first)) {
    quality <- assess_quality(result_crossref_first$title_similarity, result_crossref_first$final_score)
    cat(sprintf("✅ 成功找到DOI: %s\n", result_crossref_first$doi))
    cat(sprintf("   来源: %s | 质量: %s | 评分: %.3f\n", 
                result_crossref_first$source, quality, result_crossref_first$final_score))
    cat(sprintf("   标题相似度: %.3f | 期刊相似度: %.3f | 年份相似度: %.3f\n",
                result_crossref_first$title_similarity, 
                result_crossref_first$journal_similarity,
                result_crossref_first$year_similarity))
  } else {
    cat("❌ 未找到匹配的DOI\n")
  }
  cat(sprintf("   用时: %.2f秒\n\n", time_crossref_first))
  
  # 测试多引擎搜索 (优先OpenAlex)
  cat("🔍 测试2: 多引擎搜索 (优先OpenAlex)\n")
  start_time <- Sys.time()
  
  result_openalex_first <- search_doi_multi_engine(
    title = test_case$title,
    authors = test_case$authors,
    year = test_case$year,
    journal = test_case$journal,
    prefer_crossref = FALSE
  )
  
  time_openalex_first <- as.numeric(Sys.time() - start_time, units = "secs")
  
  if (!is.null(result_openalex_first)) {
    quality <- assess_quality(result_openalex_first$title_similarity, result_openalex_first$final_score)
    cat(sprintf("✅ 成功找到DOI: %s\n", result_openalex_first$doi))
    cat(sprintf("   来源: %s | 质量: %s | 评分: %.3f\n", 
                result_openalex_first$source, quality, result_openalex_first$final_score))
    cat(sprintf("   标题相似度: %.3f | 期刊相似度: %.3f | 年份相似度: %.3f\n",
                result_openalex_first$title_similarity, 
                result_openalex_first$journal_similarity,
                result_openalex_first$year_similarity))
  } else {
    cat("❌ 未找到匹配的DOI\n")
  }
  cat(sprintf("   用时: %.2f秒\n\n", time_openalex_first))
  
  # 单独测试Crossref
  cat("🔍 测试3: 仅Crossref API\n")
  start_time <- Sys.time()
  
  result_crossref_only <- search_doi_crossref(
    title = test_case$title,
    authors = test_case$authors,
    year = test_case$year,
    journal = test_case$journal
  )
  
  time_crossref_only <- as.numeric(Sys.time() - start_time, units = "secs")
  
  if (!is.null(result_crossref_only)) {
    quality <- assess_quality(result_crossref_only$title_similarity, result_crossref_only$final_score)
    cat(sprintf("✅ Crossref找到DOI: %s\n", result_crossref_only$doi))
    cat(sprintf("   质量: %s | 评分: %.3f\n", quality, result_crossref_only$final_score))
  } else {
    cat("❌ Crossref未找到匹配的DOI\n")
  }
  cat(sprintf("   用时: %.2f秒\n\n", time_crossref_only))
  
  # 单独测试OpenAlex
  cat("🔍 测试4: 仅OpenAlex API\n")
  start_time <- Sys.time()
  
  result_openalex_only <- search_doi_openalex(
    title = test_case$title,
    authors = test_case$authors,
    year = test_case$year,
    journal = test_case$journal
  )
  
  time_openalex_only <- as.numeric(Sys.time() - start_time, units = "secs")
  
  if (!is.null(result_openalex_only)) {
    quality <- assess_quality(result_openalex_only$title_similarity, result_openalex_only$final_score)
    cat(sprintf("✅ OpenAlex找到DOI: %s\n", result_openalex_only$doi))
    cat(sprintf("   质量: %s | 评分: %.3f\n", quality, result_openalex_only$final_score))
  } else {
    cat("❌ OpenAlex未找到匹配的DOI\n")
  }
  cat(sprintf("   用时: %.2f秒\n\n", time_openalex_only))
  
  # 保存测试结果
  test_results[[i]] <- list(
    case = test_case,
    crossref_first = result_crossref_first,
    openalex_first = result_openalex_first,
    crossref_only = result_crossref_only,
    openalex_only = result_openalex_only,
    times = list(
      crossref_first = time_crossref_first,
      openalex_first = time_openalex_first,
      crossref_only = time_crossref_only,
      openalex_only = time_openalex_only
    )
  )
  
  cat(paste(rep("=", 80), collapse = ""), "\n\n")
  
  # API调用间隔
  if (i < length(test_cases)) {
    cat("等待2秒后继续下一个测试...\n\n")
    Sys.sleep(2)
  }
}

# 生成测试报告
cat("=== 测试结果汇总 ===\n\n")

success_count_crossref_first <- sum(sapply(test_results, function(x) !is.null(x$crossref_first)))
success_count_openalex_first <- sum(sapply(test_results, function(x) !is.null(x$openalex_first)))
success_count_crossref_only <- sum(sapply(test_results, function(x) !is.null(x$crossref_only)))
success_count_openalex_only <- sum(sapply(test_results, function(x) !is.null(x$openalex_only)))

total_tests <- length(test_cases)

cat(sprintf("总测试案例: %d\n", total_tests))
cat(sprintf("多引擎(优先Crossref)成功率: %.1f%% (%d/%d)\n", 
            100 * success_count_crossref_first / total_tests, success_count_crossref_first, total_tests))
cat(sprintf("多引擎(优先OpenAlex)成功率: %.1f%% (%d/%d)\n", 
            100 * success_count_openalex_first / total_tests, success_count_openalex_first, total_tests))
cat(sprintf("仅Crossref成功率: %.1f%% (%d/%d)\n", 
            100 * success_count_crossref_only / total_tests, success_count_crossref_only, total_tests))
cat(sprintf("仅OpenAlex成功率: %.1f%% (%d/%d)\n", 
            100 * success_count_openalex_only / total_tests, success_count_openalex_only, total_tests))

# 计算平均响应时间
avg_time_crossref_first <- mean(sapply(test_results, function(x) x$times$crossref_first))
avg_time_openalex_first <- mean(sapply(test_results, function(x) x$times$openalex_first))
avg_time_crossref_only <- mean(sapply(test_results, function(x) x$times$crossref_only))
avg_time_openalex_only <- mean(sapply(test_results, function(x) x$times$openalex_only))

cat(sprintf("\n平均响应时间:\n"))
cat(sprintf("多引擎(优先Crossref): %.2f秒\n", avg_time_crossref_first))
cat(sprintf("多引擎(优先OpenAlex): %.2f秒\n", avg_time_openalex_first))
cat(sprintf("仅Crossref: %.2f秒\n", avg_time_crossref_only))
cat(sprintf("仅OpenAlex: %.2f秒\n", avg_time_openalex_only))

# 分析多引擎优势
multi_engine_advantage <- success_count_crossref_first - max(success_count_crossref_only, success_count_openalex_only)
if (multi_engine_advantage > 0) {
  cat(sprintf("\n🎉 多引擎系统优势: 比单一引擎多找到 %d 个DOI\n", multi_engine_advantage))
} else {
  cat(sprintf("\n📊 多引擎系统与最佳单一引擎性能相当\n"))
}

# 质量分析
cat(sprintf("\n=== 质量分析 ===\n"))
for (i in 1:length(test_results)) {
  result <- test_results[[i]]
  if (!is.null(result$crossref_first)) {
    quality <- assess_quality(result$crossref_first$title_similarity, result$crossref_first$final_score)
    cat(sprintf("案例%d: %s质量 (%.3f) - %s\n", 
                i, quality, result$crossref_first$final_score, result$crossref_first$source))
  }
}

cat(sprintf("\n✅ 多引擎DOI补全系统测试完成！\n"))

# 保存测试结果
save(test_results, file = "../../02_FINAL_RESULTS/multi_engine_test_results.RData")
cat(sprintf("📁 测试结果已保存: multi_engine_test_results.RData\n"))

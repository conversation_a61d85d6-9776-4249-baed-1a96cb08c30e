# PubMed增强系统 - 包含MeSH Publication Types提取
# 获取精确的生物医学文献类型信息

library(httr)
library(jsonlite)
library(stringdist)
library(xml2)

# 加载核心函数
source("doi_completion_core.R")

cat("=== PubMed增强系统 (MeSH Publication Types) ===\n")
cat("🎯 提取精确的生物医学文献类型信息\n")
cat("📊 数据源: PubMed E-utilities API + EFetch XML\n")
cat("🏷️  包含: MeSH Publication Types, 文献分类\n\n")

# === MeSH Publication Types映射 ===
mesh_publication_types <- list(
  # 研究类型
  "Clinical Trial" = "临床试验",
  "Randomized Controlled Trial" = "随机对照试验", 
  "Controlled Clinical Trial" = "对照临床试验",
  "Meta-Analysis" = "荟萃分析",
  "Systematic Review" = "系统综述",
  "Review" = "综述",
  "Case Reports" = "病例报告",
  "Observational Study" = "观察性研究",
  "Cohort Studies" = "队列研究",
  "Cross-Sectional Studies" = "横断面研究",
  
  # 文献类型
  "Journal Article" = "期刊文章",
  "Research Support" = "研究资助",
  "Letter" = "信件",
  "Editorial" = "社论",
  "Comment" = "评论",
  "News" = "新闻",
  "Biography" = "传记",
  "Historical Article" = "历史文章",
  
  # 特殊类型
  "Multicenter Study" = "多中心研究",
  "Comparative Study" = "比较研究",
  "Evaluation Studies" = "评估研究",
  "Validation Studies" = "验证研究",
  "Twin Study" = "双胞胎研究",
  "Animal Studies" = "动物研究"
)

# === 增强的PubMed DOI搜索函数 (包含MeSH) ===
search_doi_pubmed_enhanced <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 3]
    if (length(keywords) > 4) keywords <- keywords[1:4]
    if (length(keywords) == 0) return(NULL)
    
    # Step 1: ESearch - 搜索获取PMID
    search_terms <- paste(keywords, collapse = " AND ")
    esearch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=%s&retmax=10&retmode=json",
                          URLencode(search_terms))
    
    cat(sprintf("PubMed增强搜索: %s\n", search_terms))
    
    search_response <- GET(esearch_url, 
                          user_agent("DOI_Completion_PubMed_Enhanced/1.0"),
                          timeout(30))
    
    if (status_code(search_response) != 200) {
      cat("PubMed ESearch失败, 状态码:", status_code(search_response), "\n")
      return(NULL)
    }
    
    search_content <- fromJSON(rawToChar(search_response$content))
    
    if (is.null(search_content$esearchresult$idlist) || 
        length(search_content$esearchresult$idlist) == 0) {
      cat("PubMed未返回搜索结果\n")
      return(NULL)
    }
    
    pmids <- search_content$esearchresult$idlist
    cat(sprintf("PubMed返回 %d 个PMID\n", length(pmids)))
    
    # Step 2: EFetch - 获取详细XML数据 (包含MeSH)
    pmid_list <- paste(pmids[1:min(5, length(pmids))], collapse = ",")
    efetch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=%s&retmode=xml",
                         pmid_list)
    
    fetch_response <- GET(efetch_url,
                         user_agent("DOI_Completion_PubMed_Enhanced/1.0"),
                         timeout(30))
    
    if (status_code(fetch_response) != 200) {
      cat("PubMed EFetch失败, 状态码:", status_code(fetch_response), "\n")
      return(NULL)
    }
    
    # 解析XML数据
    xml_content <- read_xml(rawToChar(fetch_response$content))
    articles <- xml_find_all(xml_content, "//PubmedArticle")
    
    if (length(articles) == 0) {
      cat("PubMed XML解析失败\n")
      return(NULL)
    }
    
    best_match <- NULL
    best_score <- 0
    
    # 评估每篇文章
    for (i in 1:min(5, length(articles))) {
      article <- articles[[i]]
      
      # 提取PMID
      pmid_node <- xml_find_first(article, ".//PMID")
      candidate_pmid <- if (!is.null(pmid_node)) xml_text(pmid_node) else ""
      
      # 提取标题
      title_node <- xml_find_first(article, ".//ArticleTitle")
      candidate_title <- if (!is.null(title_node)) xml_text(title_node) else ""
      
      # 提取期刊
      journal_node <- xml_find_first(article, ".//Journal/Title")
      candidate_journal <- if (!is.null(journal_node)) xml_text(journal_node) else ""
      
      # 提取年份
      year_node <- xml_find_first(article, ".//PubDate/Year")
      candidate_year <- if (!is.null(year_node)) xml_text(year_node) else ""
      
      # 提取DOI
      doi_nodes <- xml_find_all(article, ".//ArticleId[@IdType='doi']")
      candidate_doi <- ""
      if (length(doi_nodes) > 0) {
        candidate_doi <- xml_text(doi_nodes[[1]])
      }
      
      # 如果没有DOI，跳过
      if (candidate_doi == "" || is.na(candidate_doi)) {
        cat(sprintf("PMID %s: 无DOI，跳过\n", candidate_pmid))
        next
      }
      
      # === 提取MeSH Publication Types ===
      pub_type_nodes <- xml_find_all(article, ".//PublicationType")
      mesh_pub_types <- c()
      mesh_pub_types_cn <- c()
      
      if (length(pub_type_nodes) > 0) {
        for (pt_node in pub_type_nodes) {
          pub_type <- xml_text(pt_node)
          mesh_pub_types <- c(mesh_pub_types, pub_type)
          
          # 中文翻译
          if (pub_type %in% names(mesh_publication_types)) {
            mesh_pub_types_cn <- c(mesh_pub_types_cn, mesh_publication_types[[pub_type]])
          } else {
            mesh_pub_types_cn <- c(mesh_pub_types_cn, pub_type)
          }
        }
      }
      
      # === 提取MeSH主题词 ===
      mesh_heading_nodes <- xml_find_all(article, ".//MeshHeading/DescriptorName")
      mesh_headings <- c()
      if (length(mesh_heading_nodes) > 0) {
        mesh_headings <- sapply(mesh_heading_nodes, xml_text)
      }
      
      # === 提取关键词 ===
      keyword_nodes <- xml_find_all(article, ".//Keyword")
      keywords_list <- c()
      if (length(keyword_nodes) > 0) {
        keywords_list <- sapply(keyword_nodes, xml_text)
      }
      
      # 计算相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      journal_sim <- calculate_journal_similarity(journal, candidate_journal)
      year_sim <- calculate_year_similarity(year, candidate_year)
      
      # 生物医学相关性 (基于MeSH)
      bio_rel <- 0.5
      if (length(mesh_headings) > 0 || length(mesh_pub_types) > 0) {
        bio_rel <- 1.0  # 有MeSH信息说明是标准生物医学文献
      }
      
      # 综合评分
      final_score <- (title_sim * 0.6) + (journal_sim * 0.2) + 
                     (year_sim * 0.1) + (bio_rel * 0.1)
      
      cat(sprintf("PMID %s: T=%.3f, J=%.3f, Y=%.3f, B=%.3f, 总分=%.3f\n",
                  candidate_pmid, title_sim, journal_sim, year_sim, bio_rel, final_score))
      cat(sprintf("  MeSH类型: %s\n", paste(mesh_pub_types, collapse = ", ")))
      
      # 接受条件
      if (title_sim >= 0.65 &&
          journal_sim >= 0.25 &&
          year_sim >= 0.20 &&
          final_score >= 0.55 &&
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = candidate_doi,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          pmid = candidate_pmid,
          mesh_publication_types = mesh_pub_types,
          mesh_publication_types_cn = mesh_pub_types_cn,
          mesh_headings = mesh_headings[1:min(10, length(mesh_headings))],  # 限制数量
          keywords = keywords_list[1:min(10, length(keywords_list))],
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          biomedical_relevance = bio_rel,
          final_score = final_score,
          source = "pubmed_enhanced"
        )
        
        cat(sprintf("  ✅ 新的最佳匹配! DOI: %s (PMID: %s)\n", candidate_doi, candidate_pmid))
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    cat("PubMed增强API错误:", e$message, "\n")
    return(NULL)
  })
}

# === 测试PubMed增强系统 ===
test_pubmed_enhanced <- function() {
  cat("\n=== PubMed增强系统测试 ===\n")
  
  # 明确的生物医学测试用例
  test_cases <- list(
    list(
      title = "Randomized controlled trial of aspirin for heart disease",
      authors = "Smith J",
      year = 2020,
      journal = "New England Journal of Medicine",
      description = "心脏病阿司匹林随机对照试验"
    ),
    list(
      title = "Meta-analysis of cancer immunotherapy effectiveness",
      authors = "Johnson A",
      year = 2019,
      journal = "Lancet Oncology",
      description = "癌症免疫疗法荟萃分析"
    )
  )
  
  success_count <- 0
  
  for (i in 1:length(test_cases)) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n--- 测试案例 %d: %s ---\n", i, test_case$description))
    cat(sprintf("标题: %s\n", test_case$title))
    
    result <- search_doi_pubmed_enhanced(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal
    )
    
    if (!is.null(result)) {
      success_count <- success_count + 1
      quality <- assess_quality(result$title_similarity, result$final_score)
      
      cat(sprintf("✅ PubMed增强成功: %s (PMID: %s, 质量: %s)\n", 
                  result$doi, result$pmid, quality))
      cat(sprintf("📋 MeSH文献类型: %s\n", 
                  paste(result$mesh_publication_types_cn, collapse = ", ")))
      cat(sprintf("🏷️  MeSH主题词: %s\n", 
                  paste(result$mesh_headings[1:min(5, length(result$mesh_headings))], collapse = ", ")))
      
      if (length(result$keywords) > 0) {
        cat(sprintf("🔑 关键词: %s\n", 
                    paste(result$keywords[1:min(5, length(result$keywords))], collapse = ", ")))
      }
    } else {
      cat("❌ PubMed增强未找到匹配\n")
    }
    
    if (i < length(test_cases)) {
      cat("等待3秒...\n")
      Sys.sleep(3)
    }
  }
  
  success_rate <- 100 * success_count / length(test_cases)
  cat(sprintf("\n📊 PubMed增强系统结果: %.1f%% 成功率 (%d/%d)\n", 
              success_rate, success_count, length(test_cases)))
  
  return(list(
    success_count = success_count,
    success_rate = success_rate
  ))
}

# === 生成MeSH类型统计报告 ===
analyze_mesh_types <- function(results_list) {
  cat("\n=== MeSH Publication Types 分析 ===\n")
  
  all_types <- c()
  for (result in results_list) {
    if (!is.null(result) && !is.null(result$mesh_publication_types)) {
      all_types <- c(all_types, result$mesh_publication_types)
    }
  }
  
  if (length(all_types) > 0) {
    type_counts <- table(all_types)
    type_counts <- sort(type_counts, decreasing = TRUE)
    
    cat("📊 MeSH文献类型分布:\n")
    for (i in 1:min(10, length(type_counts))) {
      type_name <- names(type_counts)[i]
      count <- type_counts[i]
      cn_name <- if (type_name %in% names(mesh_publication_types)) {
        mesh_publication_types[[type_name]]
      } else {
        type_name
      }
      cat(sprintf("  %s (%s): %d篇\n", type_name, cn_name, count))
    }
  } else {
    cat("未找到MeSH类型数据\n")
  }
}

cat("✅ PubMed增强系统 (MeSH) 已加载\n")
cat("📋 主要函数:\n")
cat("  - search_doi_pubmed_enhanced()  : 增强PubMed搜索 (含MeSH)\n")
cat("  - test_pubmed_enhanced()        : 增强系统测试\n")
cat("  - analyze_mesh_types()          : MeSH类型分析\n")

# 自动执行测试
cat("\n🚀 开始PubMed增强系统测试...\n")
pubmed_enhanced_result <- test_pubmed_enhanced()

cat(sprintf("\n🎯 PubMed增强系统测试完成!\n"))
cat(sprintf("成功率: %.1f%%\n", pubmed_enhanced_result$success_rate))

if (pubmed_enhanced_result$success_rate > 0) {
  cat("🎉 PubMed增强系统可以提供精确的MeSH文献类型信息！\n")
  cat("📋 新增字段: MeSH Publication Types, MeSH主题词, 关键词\n")
} else {
  cat("⚠️  需要使用更具体的生物医学测试用例。\n")
}

cat("\n💡 建议: PubMed增强系统最适合需要精确文献分类的生物医学研究。\n")

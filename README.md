# 文献计量学多方法比较分析系统研究框架

本项目旨在构建一个系统化的文献计量学分析框架，用于比较不同分析工具（如 Bibliometrix, VOSviewer, CiteSpace）在处理相同数据集时的表现，并探索数据预处理对分析结果的影响。

## 项目结构

```
.
├── data/                  # 数据文件
│   ├── raw/               # 原始数据
│   ├── processed/         # 处理后的数据
│   └── output_files/      # 分析输出的数据文件
├── docs/                  # 文档
│   ├── framework/         # 框架核心文档
│   │   ├── 00_Framework_Overview.md
│   │   ├── 01_Data_Foundation_and_Preparation.md
│   │   ├── 02_Comparative_Analysis_Methodology.md
│   │   ├── 03_Analytical_Modules_and_Execution.md
│   │   └── 04_Output_Standardization_and_Application.md
│   ├── guides/            # 指南性文档
│   │   └── Practical_Execution_Guide.md
│   ├── reference_code/    # 参考代码
│   │   ├── pipline2.r
│   │   └── bibliometrix-master/
│   └── archive/           # 存档的旧版本文档
│       └── 文献计量学多方法比较分析系统研究框架_v2.md
├── R/                     # R 脚本
├── visualizations/        # 可视化结果图表
├── logs/                  # 日志文件
├── .gitignore             # Git 忽略配置文件
└── README.md              # 项目说明
```

## 使用说明

(后续添加详细的使用说明、环境配置、执行步骤等)

## 贡献

(如何贡献的说明)

## 许可证

(项目的许可证信息)

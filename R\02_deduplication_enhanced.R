# 02_deduplication_enhanced.R
# 基于bibliometrix的增强去重策略
# 参考02_deduplication_extreme.R的实现方式

# --- 0. 配置管理 ---
# 加载必要的包
required_packages <- c("bibliometrix", "here", "dplyr", "stringr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 定义日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
  if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
    cat(formatted_msg, "\n", file = log_con)
  }
}

config <- list(
  # 输入输出路径配置
  paths = list(
    input = here("data_repository", "02_enhanced_dataset"),
    output = here("data_repository", "03_deduplicated_dataset"),
    logs = here("data_repository", "05_execution_logs", "deduplication_logs"),
    reports = here("data_repository", "06_data_reports", "deduplication_reports")
  ),
  # 文件命名配置
  files = list(
    input = "enhanced_data_initial.rds",
    output = "data_deduplicated.rds",
    log = "deduplication_enhanced.log"
  ),
  # 去重配置
  deduplication = list(
    # 标题去重阈值
    title_tolerance = 0.95,
    # 是否执行UT精确匹配
    ut_exact = TRUE,
    # 是否执行标题模糊匹配
    title_fuzzy = TRUE,
    # 是否执行摘要模糊匹配（资源密集型）
    abstract_fuzzy = FALSE
  )
)

# --- 1. 设置环境 ---
log_message("开始增强去重流程")

# --- 2. 定义输入和输出路径 ---
input_file <- file.path(config$paths$input, config$files$input)
output_file <- file.path(config$paths$output, config$files$output)
log_file <- file.path(config$paths$logs, config$files$log)

# 创建必要的目录
suppressMessages({
  for (dir_path in c(config$paths$output, config$paths$logs, config$paths$reports)) {
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = TRUE)
      log_message(sprintf("创建目录: %s", dir_path))
    }
  }
})

# --- 3. 加载数据 ---
if (!file.exists(input_file)) {
  log_message("错误：输入数据文件不存在", "error")
  stop(sprintf("请先运行 01_data_import_enhanced.R 脚本生成文件: %s", input_file))
}

M <- readRDS(input_file)
log_message(sprintf("成功加载数据，初始记录数: %d", nrow(M)))

# --- 4. 初始化日志文件连接 ---
log_con <- file(log_file, "w")
log_message(sprintf("日志文件已创建: %s", log_file))

# 记录开始时间
start_time <- Sys.time()
log_message(sprintf("去重开始时间: %s", format(start_time)))

# --- 5. 执行去重步骤 ---
M_dedup <- M
deduplication_summary <- list()

# 5.1 UT精确匹配
if (config$deduplication$ut_exact && "UT" %in% names(M_dedup)) {
  log_message("开始 UT 精确匹配...")
  M_dedup_before_ut <- nrow(M_dedup)
  
  # 保存去重前的重复记录
  ut_duplicates <- M_dedup[duplicated(M_dedup$UT) | duplicated(M_dedup$UT, fromLast = TRUE), ]
  if (nrow(ut_duplicates) > 0) {
    ut_duplicates_file <- file.path(config$paths$output, "ut_duplicates_before_removal.rds")
    saveRDS(ut_duplicates, ut_duplicates_file)
    log_message(sprintf("UT重复记录已保存至: %s", ut_duplicates_file))
  }
  
  # 执行去重
  M_dedup <- M_dedup %>% dplyr::distinct(UT, .keep_all = TRUE)
  ut_removed <- M_dedup_before_ut - nrow(M_dedup)
  
  log_message(sprintf("UT 精确匹配后记录数: %d (移除 %d 条)", nrow(M_dedup), ut_removed))
  deduplication_summary$ut_removed <- ut_removed
} else {
  log_message("跳过 UT 精确匹配（未找到UT字段或已禁用）", "warning")
}

# 5.2 标题模糊匹配
if (config$deduplication$title_fuzzy && "TI" %in% names(M_dedup)) {
  log_message("开始标题模糊匹配...")
  log_message(sprintf("使用标题相似度阈值: %.2f", config$deduplication$title_tolerance))
  
  M_dedup_before_title <- nrow(M_dedup)
  
  # 使用bibliometrix的duplicatedMatching函数
  M_dedup <- duplicatedMatching(M_dedup, 
                               Field = "TI", 
                               exact = FALSE, 
                               tol = config$deduplication$title_tolerance)
  
  title_removed <- M_dedup_before_title - nrow(M_dedup)
  log_message(sprintf("标题模糊匹配后记录数: %d (移除 %d 条)", nrow(M_dedup), title_removed))
  deduplication_summary$title_removed <- title_removed
} else {
  log_message("跳过标题模糊匹配（未找到TI字段或已禁用）", "warning")
}

# 5.3 摘要模糊匹配（可选，资源密集型）
if (config$deduplication$abstract_fuzzy && "AB" %in% names(M_dedup)) {
  log_message("开始摘要模糊匹配...")
  log_message("注意：摘要模糊匹配是资源密集型操作，可能需要较长时间")
  
  M_dedup_before_abstract <- nrow(M_dedup)
  
  # 使用bibliometrix的duplicatedMatching函数
  M_dedup <- duplicatedMatching(M_dedup, 
                               Field = "AB", 
                               exact = FALSE, 
                               tol = 0.90)
  
  abstract_removed <- M_dedup_before_abstract - nrow(M_dedup)
  log_message(sprintf("摘要模糊匹配后记录数: %d (移除 %d 条)", nrow(M_dedup), abstract_removed))
  deduplication_summary$abstract_removed <- abstract_removed
} else {
  log_message("跳过摘要模糊匹配（未找到AB字段或已禁用）", "info")
}

# --- 6. 生成去重报告 ---
log_message("生成去重报告...")

# 计算总体统计
total_removed <- nrow(M) - nrow(M_dedup)
deduplication_summary$total_removed <- total_removed
deduplication_summary$final_count <- nrow(M_dedup)
deduplication_summary$initial_count <- nrow(M)
deduplication_summary$reduction_rate <- round((total_removed / nrow(M)) * 100, 2)

# 保存去重报告
report_file <- file.path(config$paths$reports, "deduplication_report.txt")
report_con <- file(report_file, "w")

cat("=== 文献去重报告 ===\n", file = report_con)
cat(sprintf("报告生成时间: %s\n", format(Sys.time())), file = report_con)
cat(sprintf("初始记录数: %d\n", deduplication_summary$initial_count), file = report_con)
cat(sprintf("最终记录数: %d\n", deduplication_summary$final_count), file = report_con)
cat(sprintf("总移除数: %d\n", deduplication_summary$total_removed), file = report_con)
cat(sprintf("去重率: %.2f%%\n", deduplication_summary$reduction_rate), file = report_con)
cat("\n=== 详细去重统计 ===\n", file = report_con)

if (!is.null(deduplication_summary$ut_removed)) {
  cat(sprintf("UT精确匹配移除: %d 条\n", deduplication_summary$ut_removed), file = report_con)
}
if (!is.null(deduplication_summary$title_removed)) {
  cat(sprintf("标题模糊匹配移除: %d 条\n", deduplication_summary$title_removed), file = report_con)
}
if (!is.null(deduplication_summary$abstract_removed)) {
  cat(sprintf("摘要模糊匹配移除: %d 条\n", deduplication_summary$abstract_removed), file = report_con)
}

cat("\n=== 去重策略说明 ===\n", file = report_con)
cat("1. UT精确匹配: 基于文献唯一标识符的精确去重\n", file = report_con)
cat("2. 标题模糊匹配: 基于标题相似度的模糊去重\n", file = report_con)
cat("3. 摘要模糊匹配: 基于摘要相似度的模糊去重（可选）\n", file = report_con)

close(report_con)
log_message(sprintf("去重报告已保存至: %s", report_file))

# --- 7. 保存去重结果 ---
log_message("开始保存去重结果...")
saveRDS(M_dedup, file = output_file)
log_message(sprintf("去重后的数据已保存至: %s", output_file))

# --- 8. 清理和日志记录 ---
end_time <- Sys.time()
duration <- difftime(end_time, start_time, units = "mins")
log_message(sprintf("去重完成，总耗时: %.2f 分钟", as.numeric(duration)))
log_message(sprintf("初始记录数: %d", nrow(M)))
log_message(sprintf("最终记录数: %d", nrow(M_dedup)))
log_message(sprintf("移除重复数: %d", total_removed))
log_message(sprintf("去重率: %.2f%%", deduplication_summary$reduction_rate))
log_message("脚本执行完毕")

# 关闭日志
close(log_con) 
% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/retrievalByAuthorID.R
\name{retrievalByAuthorID}
\alias{retrievalByAuthorID}
\title{Get Author Content on SCOPUS by ID}
\usage{
retrievalByAuthorID(id, api_key, remove.duplicated = TRUE, country = TRUE)
}
\arguments{
\item{id}{is a vector of characters containing the author's SCOPUS IDs. 
SCOPUS IDs con be obtained using the function \code{\link{idByAuthor}}.}

\item{api_key}{is a character. It contains the Elsvier API key. Information about how to obtain an API Key \href{https://dev.elsevier.com/sc_apis.html}{Elsevier API website}}

\item{remove.duplicated}{is logical. If TRUE duplicated documents will be deleted from the bibliographic collection.}

\item{country}{is logical. If TRUE authors' country information will be downloaded from SCOPUS.}
}
\value{
a list containing two objects: (i) M which is a data frame with cases corresponding to articles and variables to main Field Tags named using the standard ISI WoS Field Tag codify. 
M includes the entire bibliographic collection downloaded from SCOPUS.
The main field tags are:

\tabular{lll}{
\code{AU}\tab   \tab Authors\cr
\code{TI}\tab   \tab Document Title\cr
\code{SO}\tab   \tab Publication Name (or Source)\cr
\code{DT}\tab   \tab Document Type\cr
\code{DE}\tab   \tab Authors' Keywords\cr
\code{ID}\tab   \tab Keywords associated by SCOPUS or ISI database \cr
\code{AB}\tab   \tab Abstract\cr
\code{C1}\tab   \tab Author Address\cr
\code{RP}\tab   \tab Reprint Address\cr
\code{TC}\tab   \tab Times Cited\cr
\code{PY}\tab   \tab Year\cr
\code{UT}\tab   \tab Unique Article Identifier\cr
\code{DB}\tab   \tab Database\cr}
(ii) authorDocuments which is a list containing a bibliographic data frame for each author.

LIMITATIONS: 
Currently, SCOPUS API does not allow to download document references. 
As consequence, it is not possible to perform co-citation analysis (the field CR is empty).
}
\description{
Uses SCOPUS API search to get information about documents on a set of authors using SCOPUS ID.
}
\examples{
## Request a personal API Key to Elsevier web page https://dev.elsevier.com/sc_apis.html

## api_key="your api key"

## create a data frame with the list of authors to get information and IDs
# i.e. df[1,1:3] <- c("aria","massimo","naples")
#      df[2,1:3] <- c("cuccurullo","corrado", "naples")

## run idByAuthor function
#
# authorsID <- idByAuthor(df, api_key)
#

## extract the IDs
# 
# id <- authorsID[,3]
#

## create the bibliographic collection
# 
# res <- retrievalByAuthorID(id, api_key)
#
# M <- res$M  # the entire bibliographic data frame
# M <- res$authorDocuments # the list containing a bibliographic data frame for each author

}
\seealso{
\code{\link{idByAuthor}} for downloading author information and SCOPUS ID.
}

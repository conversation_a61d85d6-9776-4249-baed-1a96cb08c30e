% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/mergeDbSources.R
\name{mergeDbSources}
\alias{mergeDbSources}
\title{Merge bibliographic data frames from supported bibliogtraphic DBs}
\usage{
mergeDbSources(..., remove.duplicated = TRUE, verbose = TRUE)
}
\arguments{
\item{...}{are the bibliographic data frames to merge.}

\item{remove.duplicated}{is logical. If TRUE duplicated documents will be deleted from the bibliographic collection.}

\item{verbose}{is logical.  If TRUE, information on duplicate documents is printed on the screen.}
}
\value{
the value returned from \code{mergeDbSources} is a bibliographic data frame.
}
\description{
Merge bibliographic data frames from different databases (WoS,SCOPUS, Lens, Openalex, etc-) into a single one.
}
\details{
bibliographic data frames are obtained by the converting function \code{\link{convert2df}}. 
The function merges data frames identifying common tag fields and duplicated records.
}
\examples{

data(isiCollection, package = "bibliometrixData")

data(scopusCollection, package = "bibliometrixData")

M <- mergeDbSources(isiCollection, scopusCollection, remove.duplicated=TRUE)

dim(M)


}
\seealso{
\code{\link{convert2df}} to import and convert an ISI or SCOPUS Export file in a bibliographic data frame.

\code{\link{biblioAnalysis}} function for bibliometric analysis.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.
}

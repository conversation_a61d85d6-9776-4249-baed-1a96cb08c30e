# 测试OpenAlex DOI补全系统

cat("=== OpenAlex DOI补全系统测试 ===\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 加载OpenAlex系统
tryCatch({
  source("openalex_doi_completion.R")
  cat("✅ OpenAlex系统加载成功\n")
}, error = function(e) {
  cat("❌ 加载OpenAlex系统失败:", e$message, "\n")
  quit(status = 1)
})

# 测试用例
test_title <- "Machine learning applications in healthcare"
test_authors <- "Johnson A"
test_year <- 2020
test_journal <- "Nature Medicine"

cat(sprintf("\n🔍 测试OpenAlex DOI搜索:\n"))
cat(sprintf("标题: %s\n", test_title))
cat(sprintf("作者: %s | 年份: %s | 期刊: %s\n\n", test_authors, test_year, test_journal))

# 测试1: 仅OpenAlex
cat("=== 测试1: 仅OpenAlex ===\n")
result_openalex <- search_doi_openalex(
  title = test_title,
  authors = test_authors,
  year = test_year,
  journal = test_journal
)

if (!is.null(result_openalex)) {
  quality <- assess_quality(result_openalex$title_similarity, result_openalex$final_score)
  cat(sprintf("\n✅ OpenAlex成功找到DOI!\n"))
  cat(sprintf("DOI: %s\n", result_openalex$doi))
  cat(sprintf("来源: %s\n", result_openalex$source))
  cat(sprintf("质量等级: %s\n", quality))
  cat(sprintf("最终评分: %.3f\n", result_openalex$final_score))
  cat(sprintf("标题相似度: %.3f\n", result_openalex$title_similarity))
} else {
  cat("\n❌ OpenAlex未找到匹配的DOI\n")
}

cat("\n", paste(rep("=", 50), collapse = ""), "\n")

# 测试2: 多引擎 (优先OpenAlex)
cat("=== 测试2: 多引擎 (优先OpenAlex) ===\n")
result_multi <- search_doi_multi_engine(
  title = test_title,
  authors = test_authors,
  year = test_year,
  journal = test_journal,
  prefer_crossref = FALSE
)

if (!is.null(result_multi)) {
  quality <- assess_quality(result_multi$title_similarity, result_multi$final_score)
  cat(sprintf("\n✅ 多引擎成功找到DOI!\n"))
  cat(sprintf("DOI: %s\n", result_multi$doi))
  cat(sprintf("来源: %s\n", result_multi$source))
  cat(sprintf("质量等级: %s\n", quality))
  cat(sprintf("最终评分: %.3f\n", result_multi$final_score))
} else {
  cat("\n❌ 多引擎未找到匹配的DOI\n")
}

cat(sprintf("\n✅ 测试完成\n"))

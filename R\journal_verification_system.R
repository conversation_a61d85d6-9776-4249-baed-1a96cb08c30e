# 期刊名称验证系统
# 处理期刊名称变更、缩写、同一期刊的不同表达方式
# 作者: Augment Agent
# 日期: 2025-06-19

cat("=== 期刊名称验证系统 ===\n")

# 加载必要的包
suppressMessages({
  library(httr)
  library(jsonlite)
  library(stringdist)
})

# 期刊名称映射数据库（常见的期刊名称变更和缩写）
journal_mapping <- list(
  # 解剖学期刊
  "ACTA ANATOMICA" = c("Cells Tissues Organs", "Acta Anatomica", "Cells, Tissues, Organs"),
  "ANATOMISCHER ANZEIGER" = c("Annals of Anatomy", "Anatomischer Anzeiger"),
  "ANATOMICAL RECORD" = c("The Anatomical Record", "Anatomical Record"),
  
  # 口腔医学期刊
  "SCANDINAVIAN JOURNAL OF DENTAL RESEARCH" = c("European Journal of Oral Sciences", "Scandinavian Journal of Dental Research"),
  "ANGLE ORTHODONTIST" = c("The Angle Orthodontist", "<PERSON>le Orthodontist"),
  "AMERICAN JOURNAL OF ORTHODONTICS AND DENTOFACIAL ORTHOPEDICS" = c("American Journal of Orthodontics and Dentofacial Orthopedics", "Am J Orthod Dentofacial Orthop"),
  
  # 神经科学期刊
  "EXPERIMENTAL BRAIN RESEARCH" = c("Experimental Brain Research", "Exp Brain Res"),
  "JOURNAL OF NEUROPHYSIOLOGY" = c("Journal of Neurophysiology", "J Neurophysiol"),
  
  # 生理学期刊
  "ACTA PHYSIOLOGICA SCANDINAVICA" = c("Acta Physiologica", "Acta Physiologica Scandinavica"),
  "JAPANESE JOURNAL OF PHYSIOLOGY" = c("The Japanese Journal of Physiology", "Japanese Journal of Physiology"),
  
  # 医学期刊
  "ARCHIVES OF ORAL BIOLOGY" = c("Archives of Oral Biology", "Arch Oral Biol"),
  "JOURNAL OF ORAL AND MAXILLOFACIAL SURGERY" = c("Journal of Oral and Maxillofacial Surgery", "J Oral Maxillofac Surg"),
  
  # 计算机期刊
  "COMPUTER METHODS AND PROGRAMS IN BIOMEDICINE" = c("Computer Methods and Programs in Biomedicine", "Comput Methods Programs Biomed"),
  
  # 形态学期刊
  "JOURNAL OF MORPHOLOGY" = c("Journal of Morphology", "J Morphol"),
  "INTERNATIONAL JOURNAL OF MORPHOLOGY" = c("International Journal of Morphology", "Int J Morphol")
)

# 标准化期刊名称函数
normalize_journal_name_advanced <- function(journal_name) {
  if (is.na(journal_name) || journal_name == "" || journal_name == "未知") return(journal_name)
  
  # 基本标准化
  normalized <- tolower(journal_name)
  normalized <- gsub("&amp;", "&", normalized)
  normalized <- gsub("\\s+", " ", normalized)
  normalized <- trimws(normalized)
  
  # 移除常见的期刊后缀和前缀
  normalized <- gsub("^the\\s+", "", normalized)
  normalized <- gsub("\\s*\\(.*\\)$", "", normalized)
  normalized <- gsub("\\s*:.*$", "", normalized)
  normalized <- gsub("\\s*-.*$", "", normalized)
  
  # 移除常见缩写符号
  normalized <- gsub("\\.", "", normalized)
  normalized <- gsub(",", "", normalized)
  
  return(normalized)
}

# 检查期刊是否为同一期刊的函数
check_journal_equivalence <- function(journal_a, journal_b) {
  if (is.na(journal_a) || is.na(journal_b) || journal_a == "未知" || journal_b == "未知") {
    return(list(is_same = FALSE, confidence = 0, reason = "缺失期刊信息"))
  }
  
  # 完全匹配
  if (tolower(journal_a) == tolower(journal_b)) {
    return(list(is_same = TRUE, confidence = 1.0, reason = "完全匹配"))
  }
  
  # 标准化后匹配
  norm_a <- normalize_journal_name_advanced(journal_a)
  norm_b <- normalize_journal_name_advanced(journal_b)
  
  if (norm_a == norm_b) {
    return(list(is_same = TRUE, confidence = 0.95, reason = "标准化后匹配"))
  }
  
  # 检查期刊映射数据库
  for (main_journal in names(journal_mapping)) {
    variants <- journal_mapping[[main_journal]]
    
    # 检查是否都在同一个期刊组中
    if ((tolower(journal_a) %in% tolower(c(main_journal, variants))) && 
        (tolower(journal_b) %in% tolower(c(main_journal, variants)))) {
      return(list(is_same = TRUE, confidence = 0.9, reason = "已知期刊名称变更"))
    }
  }
  
  # 相似度匹配
  similarity <- 1 - stringdist(norm_a, norm_b, method = "jw")
  
  if (similarity >= 0.8) {
    return(list(is_same = TRUE, confidence = similarity, reason = sprintf("高相似度匹配 (%.3f)", similarity)))
  } else if (similarity >= 0.6) {
    return(list(is_same = "可能", confidence = similarity, reason = sprintf("中等相似度匹配 (%.3f)", similarity)))
  } else {
    return(list(is_same = FALSE, confidence = similarity, reason = sprintf("低相似度 (%.3f)", similarity)))
  }
}

# 外部搜索期刊信息的函数
search_journal_external <- function(journal_name, max_results = 3) {
  if (is.na(journal_name) || journal_name == "" || journal_name == "未知") {
    return(NULL)
  }
  
  tryCatch({
    # 使用Crossref API搜索期刊信息
    query_url <- sprintf("https://api.crossref.org/journals?query=%s&rows=%d", 
                        URLencode(journal_name), max_results)
    
    response <- GET(query_url, user_agent("JournalVerifier/1.0"), timeout(30))
    
    if (status_code(response) == 200) {
      content <- fromJSON(rawToChar(response$content))
      
      if (!is.null(content$message$items) && length(content$message$items) > 0) {
        journals <- content$message$items
        
        results <- data.frame(
          title = character(),
          issn = character(),
          publisher = character(),
          similarity = numeric(),
          stringsAsFactors = FALSE
        )
        
        for (i in 1:min(nrow(journals), max_results)) {
          journal <- journals[i, ]
          
          # 计算相似度
          if (!is.null(journal$title) && length(journal$title) > 0) {
            similarity <- 1 - stringdist(normalize_journal_name_advanced(journal_name), 
                                        normalize_journal_name_advanced(journal$title), 
                                        method = "jw")
            
            issn_info <- if (!is.null(journal$ISSN) && length(journal$ISSN) > 0) {
              paste(journal$ISSN, collapse = ", ")
            } else {
              "未知"
            }
            
            publisher_info <- if (!is.null(journal$publisher)) {
              journal$publisher
            } else {
              "未知"
            }
            
            results <- rbind(results, data.frame(
              title = journal$title,
              issn = issn_info,
              publisher = publisher_info,
              similarity = similarity,
              stringsAsFactors = FALSE
            ))
          }
        }
        
        # 按相似度排序
        results <- results[order(results$similarity, decreasing = TRUE), ]
        return(results)
      }
    }
    
    return(NULL)
  }, error = function(e) {
    cat(sprintf("外部搜索错误: %s\n", e$message))
    return(NULL)
  })
}

# 创建期刊验证报告的函数
create_journal_verification_report <- function(verification_file, output_dir) {
  cat("正在创建期刊验证报告...\n")
  
  # 读取核查表格
  if (!file.exists(verification_file)) {
    stop(sprintf("核查文件不存在: %s", verification_file))
  }
  
  verification_data <- read.csv(verification_file, stringsAsFactors = FALSE)
  
  # 筛选匹配记录B的行
  matched_records <- verification_data[verification_data$记录类型 == "匹配记录(B)", ]
  
  # 创建期刊验证报告
  journal_report <- data.frame(
    记录编号 = character(),
    原始期刊 = character(),
    匹配期刊 = character(),
    期刊匹配度 = numeric(),
    是否同一期刊 = character(),
    置信度 = numeric(),
    验证原因 = character(),
    外部搜索结果 = character(),
    建议 = character(),
    stringsAsFactors = FALSE
  )
  
  cat(sprintf("开始验证 %d 条期刊记录...\n", nrow(matched_records)))
  
  for (i in 1:nrow(matched_records)) {
    if (i %% 10 == 0) cat(sprintf("处理第 %d/%d 条记录\n", i, nrow(matched_records)))
    
    record_num <- matched_records$记录编号[i]
    
    # 找到对应的原始记录
    original_record <- verification_data[verification_data$记录编号 == record_num & 
                                       verification_data$记录类型 == "原始记录(A)", ]
    
    if (nrow(original_record) > 0) {
      original_journal <- original_record$期刊[1]
      matched_journal <- matched_records$期刊[i]
      journal_similarity <- as.numeric(matched_records$期刊匹配度[i])
      
      # 检查期刊等价性
      equivalence <- check_journal_equivalence(original_journal, matched_journal)
      
      # 外部搜索（仅对低匹配度的记录）
      external_search <- ""
      if (journal_similarity < 0.7 && !is.na(journal_similarity)) {
        search_results <- search_journal_external(original_journal, max_results = 2)
        if (!is.null(search_results) && nrow(search_results) > 0) {
          external_search <- paste(
            sprintf("%s (相似度:%.3f)", search_results$title[1:min(2, nrow(search_results))], 
                   search_results$similarity[1:min(2, nrow(search_results))]), 
            collapse = "; "
          )
        } else {
          external_search <- "未找到相关期刊"
        }
        
        # API限制延时
        Sys.sleep(2)
      }
      
      # 生成建议
      suggestion <- ""
      if (equivalence$is_same == TRUE) {
        suggestion <- "接受DOI补全"
      } else if (equivalence$is_same == "可能") {
        suggestion <- "需要人工确认"
      } else {
        suggestion <- "建议拒绝，期刊不匹配"
      }
      
      # 添加到报告
      journal_report <- rbind(journal_report, data.frame(
        记录编号 = record_num,
        原始期刊 = original_journal,
        匹配期刊 = matched_journal,
        期刊匹配度 = journal_similarity,
        是否同一期刊 = as.character(equivalence$is_same),
        置信度 = equivalence$confidence,
        验证原因 = equivalence$reason,
        外部搜索结果 = external_search,
        建议 = suggestion,
        stringsAsFactors = FALSE
      ))
    }
  }
  
  # 保存期刊验证报告
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  
  # CSV文件
  csv_file <- file.path(output_dir, "journal_verification_report.csv")
  write.csv(journal_report, csv_file, row.names = FALSE)
  
  # Excel文件
  if (require(openxlsx, quietly = TRUE)) {
    excel_file <- file.path(output_dir, "journal_verification_report.xlsx")
    
    wb <- createWorkbook()
    addWorksheet(wb, "期刊验证报告")
    
    # 写入数据
    writeData(wb, "期刊验证报告", journal_report)
    
    # 设置列宽
    setColWidths(wb, "期刊验证报告", cols = 1:ncol(journal_report), widths = "auto")
    
    # 设置标题行样式
    header_style <- createStyle(textDecoration = "bold", fgFill = "#4472C4", fontColour = "white")
    addStyle(wb, "期刊验证报告", header_style, rows = 1, cols = 1:ncol(journal_report), gridExpand = TRUE)
    
    # 设置条件格式
    for (i in 2:(nrow(journal_report) + 1)) {
      row_data <- journal_report[i-1, ]
      
      if (row_data$是否同一期刊 == "TRUE") {
        # 同一期刊 - 绿色背景
        same_style <- createStyle(fgFill = "#E8F5E8")
        addStyle(wb, "期刊验证报告", same_style, rows = i, cols = 1:ncol(journal_report), gridExpand = TRUE)
      } else if (row_data$是否同一期刊 == "可能") {
        # 可能同一期刊 - 黄色背景
        maybe_style <- createStyle(fgFill = "#FFF2CC")
        addStyle(wb, "期刊验证报告", maybe_style, rows = i, cols = 1:ncol(journal_report), gridExpand = TRUE)
      } else {
        # 不同期刊 - 红色背景
        different_style <- createStyle(fgFill = "#FFE4E1")
        addStyle(wb, "期刊验证报告", different_style, rows = i, cols = 1:ncol(journal_report), gridExpand = TRUE)
      }
    }
    
    saveWorkbook(wb, excel_file, overwrite = TRUE)
    cat(sprintf("Excel期刊验证报告已保存: %s\n", excel_file))
  }
  
  cat(sprintf("CSV期刊验证报告已保存: %s\n", csv_file))
  
  # 统计报告
  cat("\n=== 期刊验证统计 ===\n")
  same_count <- sum(journal_report$是否同一期刊 == "TRUE", na.rm = TRUE)
  maybe_count <- sum(journal_report$是否同一期刊 == "可能", na.rm = TRUE)
  different_count <- sum(journal_report$是否同一期刊 == "FALSE", na.rm = TRUE)
  
  cat(sprintf("总记录数: %d\n", nrow(journal_report)))
  cat(sprintf("确认同一期刊: %d (%.1f%%)\n", same_count, 100 * same_count / nrow(journal_report)))
  cat(sprintf("可能同一期刊: %d (%.1f%%)\n", maybe_count, 100 * maybe_count / nrow(journal_report)))
  cat(sprintf("不同期刊: %d (%.1f%%)\n", different_count, 100 * different_count / nrow(journal_report)))
  
  return(journal_report)
}

# 主执行函数
main_journal_verification <- function() {
  verification_file <- "data_repository/04_enhancement_reports/doi_verification_table.csv"
  output_dir <- "data_repository/04_enhancement_reports"
  
  if (file.exists(verification_file)) {
    journal_report <- create_journal_verification_report(verification_file, output_dir)
    return(journal_report)
  } else {
    cat("未找到DOI核查表格文件，请先运行doi_verification_interface.R\n")
    return(NULL)
  }
}

# 执行期刊验证
if (!interactive()) {
  journal_report <- main_journal_verification()
}

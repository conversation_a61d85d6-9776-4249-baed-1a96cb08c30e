# PubMed MeSH优化系统
# 重点提升DOI覆盖率和MeSH分类准确度

library(httr)
library(jsonlite)
library(stringdist)
library(xml2)

# 加载核心函数
source("doi_completion_core.R")

cat("=== PubMed MeSH优化系统 ===\n")
cat("🎯 目标: 提升DOI覆盖率和MeSH分类准确度\n")
cat("📊 策略: 扩大搜索范围 + 优化DOI提取 + 增强MeSH分析\n\n")

# === 优化的MeSH Publication Types映射 ===
enhanced_mesh_types <- list(
  # 研究设计类型 (高价值)
  "Randomized Controlled Trial" = list(cn = "随机对照试验", value = 10, evidence_level = "A"),
  "Controlled Clinical Trial" = list(cn = "对照临床试验", value = 9, evidence_level = "A"),
  "Clinical Trial" = list(cn = "临床试验", value = 8, evidence_level = "B"),
  "Meta-Analysis" = list(cn = "荟萃分析", value = 10, evidence_level = "A"),
  "Systematic Review" = list(cn = "系统综述", value = 9, evidence_level = "A"),
  "Multicenter Study" = list(cn = "多中心研究", value = 8, evidence_level = "B"),
  
  # 观察性研究
  "Cohort Studies" = list(cn = "队列研究", value = 7, evidence_level = "B"),
  "Case-Control Studies" = list(cn = "病例对照研究", value = 6, evidence_level = "B"),
  "Cross-Sectional Studies" = list(cn = "横断面研究", value = 5, evidence_level = "C"),
  "Observational Study" = list(cn = "观察性研究", value = 6, evidence_level = "B"),
  
  # 描述性研究
  "Case Reports" = list(cn = "病例报告", value = 3, evidence_level = "D"),
  "Case Series" = list(cn = "病例系列", value = 4, evidence_level = "D"),
  
  # 文献类型
  "Review" = list(cn = "综述", value = 6, evidence_level = "C"),
  "Journal Article" = list(cn = "期刊文章", value = 5, evidence_level = "C"),
  "Editorial" = list(cn = "社论", value = 2, evidence_level = "E"),
  "Letter" = list(cn = "信件", value = 2, evidence_level = "E"),
  "Comment" = list(cn = "评论", value = 2, evidence_level = "E"),
  
  # 特殊研究类型
  "Comparative Study" = list(cn = "比较研究", value = 7, evidence_level = "B"),
  "Evaluation Studies" = list(cn = "评估研究", value = 6, evidence_level = "C"),
  "Validation Studies" = list(cn = "验证研究", value = 6, evidence_level = "C"),
  "Twin Study" = list(cn = "双胞胎研究", value = 7, evidence_level = "B"),
  "Animal Studies" = list(cn = "动物研究", value = 4, evidence_level = "D")
)

# === 优化的PubMed DOI搜索函数 ===
search_doi_pubmed_optimized <- function(title, authors, year, journal, expand_search = TRUE) {
  tryCatch({
    # 构建多层次查询策略
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    
    # 策略1: 精确查询 (前4个关键词)
    keywords_precise <- title_words[nchar(title_words) > 3][1:min(4, length(title_words[nchar(title_words) > 3]))]
    
    # 策略2: 扩展查询 (前6个关键词，包含较短词)
    keywords_expanded <- title_words[nchar(title_words) > 2][1:min(6, length(title_words[nchar(title_words) > 2]))]
    
    # 策略3: 宽松查询 (前3个最重要关键词)
    keywords_loose <- title_words[nchar(title_words) > 4][1:min(3, length(title_words[nchar(title_words) > 4]))]
    
    search_strategies <- list()
    if (length(keywords_precise) >= 3) {
      search_strategies[["precise"]] <- paste(keywords_precise, collapse = " AND ")
    }
    if (expand_search && length(keywords_expanded) >= 3) {
      search_strategies[["expanded"]] <- paste(keywords_expanded, collapse = " AND ")
    }
    if (expand_search && length(keywords_loose) >= 2) {
      search_strategies[["loose"]] <- paste(keywords_loose, collapse = " AND ")
    }
    
    if (length(search_strategies) == 0) return(NULL)
    
    best_result <- NULL
    best_score <- 0
    
    # 尝试不同的搜索策略
    for (strategy_name in names(search_strategies)) {
      search_terms <- search_strategies[[strategy_name]]
      
      cat(sprintf("PubMed优化搜索 (%s): %s\n", strategy_name, search_terms))
      
      # Step 1: ESearch
      esearch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&term=%s&retmax=20&retmode=json",
                            URLencode(search_terms))
      
      search_response <- GET(esearch_url, 
                            user_agent("DOI_Completion_PubMed_Optimized/1.0"),
                            timeout(30))
      
      if (status_code(search_response) != 200) {
        cat(sprintf("  %s策略API失败\n", strategy_name))
        next
      }
      
      search_content <- fromJSON(rawToChar(search_response$content))
      
      if (is.null(search_content$esearchresult$idlist) || 
          length(search_content$esearchresult$idlist) == 0) {
        cat(sprintf("  %s策略无结果\n", strategy_name))
        next
      }
      
      pmids <- search_content$esearchresult$idlist
      cat(sprintf("  %s策略返回 %d 个PMID\n", strategy_name, length(pmids)))
      
      # Step 2: EFetch - 获取详细信息
      pmid_list <- paste(pmids[1:min(10, length(pmids))], collapse = ",")
      efetch_url <- sprintf("https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=%s&retmode=xml",
                           pmid_list)
      
      fetch_response <- GET(efetch_url,
                           user_agent("DOI_Completion_PubMed_Optimized/1.0"),
                           timeout(30))
      
      if (status_code(fetch_response) != 200) {
        cat(sprintf("  %s策略EFetch失败\n", strategy_name))
        next
      }
      
      # 解析XML
      xml_content <- read_xml(rawToChar(fetch_response$content))
      articles <- xml_find_all(xml_content, "//PubmedArticle")
      
      if (length(articles) == 0) {
        cat(sprintf("  %s策略XML解析失败\n", strategy_name))
        next
      }
      
      # 评估每篇文章
      for (i in 1:min(10, length(articles))) {
        article <- articles[[i]]
        
        # 提取基本信息
        pmid_node <- xml_find_first(article, ".//PMID")
        candidate_pmid <- if (!is.null(pmid_node)) xml_text(pmid_node) else ""
        
        title_node <- xml_find_first(article, ".//ArticleTitle")
        candidate_title <- if (!is.null(title_node)) xml_text(title_node) else ""
        
        # 提取期刊信息 (多种方式)
        journal_nodes <- xml_find_all(article, ".//Journal/Title | .//Journal/ISOAbbreviation | .//MedlineJournalInfo/MedlineTA")
        candidate_journal <- ""
        if (length(journal_nodes) > 0) {
          candidate_journal <- xml_text(journal_nodes[[1]])
        }
        
        # 提取年份 (多种方式)
        year_nodes <- xml_find_all(article, ".//PubDate/Year | .//DateCompleted/Year | .//DateRevised/Year")
        candidate_year <- ""
        if (length(year_nodes) > 0) {
          candidate_year <- xml_text(year_nodes[[1]])
        }
        
        # 优化的DOI提取 (多种ID类型)
        doi_nodes <- xml_find_all(article, ".//ArticleId[@IdType='doi'] | .//ELocationID[@EIdType='doi']")
        candidate_doi <- ""
        if (length(doi_nodes) > 0) {
          candidate_doi <- xml_text(doi_nodes[[1]])
          # 清理DOI格式
          candidate_doi <- gsub("^https?://doi.org/", "", candidate_doi)
          candidate_doi <- gsub("^doi:", "", candidate_doi)
        }
        
        # 如果没有DOI，尝试其他标识符
        if (candidate_doi == "") {
          pmc_nodes <- xml_find_all(article, ".//ArticleId[@IdType='pmc']")
          if (length(pmc_nodes) > 0) {
            pmc_id <- xml_text(pmc_nodes[[1]])
            # PMC可以转换为DOI的情况
            if (grepl("^PMC\\d+", pmc_id)) {
              candidate_doi <- paste0("PMC:", pmc_id)  # 标记为PMC标识符
            }
          }
        }
        
        # 如果仍然没有标识符，跳过
        if (candidate_doi == "" || is.na(candidate_doi)) {
          next
        }
        
        # === 增强的MeSH信息提取 ===
        pub_type_nodes <- xml_find_all(article, ".//PublicationType")
        mesh_pub_types <- c()
        mesh_pub_types_cn <- c()
        mesh_values <- c()
        evidence_levels <- c()
        
        if (length(pub_type_nodes) > 0) {
          for (pt_node in pub_type_nodes) {
            pub_type <- xml_text(pt_node)
            mesh_pub_types <- c(mesh_pub_types, pub_type)
            
            # 增强的映射
            if (pub_type %in% names(enhanced_mesh_types)) {
              mesh_info <- enhanced_mesh_types[[pub_type]]
              mesh_pub_types_cn <- c(mesh_pub_types_cn, mesh_info$cn)
              mesh_values <- c(mesh_values, mesh_info$value)
              evidence_levels <- c(evidence_levels, mesh_info$evidence_level)
            } else {
              mesh_pub_types_cn <- c(mesh_pub_types_cn, pub_type)
              mesh_values <- c(mesh_values, 3)  # 默认值
              evidence_levels <- c(evidence_levels, "C")
            }
          }
        }
        
        # MeSH主题词
        mesh_heading_nodes <- xml_find_all(article, ".//MeshHeading/DescriptorName")
        mesh_headings <- c()
        if (length(mesh_heading_nodes) > 0) {
          mesh_headings <- sapply(mesh_heading_nodes, xml_text)
        }
        
        # 关键词
        keyword_nodes <- xml_find_all(article, ".//Keyword")
        keywords_list <- c()
        if (length(keyword_nodes) > 0) {
          keywords_list <- sapply(keyword_nodes, xml_text)
        }
        
        # 计算相似度
        title_sim <- calculate_title_similarity(title, candidate_title)
        journal_sim <- calculate_journal_similarity(journal, candidate_journal)
        year_sim <- calculate_year_similarity(year, candidate_year)
        
        # 增强的生物医学相关性 (基于MeSH)
        bio_rel <- 0.5
        if (length(mesh_headings) > 0 || length(mesh_pub_types) > 0) {
          bio_rel <- 1.0
          # 基于MeSH类型的质量加权
          if (length(mesh_values) > 0) {
            max_mesh_value <- max(mesh_values)
            bio_rel <- min(1.0, bio_rel + (max_mesh_value / 20))  # 最高质量类型额外加分
          }
        }
        
        # 策略权重调整
        strategy_weight <- switch(strategy_name,
                                "precise" = 1.0,
                                "expanded" = 0.9,
                                "loose" = 0.8)
        
        # 综合评分
        final_score <- ((title_sim * 0.6) + (journal_sim * 0.2) + 
                       (year_sim * 0.1) + (bio_rel * 0.1)) * strategy_weight
        
        cat(sprintf("  PMID %s (%s): T=%.3f, J=%.3f, Y=%.3f, B=%.3f, 总分=%.3f\n",
                    candidate_pmid, strategy_name, title_sim, journal_sim, year_sim, bio_rel, final_score))
        
        # 优化的接受条件
        min_title_sim <- switch(strategy_name, "precise" = 0.65, "expanded" = 0.60, "loose" = 0.55)
        min_final_score <- switch(strategy_name, "precise" = 0.55, "expanded" = 0.50, "loose" = 0.45)
        
        if (title_sim >= min_title_sim &&
            journal_sim >= 0.20 &&
            year_sim >= 0.10 &&
            final_score >= min_final_score &&
            final_score > best_score) {
          
          best_score <- final_score
          best_result <- list(
            doi = candidate_doi,
            title = candidate_title,
            journal = candidate_journal,
            year = candidate_year,
            pmid = candidate_pmid,
            mesh_publication_types = mesh_pub_types,
            mesh_publication_types_cn = mesh_pub_types_cn,
            mesh_values = mesh_values,
            evidence_levels = evidence_levels,
            mesh_headings = mesh_headings[1:min(10, length(mesh_headings))],
            keywords = keywords_list[1:min(10, length(keywords_list))],
            title_similarity = title_sim,
            journal_similarity = journal_sim,
            year_similarity = year_sim,
            biomedical_relevance = bio_rel,
            final_score = final_score,
            search_strategy = strategy_name,
            source = "pubmed_optimized"
          )
          
          cat(sprintf("    ✅ 新的最佳匹配! DOI: %s (策略: %s)\n", candidate_doi, strategy_name))
        }
      }
      
      # 策略间休息
      if (strategy_name != names(search_strategies)[length(search_strategies)]) {
        Sys.sleep(2)
      }
    }
    
    return(best_result)
    
  }, error = function(e) {
    cat("PubMed优化API错误:", e$message, "\n")
    return(NULL)
  })
}

# === MeSH质量分析函数 ===
analyze_mesh_quality <- function(mesh_result) {
  if (is.null(mesh_result) || is.null(mesh_result$mesh_publication_types)) {
    return(list(quality_score = 0, evidence_level = "E", description = "无MeSH信息"))
  }
  
  mesh_types <- mesh_result$mesh_publication_types
  mesh_values <- mesh_result$mesh_values
  evidence_levels <- mesh_result$evidence_levels
  
  if (length(mesh_values) == 0) {
    return(list(quality_score = 3, evidence_level = "C", description = "基本MeSH信息"))
  }
  
  # 计算质量评分
  max_value <- max(mesh_values)
  avg_value <- mean(mesh_values)
  quality_score <- round((max_value + avg_value) / 2, 1)
  
  # 确定证据级别
  best_evidence <- evidence_levels[which.max(mesh_values)]
  
  # 生成描述
  high_value_types <- mesh_types[mesh_values >= 8]
  if (length(high_value_types) > 0) {
    description <- paste("高质量研究:", paste(high_value_types, collapse = ", "))
  } else {
    description <- paste("标准研究:", paste(mesh_types[1:min(3, length(mesh_types))], collapse = ", "))
  }
  
  return(list(
    quality_score = quality_score,
    evidence_level = best_evidence,
    description = description,
    high_value_types = high_value_types
  ))
}

# === 测试优化系统 ===
test_pubmed_optimized <- function() {
  cat("\n=== PubMed优化系统测试 ===\n")
  
  # 更具体的生物医学测试用例
  test_cases <- list(
    list(
      title = "Efficacy and safety of COVID-19 vaccines systematic review",
      authors = "Smith J",
      year = 2021,
      journal = "The Lancet",
      description = "COVID疫苗系统综述"
    ),
    list(
      title = "Randomized trial aspirin cardiovascular disease prevention",
      authors = "Johnson A",
      year = 2020,
      journal = "New England Journal of Medicine",
      description = "阿司匹林心血管预防RCT"
    ),
    list(
      title = "Meta-analysis cancer immunotherapy clinical trials",
      authors = "Brown C",
      year = 2019,
      journal = "Nature Medicine",
      description = "癌症免疫疗法荟萃分析"
    )
  )
  
  success_count <- 0
  mesh_extraction_count <- 0
  high_quality_count <- 0
  
  for (i in 1:length(test_cases)) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n--- 测试案例 %d: %s ---\n", i, test_case$description))
    cat(sprintf("标题: %s\n", test_case$title))
    
    result <- search_doi_pubmed_optimized(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal,
      expand_search = TRUE
    )
    
    if (!is.null(result)) {
      success_count <- success_count + 1
      mesh_extraction_count <- mesh_extraction_count + 1
      
      quality_analysis <- analyze_mesh_quality(result)
      
      if (quality_analysis$quality_score >= 7) {
        high_quality_count <- high_quality_count + 1
      }
      
      cat(sprintf("✅ PubMed优化成功: %s (PMID: %s)\n", result$doi, result$pmid))
      cat(sprintf("📋 MeSH类型: %s\n", paste(result$mesh_publication_types_cn, collapse = ", ")))
      cat(sprintf("⭐ 质量评分: %.1f | 证据级别: %s\n", 
                  quality_analysis$quality_score, quality_analysis$evidence_level))
      cat(sprintf("📝 描述: %s\n", quality_analysis$description))
      cat(sprintf("🔍 搜索策略: %s\n", result$search_strategy))
      
    } else {
      cat("❌ PubMed优化未找到匹配\n")
    }
    
    if (i < length(test_cases)) {
      cat("等待3秒...\n")
      Sys.sleep(3)
    }
  }
  
  success_rate <- 100 * success_count / length(test_cases)
  mesh_rate <- 100 * mesh_extraction_count / length(test_cases)
  quality_rate <- 100 * high_quality_count / length(test_cases)
  
  cat(sprintf("\n📊 PubMed优化系统结果:\n"))
  cat(sprintf("DOI补全成功率: %.1f%% (%d/%d)\n", success_rate, success_count, length(test_cases)))
  cat(sprintf("MeSH提取成功率: %.1f%% (%d/%d)\n", mesh_rate, mesh_extraction_count, length(test_cases)))
  cat(sprintf("高质量MeSH率: %.1f%% (%d/%d)\n", quality_rate, high_quality_count, length(test_cases)))
  
  return(list(
    success_count = success_count,
    success_rate = success_rate,
    mesh_rate = mesh_rate,
    quality_rate = quality_rate
  ))
}

cat("✅ PubMed MeSH优化系统已加载\n")
cat("📋 主要函数:\n")
cat("  - search_doi_pubmed_optimized()  : 优化PubMed搜索\n")
cat("  - analyze_mesh_quality()         : MeSH质量分析\n")
cat("  - test_pubmed_optimized()        : 优化系统测试\n")

# 自动执行优化测试
cat("\n🚀 开始PubMed优化系统测试...\n")
pubmed_optimized_result <- test_pubmed_optimized()

cat(sprintf("\n🎯 PubMed优化测试完成!\n"))
cat(sprintf("DOI补全成功率: %.1f%%\n", pubmed_optimized_result$success_rate))
cat(sprintf("MeSH提取成功率: %.1f%%\n", pubmed_optimized_result$mesh_rate))
cat(sprintf("高质量MeSH率: %.1f%%\n", pubmed_optimized_result$quality_rate))

if (pubmed_optimized_result$success_rate >= 33.3) {
  cat("🎉 PubMed优化显著改善！DOI覆盖率和MeSH质量都有提升。\n")
} else if (pubmed_optimized_result$mesh_rate >= 50) {
  cat("📈 MeSH提取能力良好，DOI覆盖率仍需改进。\n")
} else {
  cat("⚠️  需要进一步优化搜索策略和DOI提取方法。\n")
}

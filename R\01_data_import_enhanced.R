# 01_data_import_enhanced.R
# 实现更优的WoS数据转换方案
# 保持与bibliometrix相同的字段映射和格式标准

# 设置工作环境
options(repos = c(CRAN = "https://cloud.r-project.org"))

# 加载必要的包
required_packages <- c("here", "dplyr", "stringr", "data.table", "bibliometrix")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) install.packages(pkg)
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保bibliometrix包被正确加载
if (!requireNamespace("bibliometrix", quietly = TRUE)) {
  install.packages("bibliometrix")
  library(bibliometrix)
}

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
  if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
    cat(formatted_msg, "\n", file = log_con)
  }
}

# 配置
config <- list(
  paths = list(
    input = here("data_repository", "00_original_raw_files"),
    output = here("data_repository", "02_enhanced_dataset"),
    logs = here("data_repository", "05_execution_logs", "enhanced_import_logs")
  ),
  files = list(
    output = "enhanced_data_initial.rds",
    log = "enhanced_import.log"
  )
)

# 创建必要的目录
for (path in config$paths) {
  if (!dir.exists(path)) dir.create(path, recursive = TRUE)
}

# 设置日志文件
log_file <- file.path(config$paths$logs, config$files$log)
log_con <- file(log_file, "w")
if (!isOpen(log_con)) {
  stop("无法打开日志文件")
}

# 记录开始时间
start_time <- Sys.time()
log_message("开始增强数据导入流程")

# 获取所有输入文件
input_files <- list.files(config$paths$input, pattern = "\\.txt$", full.names = TRUE)
if (length(input_files) == 0) {
  stop("未找到输入文件")
}
log_message(sprintf("找到%d个输入文件", length(input_files)))

# 执行数据转换
log_message("开始数据转换流程...")

# 读取并合并所有原始数据
log_message("正在读取原始数据...")
raw_data <- character()
for (file in input_files) {
  log_message(sprintf("读取文件: %s", basename(file)))
  # 使用readLines时添加warn=FALSE来抑制警告
  # 使用file()函数打开文件，确保完整读取
  con <- file(file, "r", encoding = "UTF-8")
  file_data <- readLines(con, warn = FALSE)
  close(con)
  raw_data <- c(raw_data, file_data)
  log_message(sprintf("文件 %s 读取完成，共 %d 行", basename(file), length(file_data)))
}

# 解析WoS记录
parse_wos_records <- function(raw_data) {
  log_message("开始解析WoS记录...")
  
  # 使用data.table提高性能
  records <- data.table()
  
  # 查找记录分隔符
  record_starts <- which(grepl("^ER", raw_data))
  record_ends <- which(grepl("^EF", raw_data))
  
  if (length(record_starts) != length(record_ends)) {
    stop("记录分隔符不匹配")
  }
  
  # 批处理大小
  batch_size <- 1000
  n_batches <- ceiling(length(record_starts) / batch_size)
  
  for (batch in 1:n_batches) {
    start_idx <- (batch - 1) * batch_size + 1
    end_idx <- min(batch * batch_size, length(record_starts))
    
    batch_records <- lapply(start_idx:end_idx, function(i) {
      record_start <- ifelse(i == 1, 1, record_starts[i-1] + 1)
      record_end <- record_starts[i] - 1
      record_lines <- raw_data[record_start:record_end]
      
      # 解析字段
      fields <- list()
      current_field <- NULL
      current_value <- character()
      
      for (line in record_lines) {
        if (grepl("^[A-Z]{2}\\s", line)) {
          # 新字段开始
          if (!is.null(current_field)) {
            fields[[current_field]] <- paste(current_value, collapse = " ")
          }
          current_field <- substr(line, 1, 2)
          current_value <- substr(line, 4, nchar(line))
        } else if (!is.null(current_field)) {
          # 继续当前字段
          current_value <- c(current_value, line)
        }
      }
      
      # 添加最后一个字段
      if (!is.null(current_field)) {
        fields[[current_field]] <- paste(current_value, collapse = " ")
      }
      
      return(fields)
    })
    
    # 转换为data.table并添加到结果中
    batch_dt <- rbindlist(batch_records, fill = TRUE)
    records <- rbind(records, batch_dt)
    
    log_message(sprintf("完成第%d批处理，共%d条记录", batch, nrow(records)))
  }
  
  return(records)
}

# 标准化处理函数
postprocessing <- function(DATA) {
  log_message("开始标准化处理...")
  
  # 作者名称处理
  DATA$AU <- gsub("\\s+", " ", DATA$AU)
  DATA$AF <- gsub("\\.|,", "", DATA$AU)
  
  listAU <- strsplit(DATA$AU, ";")
  AU <- lapply(listAU, function(l) {
    lastname <- trim(gsub(",.*", "", l))
    firstname <- strsplit(trim(gsub(".*,", "", l)), " ")
    firstname <- gsub("[^:A-Z:]", "", firstname)
    AU <- paste(lastname, unlist(firstname), sep = " ", collapse = ";")
    return(AU)
  })
  DATA$AU <- unlist(AU)
  
  # 引用次数处理
  if ("TC" %in% names(DATA)) {
    DATA$TC <- as.numeric(sub("\\D*(\\d+).*", "\\1", DATA$TC))
  }
  
  # 参考文献处理
  if ("CR" %in% names(DATA)) {
    DATA$CR <- gsub("\\.;", ";", DATA$CR)
    DATA$CR <- substr(DATA$CR, 1, (nchar(DATA$CR) - 1))
  }
  
  # 年份处理
  if ("PY" %in% names(DATA)) {
    DATA$PY <- as.numeric(sub("\\D*(\\d+).*", "\\1", DATA$PY))
  }
  
  # 唯一标识符处理
  if ("UT" %in% names(DATA)) {
    DATA$UT <- gsub(":", "", DATA$UT, fixed = TRUE)
  }
  
  # 通讯作者处理
  if (!("RP" %in% names(DATA)) && ("C1" %in% names(DATA))) {
    DATA$RP <- unlist(lapply(strsplit(DATA$C1, "\\."), function(l) l[1]))
  }
  
  # 关键词处理
  if ("ID" %in% names(DATA)) {
    DATA$ID <- gsub("   |,", ";", DATA$ID)
  }
  if ("DE" %in% names(DATA)) {
    DATA$DE <- gsub("   |,", ";", DATA$DE)
  }
  
  # 期刊名称处理
  if ("SO" %in% names(DATA) && "BO" %in% names(DATA)) {
    ind <- which(is.na(DATA$SO))
    DATA$SO[ind] <- DATA$BO[ind]
  }
  
  # 页码处理
  if ("PN" %in% names(DATA)) {
    DATA$PN <- as.numeric(gsub("[^0-9]", "", DATA$PN))
  }
  
  # 数据库标识
  DATA$DB <- "ISI"
  
  # 转换为大写
  DI <- DATA$DI
  URL <- DATA$url
  DATA <- data.frame(lapply(DATA, toupper), stringsAsFactors = FALSE)
  
  # 期刊缩写处理
  if ("JI" %in% names(DATA)) {
    DATA$J9 <- gsub("\\.", "", DATA$JI)
  } else {
    DATA$J9 <- DATA$JI <- sapply(DATA$SO, AbbrevTitle, USE.NAMES = FALSE)
  }
  
  DATA$DI <- DI
  DATA$url <- URL
  
  return(DATA)
}

# 创建修改版本的convert2df函数
convert2df_modified <- function(file, dbsource = "wos", format = "plaintext", remove.duplicates = TRUE) {
  
  allowed_formats <- c('api', 'bibtex', 'csv', 'endnote','excel','plaintext', 'pubmed') 
  allowed_db <- c('cochrane','dimensions','generic','isi','openalex', 'openalex_api','pubmed','scopus','wos', 'lens')
  
  cat("\nConverting your",dbsource,"collection into a bibliographic dataframe\n\n")
  if (length(setdiff(dbsource,allowed_db))>0){
    cat("\n 'dbsource' argument is not properly specified")
    cat("\n 'dbsource' argument has to be a character string matching one among:",allowed_db, "\n")
  }
  if (length(setdiff(format,allowed_formats))>0){
    cat("\n 'format' argument is not properly specified")
    cat("\n 'format' argument has to be a character string matching one among:",allowed_formats,"\n")
  }

  if (dbsource=="wos") dbsource <- "isi"
  if (format=="endnote") format <- "plaintext"
  if (format == "lens") format <- "csv"
  
  # 使用原始的convert2df函数处理数据
  M <- convert2df(file, dbsource, format, remove.duplicates = FALSE)
  
  # 手动处理重复记录（不删除，只输出）
  if (isTRUE(remove.duplicates)) {
    switch(dbsource,
           isi={
             id_field <- "UT"
           },
           scopus={
             if (format=="csv"){
               id_field <- "UT"
             } else {
               id_field <- "TI"
             }
           },
           openalex={
             id_field <- "id_oa"
           },
           openalex_api={
             id_field <- "id_oa"
           },
           dimneisons={
             id_field <- "UT"
           },
           pubmed={
             id_field <- "PMID"
           },
           lens={
             id_field <- "UT"
           },
           {
             id_field <- "TI"
           })
    
    d <- duplicated(M[[id_field]]) 
    if (sum(d) > 0) {
      cat("\n发现 ", sum(d), " 条重复文档\n")
      
      # 找出所有重复的ID
      duplicate_ids <- M[[id_field]][d]
      # 找出所有具有这些ID的记录（包括第一条）
      all_duplicates <- M[M[[id_field]] %in% duplicate_ids, ]
      
      # 保存重复记录
      duplicates_file <- file.path(config$paths$output, sprintf("duplicates_%s.rds", tolower(id_field)))
      saveRDS(all_duplicates, duplicates_file)
      cat(sprintf("重复记录已保存到: %s\n", duplicates_file))
      
      # 输出重复记录详情
      cat("\n重复记录详细信息：\n")
      tryCatch({
        if ("UT" %in% names(all_duplicates) && "TI" %in% names(all_duplicates) && "PY" %in% names(all_duplicates)) {
          print(all_duplicates %>% select(UT, TI, PY, all_of(id_field)) %>% arrange(.data[[id_field]]))
        } else {
          # 如果缺少某些字段，显示可用的字段
          available_fields <- intersect(c("UT", "TI", "PY", id_field), names(all_duplicates))
          if (length(available_fields) > 0) {
            print(all_duplicates %>% select(all_of(available_fields)) %>% arrange(.data[[id_field]]))
          } else {
            print(head(all_duplicates, 10))  # 显示前10行
          }
        }
      }, error = function(e) {
        cat("无法显示重复记录详情，错误:", e$message, "\n")
        cat("重复记录已保存到文件中\n")
      })
    } else {
      cat("\n未发现重复文档\n")
    }
    # 注意：这里不删除重复记录，保持原始数据
  }
  
  return(M)
}

# 执行数据转换
log_message("开始数据转换流程...")

# 使用修改后的convert2df函数
M <- convert2df_modified(input_files, dbsource = "isi", format = "plaintext", remove.duplicates = TRUE)

# 保存结果
output_file <- file.path(config$paths$output, config$files$output)
saveRDS(M, file = output_file)

# 记录结束时间
end_time <- Sys.time()
log_message(sprintf("数据转换完成，总耗时: %.2f 分钟", 
                   as.numeric(difftime(end_time, start_time, units = "mins"))))
log_message(sprintf("处理记录数: %d", nrow(M)))
log_message(sprintf("数据字段数: %d", ncol(M)))

# 关闭日志连接
if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
  close(log_con)
} 
# ============================================================================
# 多API学术文献数据补全系统
# 整合OpenAlex、Crossref和Unpaywall API进行学术文献元数据补全
# 邮箱: <EMAIL>
# ============================================================================

# 加载必要的库
library(httr)
library(jsonlite)
library(dplyr)
library(stringr)
library(purrr)
library(readr)

# =============================================================================
# 通用API工具函数
# =============================================================================

#' API请求函数，支持重试和错误处理
#' @param url API请求地址
#' @param max_retries 最大重试次数
#' @param delay_seconds 重试间隔秒数
#' @return API响应对象或NULL
api_request <- function(url, max_retries = 3, delay_seconds = 1) {
  for (i in 1:max_retries) {
    tryCatch({
      response <- GET(url, timeout(30))
      
      # 检查HTTP状态
      if (status_code(response) == 200) {
        return(response)
      } else if (status_code(response) == 429) {
        # 速率限制，等待更长时间
        wait_time <- delay_seconds * (2^i)
        message(paste0("API速率限制，等待", wait_time, "秒后重试..."))
        Sys.sleep(wait_time)
      } else {
        message(paste0("API请求失败，状态码: ", status_code(response), 
                      ", 尝试", i, "/", max_retries))
        Sys.sleep(delay_seconds)
      }
    }, error = function(e) {
      message(paste0("API请求错误: ", e$message, ", 尝试", i, "/", max_retries))
      Sys.sleep(delay_seconds)
    })
  }
  return(NULL)  # 所有重试都失败
}

#' 对API响应进行日志记录
#' @param source API来源
#' @param query_type 查询类型
#' @param query 查询内容
#' @param success 是否成功
#' @param details 详细信息
log_api_call <- function(source, query_type, query, success, details = "") {
  log_msg <- paste0(
    "[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
    source, " API: ", 
    query_type, " 查询 (", query, "): ",
    if(success) "成功" else "失败",
    if(details != "") paste0(" - ", details) else ""
  )
  message(log_msg)
  return(invisible(log_msg))
}

# =============================================================================
# OpenAlex API 函数
# =============================================================================

#' 通过DOI查询OpenAlex获取详细的学术信息
#' @param doi DOI字符串
#' @param email 用于标识请求的邮箱
#' @return 包含机构、国家和引用的列表
query_openalex <- function(doi, email = "<EMAIL>") {
  if (is.na(doi) || doi == "") return(NULL)
  
  # 清理DOI
  doi <- str_trim(doi)
  
  # 构建API URL
  url <- paste0("https://api.openalex.org/works/https://doi.org/", 
               doi, "?mailto=", email)
  
  # 发送请求
  response <- api_request(url)
  if (is.null(response)) {
    log_api_call("OpenAlex", "DOI", doi, FALSE, "请求失败")
    return(NULL)
  }
  
  # 解析响应
  tryCatch({
    content <- fromJSON(content(response, "text"), flatten = TRUE)
    
    # 检查是否有结果
    if (!is.null(content$error)) {
      log_api_call("OpenAlex", "DOI", doi, FALSE, content$error)
      return(NULL)
    }
    
    # 提取机构和国家信息
    institutions <- list()
    countries <- c()
    
    if (!is.null(content$authorships)) {
      for (authorship in content$authorships) {
        if (!is.null(authorship$institutions) && length(authorship$institutions) > 0) {
          for (inst in authorship$institutions) {
            if (!is.null(inst$display_name)) {
              institutions <- c(institutions, inst$display_name)
            }
            
            if (!is.null(inst$country_code)) {
              countries <- c(countries, inst$country_code)
            }
          }
        }
      }
    }
    
    # 去重
    institutions <- unique(unlist(institutions))
    countries <- unique(countries)
    
    # 提取摘要
    abstract <- if (!is.null(content$abstract_inverted_index)) {
      # OpenAlex返回的是一个特殊的摘要索引格式，需要重建
      try({
        words <- names(content$abstract_inverted_index)
        positions <- content$abstract_inverted_index
        
        # 创建单词位置映射
        word_positions <- list()
        for (i in seq_along(words)) {
          word <- words[i]
          for (pos in positions[[i]]) {
            word_positions[[as.character(pos+1)]] <- word
          }
        }
        
        # 按位置排序并组合
        positions_sorted <- as.numeric(names(word_positions))
        words_sorted <- word_positions[order(positions_sorted)]
        abstract_text <- paste(unlist(words_sorted), collapse = " ")
        abstract_text
      }, silent = TRUE)
    } else {
      NA
    }
    
    # 如果摘要重建失败，返回NA
    if (inherits(abstract, "try-error")) {
      abstract <- NA
    }
    
    # 创建结果列表
    result <- list(
      openalex_id = content$id,
      doi = content$doi,
      title = content$title,
      publication_year = content$publication_year,
      type = content$type,
      open_access = !is.null(content$open_access) && content$open_access$is_oa,
      cited_by_count = content$cited_by_count,
      institutions = if (length(institutions) > 0) paste(institutions, collapse = "; ") else NA,
      countries = if (length(countries) > 0) paste(countries, collapse = "; ") else NA,
      abstract = abstract,
      journal = if (!is.null(content$primary_location$source$display_name)) 
                content$primary_location$source$display_name else NA,
      concepts = if (!is.null(content$concepts) && length(content$concepts) > 0) {
        concept_list <- lapply(content$concepts[1:min(5, length(content$concepts))], function(c) {
          paste0(c$display_name, " (", round(c$score * 100), "%)")
        })
        paste(unlist(concept_list), collapse = "; ")
      } else NA
    )
    
    log_api_call("OpenAlex", "DOI", doi, TRUE)
    return(result)
    
  }, error = function(e) {
    log_api_call("OpenAlex", "DOI", doi, FALSE, e$message)
    return(NULL)
  })
}

# =============================================================================
# Crossref API 函数
# =============================================================================

#' 通过DOI查询Crossref获取引文和基础元数据
#' @param doi DOI字符串
#' @param email 用于标识请求的邮箱
#' @return 包含引文和元数据的列表
query_crossref <- function(doi, email = "<EMAIL>") {
  if (is.na(doi) || doi == "") return(NULL)
  
  # 清理DOI
  doi <- str_trim(doi)
  
  # 构建API URL，添加邮箱提高配额
  url <- paste0("https://api.crossref.org/works/", doi, 
               "?mailto=", email)
  
  # 发送请求
  response <- api_request(url)
  if (is.null(response)) {
    log_api_call("Crossref", "DOI", doi, FALSE, "请求失败")
    return(NULL)
  }
  
  # 解析响应
  tryCatch({
    content <- fromJSON(content(response, "text"), flatten = TRUE)
    
    # 检查是否有结果
    if (content$status != "ok") {
      log_api_call("Crossref", "DOI", doi, FALSE, "状态不为OK")
      return(NULL)
    }
    
    data <- content$message
    
    # 提取作者信息
    authors <- if (!is.null(data$author)) {
      author_names <- sapply(data$author, function(auth) {
        if (!is.null(auth$given) && !is.null(auth$family)) {
          paste(auth$given, auth$family)
        } else if (!is.null(auth$family)) {
          auth$family
        } else {
          NA
        }
      })
      
      # 提取ORCID
      orcids <- sapply(data$author, function(auth) {
        if (!is.null(auth$ORCID)) {
          # 提取ORCID ID部分
          orcid <- auth$ORCID
          if (grepl("^http", orcid)) {
            orcid <- sub(".*/(\\d{4}-\\d{4}-\\d{4}-\\d{3}[0-9X])$", "\\1", orcid)
          }
          return(orcid)
        } else {
          return(NA)
        }
      })
      
      # 将作者与ORCID关联
      data.frame(
        name = author_names,
        orcid = orcids,
        stringsAsFactors = FALSE
      )
    } else {
      data.frame(name = character(0), orcid = character(0))
    }
    
    # 提取资助信息
    funders <- if (!is.null(data$funder)) {
      funder_names <- sapply(data$funder, function(f) f$name)
      funder_dois <- sapply(data$funder, function(f) {
        if (!is.null(f$DOI)) f$DOI else NA
      })
      funder_info <- paste(
        na.omit(paste0(funder_names, " (", funder_dois, ")")),
        collapse = "; "
      )
      if (funder_info == "") NA else funder_info
    } else {
      NA
    }
    
    # 提取引用信息
    citation_count <- if (!is.null(data$`is-referenced-by-count`)) {
      data$`is-referenced-by-count`
    } else {
      NA
    }
    
    # 提取参考文献数量
    references_count <- if (!is.null(data$`references-count`)) {
      data$`references-count`
    } else {
      NA
    }
    
    # 提取ISSN
    issn <- if (!is.null(data$ISSN)) {
      paste(data$ISSN, collapse = "; ")
    } else {
      NA
    }
    
    # 创建结果列表
    result <- list(
      doi = data$DOI,
      title = if (!is.null(data$title)) paste(data$title, collapse = " ") else NA,
      journal = if (!is.null(data$`container-title`)) 
                paste(data$`container-title`, collapse = " ") else NA,
      issn = issn,
      publisher = data$publisher,
      type = data$type,
      authors = authors,
      publication_date = if (!is.null(data$created)) 
                         as.character(data$created$`date-time`) else NA,
      citation_count = citation_count,
      references_count = references_count,
      funder = funders,
      url = if (!is.null(data$URL)) data$URL else NA,
      subject = if (!is.null(data$subject)) paste(data$subject, collapse = "; ") else NA
    )
    
    log_api_call("Crossref", "DOI", doi, TRUE)
    return(result)
    
  }, error = function(e) {
    log_api_call("Crossref", "DOI", doi, FALSE, e$message)
    return(NULL)
  })
}

# =============================================================================
# Unpaywall API 函数
# =============================================================================

#' 通过DOI查询Unpaywall获取开放获取信息
#' @param doi DOI字符串
#' @param email 注册的邮箱
#' @return 包含开放获取信息的列表
query_unpaywall <- function(doi, email = "<EMAIL>") {
  if (is.na(doi) || doi == "") return(NULL)
  
  # 清理DOI
  doi <- str_trim(doi)
  
  # 构建API URL
  url <- paste0("https://api.unpaywall.org/v2/", doi, "?email=", email)
  
  # 发送请求
  response <- api_request(url)
  if (is.null(response)) {
    log_api_call("Unpaywall", "DOI", doi, FALSE, "请求失败")
    return(NULL)
  }
  
  # 解析响应
  tryCatch({
    content <- fromJSON(content(response, "text"), flatten = TRUE)
    
    # 检查是否有结果
    if (!is.null(content$error)) {
      log_api_call("Unpaywall", "DOI", doi, FALSE, content$error)
      return(NULL)
    }
    
    # 提取关键信息
    result <- list(
      doi = content$doi,
      is_oa = content$is_oa,
      oa_status = content$oa_status,
      oa_url = ifelse(!is.null(content$best_oa_location$url), 
                      content$best_oa_location$url, NA),
      journal_name = content$journal_name,
      publisher = content$publisher,
      year = content$year,
      genre = content$genre,
      license = ifelse(!is.null(content$best_oa_location$license), 
                       content$best_oa_location$license, NA)
    )
    
    log_api_call("Unpaywall", "DOI", doi, TRUE)
    return(result)
    
  }, error = function(e) {
    log_api_call("Unpaywall", "DOI", doi, FALSE, e$message)
    return(NULL)
  })
}

# =============================================================================
# 单篇文章综合补全函数
# =============================================================================

#' 综合补全单篇文章的缺失信息
#' @param article 包含文章信息的数据框行或列表
#' @param email 用于API查询的邮箱
#' @return 补全后的文章信息列表
enrich_article_comprehensive <- function(article, email = "<EMAIL>") {
  # 确保DOI存在
  doi <- NULL
  if (!is.null(article$DI) && !is.na(article$DI) && article$DI != "") {
    doi <- article$DI
  } else if (!is.null(article$DOI) && !is.na(article$DOI) && article$DOI != "") {
    doi <- article$DOI
  } else {
    message("文章缺少DOI，无法使用API补全")
    return(article)
  }
  
  # 初始化结果
  result <- as.list(article)
  
  # 1. Crossref查询
  crossref_data <- query_crossref(doi, email)
  if (!is.null(crossref_data)) {
    # 补充基础元数据
    if (is.null(result$TI) || is.na(result$TI) || result$TI == "") 
      result$TI <- crossref_data$title
    
    if (is.null(result$SO) || is.na(result$SO) || result$SO == "") 
      result$SO <- crossref_data$journal
    
    if (is.null(result$PU) || is.na(result$PU) || result$PU == "") 
      result$PU <- crossref_data$publisher
    
    # 补充ISSN
    if (!is.null(crossref_data$issn) && 
        (is.null(result$SN) || is.na(result$SN) || result$SN == "")) 
      result$SN <- crossref_data$issn
    
    # 补充引用信息
    if (!is.null(crossref_data$citation_count) && 
        (is.null(result$Z9) || is.na(result$Z9) || result$Z9 == ""))
      result$Z9 <- crossref_data$citation_count
    
    # 补充类型信息
    if (!is.null(crossref_data$type) && 
        (is.null(result$DT) || is.na(result$DT) || result$DT == ""))
      result$DT <- crossref_data$type
    
    # 补充资助信息
    if (!is.null(crossref_data$funder) && !is.na(crossref_data$funder) && 
        (is.null(result$FU) || is.na(result$FU) || result$FU == ""))
      result$FU <- crossref_data$funder
    
    # 补充URL
    if (!is.null(crossref_data$url) && !is.na(crossref_data$url) && 
        (is.null(result$LK) || is.na(result$LK) || result$LK == ""))
      result$LK <- crossref_data$url
    
    # 添加Crossref专有字段
    result$Crossref_Subject <- crossref_data$subject
    result$Crossref_References_Count <- crossref_data$references_count
    
    # 处理作者ORCID
    if (!is.null(crossref_data$authors) && nrow(crossref_data$authors) > 0) {
      # 如果现有作者字段为空，且Crossref有作者信息
      if (is.null(result$AU) || is.na(result$AU) || result$AU == "") {
        result$AU <- paste(crossref_data$authors$name, collapse = "; ")
      }
      
      # 添加ORCID信息
      valid_orcids <- crossref_data$authors[!is.na(crossref_data$authors$orcid), ]
      if (nrow(valid_orcids) > 0) {
        result$Author_ORCIDs <- paste(
          paste0(valid_orcids$name, " (", valid_orcids$orcid, ")"),
          collapse = "; "
        )
      }
    }
  }
  
  # 2. Unpaywall查询
  unpaywall_data <- query_unpaywall(doi, email)
  if (!is.null(unpaywall_data)) {
    # 补充OA信息
    result$Unpaywall_Is_OA <- unpaywall_data$is_oa
    result$Unpaywall_OA_Status <- unpaywall_data$oa_status
    result$Unpaywall_OA_URL <- unpaywall_data$oa_url
    result$Unpaywall_License <- unpaywall_data$license
    
    # 如果原始数据中缺少，添加期刊和出版商信息
    if (is.null(result$SO) || is.na(result$SO) || result$SO == "") 
      result$SO <- unpaywall_data$journal_name
    
    if (is.null(result$PU) || is.na(result$PU) || result$PU == "") 
      result$PU <- unpaywall_data$publisher
    
    # 补充年份
    if (!is.null(unpaywall_data$year) && !is.na(unpaywall_data$year) &&
        (is.null(result$PY) || is.na(result$PY) || result$PY == ""))
      result$PY <- unpaywall_data$year
  }
  
  # 3. OpenAlex查询
  openalex_data <- query_openalex(doi, email)
  if (!is.null(openalex_data)) {
    # 补充国家信息
    if (!is.null(openalex_data$countries) && !is.na(openalex_data$countries)) {
      result$OpenAlex_Country <- openalex_data$countries
    }
    
    # 补充机构信息
    if (!is.null(openalex_data$institutions) && !is.na(openalex_data$institutions)) {
      result$OpenAlex_Institutions <- openalex_data$institutions
    }
    
    # 补充引用次数
    if (!is.null(openalex_data$cited_by_count) && 
        (is.null(result$Z9) || is.na(result$Z9) || result$Z9 == ""))
      result$Z9 <- openalex_data$cited_by_count
    
    # 补充摘要
    if (!is.null(openalex_data$abstract) && !is.na(openalex_data$abstract) &&
        (is.null(result$AB) || is.na(result$AB) || result$AB == ""))
      result$AB <- openalex_data$abstract
    
    # 补充学科概念
    result$OpenAlex_Concepts <- openalex_data$concepts
    
    # 补充OpenAlex ID
    result$OpenAlex_ID <- openalex_data$openalex_id
    
    # 补充类型信息
    if (!is.null(openalex_data$type) && 
        (is.null(result$DT) || is.na(result$DT) || result$DT == ""))
      result$DT <- openalex_data$type
  }
  
  # 添加处理时间戳
  result$Enrichment_Date <- format(Sys.time(), "%Y-%m-%d")
  
  return(result)
}

# =============================================================================
# 批量文章补全函数
# =============================================================================

#' 批量补全多篇文章的缺失信息
#' @param data 包含多篇文章的数据框
#' @param batch_size 每批处理的文章数
#' @param delay_seconds 批次间延迟秒数
#' @param email 用于API查询的邮箱
#' @return 补全后的数据框
enrich_articles_batch <- function(data, 
                                 batch_size = 10, 
                                 delay_seconds = 2,
                                 email = "<EMAIL>") {
  # 确保数据是数据框
  if (!is.data.frame(data)) {
    stop("输入数据必须是数据框")
  }
  
  # 创建结果数据框副本
  result_df <- data
  
  # 记录开始时间
  start_time <- Sys.time()
  message(paste0("[", format(start_time, "%Y-%m-%d %H:%M:%S"), "] ",
                "批量处理: 开始综合补全处理，共", nrow(data), "篇文章"))
  
  # 分批处理
  total_batches <- ceiling(nrow(data) / batch_size)
  for (batch_idx in 1:total_batches) {
    # 计算当前批次的索引范围
    start_idx <- (batch_idx - 1) * batch_size + 1
    end_idx <- min(batch_idx * batch_size, nrow(data))
    
    message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                  "批量处理: 处理批次 ", batch_idx, "/", total_batches,
                  " (文章 ", start_idx, "-", end_idx, ")"))
    
    # 处理当前批次的每篇文章
    for (row_idx in start_idx:end_idx) {
      # 提取文章信息
      article <- data[row_idx, ]
      
      # 计算文章标识信息用于日志
      article_id <- if (!is.null(article$UT) && !is.na(article$UT)) {
        article$UT
      } else if (!is.null(article$DI) && !is.na(article$DI)) {
        article$DI
      } else {
        paste0("Row#", row_idx)
      }
      
      article_title <- if (!is.null(article$TI) && !is.na(article$TI)) {
        substr(article$TI, 1, 40)
      } else {
        "未知标题"
      }
      
      message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                    "文章处理: 开始处理文章: ", article_id, ", 标题: ", 
                    article_title, "..."))
      
      # 补全文章信息
      enriched_article <- enrich_article_comprehensive(article, email)
      
      # 更新结果数据框
      for (col_name in names(enriched_article)) {
        # 如果列不存在于结果数据框中，添加该列
        if (!(col_name %in% names(result_df))) {
          result_df[[col_name]] <- NA
        }
        
        # 更新单元格值
        result_df[row_idx, col_name] <- enriched_article[[col_name]]
      }
      
      # 显示处理进度
      if (row_idx %% 10 == 0 || row_idx == end_idx) {
        elapsed <- difftime(Sys.time(), start_time, units = "mins")
        message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                      "进度: ", row_idx, "/", nrow(data), " (", 
                      round(row_idx/nrow(data)*100), "%), ",
                      "用时: ", round(elapsed, 1), " 分钟"))
      }
    }
    
    # 批次间延迟，避免API速率限制
    if (batch_idx < total_batches) {
      message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                    "批量处理: 暂停", delay_seconds, "秒以遵守API限制..."))
      Sys.sleep(delay_seconds)
    }
  }
  
  # 记录完成信息
  end_time <- Sys.time()
  total_time <- difftime(end_time, start_time, units = "mins")
  message(paste0("[", format(end_time, "%Y-%m-%d %H:%M:%S"), "] ",
                "批量处理: 补全处理完成，总用时: ", round(total_time, 1), " 分钟"))
  
  # 保存结果
  timestamp <- format(Sys.time(), "%Y%m%d%H%M")
  csv_path <- paste0("enriched_data_", timestamp, ".csv")
  rds_path <- paste0("enriched_data_", timestamp, ".rds")
  
  write.csv(result_df, csv_path, row.names = FALSE)
  saveRDS(result_df, rds_path)
  
  message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                "批量处理: 补全结果已保存至:\n", 
                "CSV: ", csv_path, "\n",
                "RDS: ", rds_path))
  
  return(result_df)
}

# =============================================================================
# 数据加载和前处理函数
# =============================================================================

#' 加载并预处理Web of Science数据
#' @param file_path 数据文件路径
#' @param file_format 文件格式，支持"csv"或"tab"
#' @return 预处理后的数据框
load_wos_data <- function(file_path, file_format = "csv") {
  message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                "数据加载: 开始加载", file_path))
  
  # 根据格式加载数据
  if (file_format == "csv") {
    data <- read.csv(file_path, stringsAsFactors = FALSE, check.names = FALSE)
  } else if (file_format == "tab") {
    data <- read.delim(file_path, stringsAsFactors = FALSE, check.names = FALSE)
  } else {
    stop("不支持的文件格式，请使用'csv'或'tab'")
  }
  
  message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                "数据加载: 成功加载", nrow(data), "行和", ncol(data), "列"))
  
  # 检查DOI列
  if (!("DI" %in% names(data)) && ("DOI" %in% names(data))) {
    message("将DOI列重命名为DI以保持一致性")
    data$DI <- data$DOI
  } else if (!("DI" %in% names(data)) && !("DOI" %in% names(data))) {
    warning("数据中没有DOI列，这可能会限制API查询的效果")
  }
  
  # 检查并计算缺失值
  missing_counts <- sapply(data, function(x) sum(is.na(x) | x == ""))
  total_missing <- sum(missing_counts)
  message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                "数据质量: 数据中共有", total_missing, "个缺失值，占比", 
                round(total_missing/(nrow(data)*ncol(data))*100, 2), "%"))
  
  # 显示前5个缺失最多的列
  missing_cols <- sort(missing_counts, decreasing = TRUE)[1:min(5, length(missing_counts))]
  message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                "数据质量: 缺失值最多的列:\n", 
                paste(names(missing_cols), "(", missing_cols, "个)", collapse = "\n")))
  
  # 如果DOI列有缺失值，提示用户
  if ("DI" %in% names(data)) {
    doi_missing <- sum(is.na(data$DI) | data$DI == "")
    if (doi_missing > 0) {
      warning(paste0("有", doi_missing, "篇文章(", 
                    round(doi_missing/nrow(data)*100, 2), 
                    "%)没有DOI，这些文章无法通过API补全"))
    }
  }
  
  return(data)
}

#' 生成数据补全报告
#' @param original_data 原始数据
#' @param enriched_data 补全后的数据
#' @return 报告内容的字符串
generate_enrichment_report <- function(original_data, enriched_data) {
  # 计算新增字段
  original_cols <- names(original_data)
  enriched_cols <- names(enriched_data)
  new_cols <- setdiff(enriched_cols, original_cols)
  
  # 统计缺失值改进情况
  original_missing <- sapply(original_data, function(x) sum(is.na(x) | x == ""))
  enriched_missing <- sapply(enriched_data[, original_cols], function(x) sum(is.na(x) | x == ""))
  
  # 计算改进的字段
  improved_cols <- names(which(enriched_missing < original_missing))
  
  # 汇总改进情况
  total_original_missing <- sum(original_missing)
  total_enriched_missing <- sum(enriched_missing)
  improvement_percentage <- (total_original_missing - total_enriched_missing) / total_original_missing * 100
  
  # 获取API调用统计
  api_fields <- grep("^(OpenAlex|Crossref|Unpaywall)_", enriched_cols, value = TRUE)
  api_stats <- sapply(api_fields, function(field) {
    sum(!is.na(enriched_data[[field]]) & enriched_data[[field]] != "")
  })
  
  # 生成报告
  report <- paste0(
    "# 多API学术文献数据补全报告\n",
    "生成时间: ", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "\n\n",
    
    "## 基本统计\n",
    "- 文章总数: ", nrow(original_data), "\n",
    "- 原始字段数: ", length(original_cols), "\n",
    "- 补全后字段数: ", length(enriched_cols), "\n",
    "- 新增字段数: ", length(new_cols), "\n\n",
    
    "## 新增字段\n",
    paste("- ", new_cols, collapse = "\n"), "\n\n",
    
    "## 缺失值改进\n",
    "- 原始数据缺失值: ", total_original_missing, "\n",
    "- 补全后缺失值: ", total_enriched_missing, "\n",
    "- 减少缺失值: ", (total_original_missing - total_enriched_missing), 
    " (", round(improvement_percentage, 2), "%)\n\n",
    
    "## 改进最显著的字段\n",
    paste0("- ", names(sort((original_missing - enriched_missing)[improved_cols], decreasing = TRUE)[1:min(10, length(improved_cols))]), 
          collapse = "\n"), "\n\n",
    
    "## API调用统计\n",
    "### OpenAlex\n",
    paste0("- ", grep("^OpenAlex_", api_fields, value = TRUE), ": ", 
          api_stats[grep("^OpenAlex_", api_fields, value = TRUE)], "篇文章",
          collapse = "\n"), "\n\n",
    
    "### Crossref\n",
    paste0("- ", grep("^Crossref_", api_fields, value = TRUE), ": ", 
          api_stats[grep("^Crossref_", api_fields, value = TRUE)], "篇文章",
          collapse = "\n"), "\n\n",
    
    "### Unpaywall\n",
    paste0("- ", grep("^Unpaywall_", api_fields, value = TRUE), ": ", 
          api_stats[grep("^Unpaywall_", api_fields, value = TRUE)], "篇文章",
          collapse = "\n"), "\n\n"
  )
  
  # 保存报告
  report_path <- paste0("enrichment_report_", format(Sys.time(), "%Y%m%d%H%M"), ".md")
  writeLines(report, report_path)
  
  message(paste0("[", format(Sys.time(), "%Y-%m-%d %H:%M:%S"), "] ",
                "报告生成: 补全报告已保存至 ", report_path))
  
  return(report)
}

# =============================================================================
# 主函数
# =============================================================================

#' 执行完整的多API补全流程
#' @param input_file 输入文件路径
#' @param file_format 文件格式，"csv"或"tab"
#' @param batch_size 每批处理的文章数
#' @param delay_seconds 批次间延迟秒数
#' @param email 用于API查询的邮箱
#' @return 补全后的数据框
run_multi_api_enrichment <- function(input_file, 
                                     file_format = "csv", 
                                     batch_size = 10, 
                                     delay_seconds = 2,
                                     email = "<EMAIL>") {
  # 显示欢迎信息
  message("\n========================================================")
  message("      多API学术文献数据补全系统")
  message("  整合OpenAlex、Crossref和Unpaywall API")
  message("========================================================\n")
  
  # 加载数据
  wos_data <- load_wos_data(input_file, file_format)
  
  # 执行补全
  message("\n--- 开始执行多API数据补全 ---\n")
  enriched_data <- enrich_articles_batch(wos_data, batch_size, delay_seconds, email)
  
  # 生成报告
  message("\n--- 生成数据补全报告 ---\n")
  generate_enrichment_report(wos_data, enriched_data)
  
  # 完成信息
  message("\n========================================================")
  message("      数据补全完成!")
  message("========================================================\n")
  
  return(enriched_data)
}

# =============================================================================
# 示例使用
# =============================================================================

# 要使用此脚本，取消下面代码的注释并修改路径
# 
运行完整工作流
 enriched_wos_data <- run_multi_api_enrichment(
input_file = "your_wos_data.csv", 
#   file_format = "csv",
#   batch_size = 10,
#   delay_seconds = 2,
#   email = "<EMAIL>"
# )
# 
# # 或者手动运行各个步骤
# wos_data <- load_wos_data("your_wos_data.csv", "csv")
# enriched_data <- enrich_articles_batch(wos_data, batch_size = 10, delay_seconds = 2)
# generate_enrichment_report(wos_data, enriched_data)
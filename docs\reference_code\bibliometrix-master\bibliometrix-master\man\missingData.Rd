% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/missingData.R
\name{missingData}
\alias{missingData}
\title{Completeness of bibliographic metadata}
\usage{
missingData(M)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by \code{\link{convert2df}} function.}
}
\value{
The function \code{missingData} returns a list containing two objects:
\tabular{lll}{
\code{allTags}  \tab   \tab is a data frame including results for all original metadata tags from the collection\cr
\code{mandatoryTags}\tab    \tab is a data frame that included only the tags needed for analysis with bibliometrix and biblioshiny.}
}
\description{
It calculates the percentage of missing data in the metadata of a bibliographic data frame.
}
\details{
Each metadata is assigned a status c("Excellent," "Good," "Acceptable", "Poor", "Critical," "Completely missing") 
depending on the percentage of missing data. In particular, the column *status* classifies the percentage of missing 
value in 5 categories: "Excellent" (0%), "Good" (0.01% to 10.00%), "Acceptable" (from 10.01% to 20.00%), 
"Poor" (from 20.01% to 50.00%), "Critical" (from 50.01% to 99.99%), "Completely missing" (100%).

The results of the function allow us to understand which analyses can be performed with bibliometrix 
and which cannot based on the completeness (or status) of different metadata.
}
\examples{
data(scientometrics, package = "bibliometrixData")
res <- missingData(scientometrics)
print(res$mandatoryTags)

}

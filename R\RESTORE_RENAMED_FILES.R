# 恢复已重命名的文件
# 从备份中恢复并正确重命名

cat("=== 恢复已重命名的文件 ===\n")
cat("从备份中恢复并正确重命名...\n\n")

# 恢复并重命名文件
restore_and_rename <- function() {
  cat("📦 从备份恢复并重命名文件...\n")
  
  # 从BACKUP_ACTUAL_RENAME恢复并重命名
  restore_mapping <- list(
    "BACKUP_ACTUAL_RENAME/01_data_import_wos.R" = "01_import_wos_data.R",
    "BACKUP_ACTUAL_RENAME/02_data_import_citespace.R" = "02_import_citespace_data.R",
    "BACKUP_ACTUAL_RENAME/03_data_import_vosviewer.R" = "03_import_vosviewer_data.R",
    "BACKUP_ACTUAL_RENAME/04_data_validation.R" = "04_validate_and_clean_data.R",
    "BACKUP_ACTUAL_RENAME/05_deduplication_enhanced.R" = "05_deduplicate_records.R"
  )
  
  for (backup_path in names(restore_mapping)) {
    target_file <- restore_mapping[[backup_path]]
    src_path <- file.path("R", backup_path)
    dst_path <- file.path("R", target_file)
    
    if (file.exists(src_path)) {
      file.copy(src_path, dst_path)
      cat(sprintf("  ✅ 恢复并重命名: %s → %s\n", backup_path, target_file))
    } else {
      cat(sprintf("  ❌ 备份文件不存在: %s\n", backup_path))
    }
  }
}

# 最终验证
final_check <- function() {
  cat("\n🔍 最终验证所有文件...\n")
  
  expected_files <- c(
    "01_import_wos_data.R",
    "02_import_citespace_data.R", 
    "03_import_vosviewer_data.R",
    "04_validate_and_clean_data.R",
    "05_deduplicate_records.R",
    "06_deduplicate_advanced.R",
    "07_enhance_data_comprehensive.R",
    "08_complete_missing_dois.R",
    "09_integrate_enhanced_data.R",
    "10_quality_control_and_report.R"
  )
  
  cat("最终文件检查:\n")
  all_present <- TRUE
  
  for (file in expected_files) {
    if (file.exists(file.path("R", file))) {
      cat(sprintf("  ✅ %s\n", file))
    } else {
      cat(sprintf("  ❌ %s (缺失)\n", file))
      all_present <- FALSE
    }
  }
  
  if (all_present) {
    cat("\n🎉 所有文件重命名完全成功！\n")
    
    # 显示最终的处理流程
    cat("\n📋 最终处理流程 (按数据处理顺序):\n")
    cat("┌─ 阶段1: 数据导入 ─────────────────────┐\n")
    cat("│ 01_import_wos_data.R              │ WoS数据导入\n")
    cat("│ 02_import_citespace_data.R        │ CiteSpace数据导入\n") 
    cat("│ 03_import_vosviewer_data.R        │ VOSviewer数据导入\n")
    cat("├─ 阶段2: 验证清理 ─────────────────────┤\n")
    cat("│ 04_validate_and_clean_data.R      │ 数据验证与清理\n")
    cat("├─ 阶段3: 去重处理 ─────────────────────┤\n")
    cat("│ 05_deduplicate_records.R          │ 去重处理\n")
    cat("│ 06_deduplicate_advanced.R         │ 高级去重(可选)\n")
    cat("├─ 阶段4: 数据增强 ─────────────────────┤\n")
    cat("│ 07_enhance_data_comprehensive.R   │ 数据增强\n")
    cat("│ 08_complete_missing_dois.R        │ DOI补全 ⭐\n")
    cat("│ 09_integrate_enhanced_data.R      │ 数据整合\n")
    cat("├─ 阶段5: 质量控制 ─────────────────────┤\n")
    cat("│ 10_quality_control_and_report.R   │ 质量控制\n")
    cat("└───────────────────────────────────────┘\n")
    
    cat("\n🎯 核心改进总结:\n")
    cat("✅ DOI补全从06移到08，正确定位在数据增强阶段\n")
    cat("✅ 文件编号与处理顺序完全对应\n")
    cat("✅ 文件名直观反映处理功能\n")
    cat("✅ 阶段划分清晰明确\n")
    cat("✅ 符合数据处理的最佳实践\n")
    
  } else {
    cat("\n⚠️  仍有文件缺失\n")
  }
  
  return(all_present)
}

# 显示使用指南
show_usage_guide <- function() {
  cat("\n📖 使用指南:\n")
  
  cat("\n1. 完整流程执行:\n")
  cat("```r\n")
  cat("# 按顺序执行所有核心脚本\n")
  cat("scripts <- c('01', '02', '03', '04', '05', '07', '08', '09', '10')\n")
  cat("for (num in scripts) {\n")
  cat("  script_file <- list.files('R', pattern = paste0('^', num, '_'), full.names = TRUE)[1]\n")
  cat("  if (!is.null(script_file) && file.exists(script_file)) {\n")
  cat("    cat('执行:', basename(script_file), '\\n')\n")
  cat("    source(script_file)\n")
  cat("  }\n")
  cat("}\n")
  cat("```\n")
  
  cat("\n2. 可选高级功能:\n")
  cat("```r\n")
  cat("# 高级去重 (在05后可选执行)\n")
  cat("source('R/06_deduplicate_advanced.R')\n")
  cat("```\n")
  
  cat("\n3. 部分流程执行:\n")
  cat("```r\n")
  cat("# 只执行数据增强部分\n")
  cat("source('R/07_enhance_data_comprehensive.R')  # 数据增强\n")
  cat("source('R/08_complete_missing_dois.R')       # DOI补全\n")
  cat("source('R/09_integrate_enhanced_data.R')     # 数据整合\n")
  cat("```\n")
}

# 主执行函数
main_restore <- function() {
  cat("开始恢复已重命名的文件...\n\n")
  
  restore_and_rename()
  success <- final_check()
  
  if (success) {
    show_usage_guide()
  }
  
  cat("\n=== 文件恢复完成 ===\n")
  cat("✅ 从备份恢复了所有文件\n")
  cat("✅ 完成了正确的重命名\n")
  
  if (success) {
    cat("\n🎉 R脚本按数据处理顺序重新整理完全成功！\n")
    cat("📋 DOI补全现在正确位于数据增强阶段 (08)\n")
    cat("🔄 所有文件按处理顺序完美组织\n")
    cat("📁 文件名直观反映处理功能\n")
    cat("🎯 完全符合数据处理的最佳实践\n")
  } else {
    cat("\n⚠️  文件恢复仍有问题\n")
  }
}

# 执行恢复
main_restore()

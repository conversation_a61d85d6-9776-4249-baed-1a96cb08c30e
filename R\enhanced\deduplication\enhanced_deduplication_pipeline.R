# --- 增强去重主流程脚本 ---
# 支持增强数据集与标准数据集两种模式
# 参数mode可选："enhanced"（增强数据流，默认）或"bibliometrix"（标准数据流）
# 详细说明见脚本注释

# --- 0. 配置管理 ---
required_packages <- c("bibliometrix", "here", "dplyr", "stringr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保here包正确初始化
if (!requireNamespace("here", quietly = TRUE)) {
  stop("无法加载here包，请手动安装：install.packages('here')")
}

# --- 1. 解析参数 ---
# mode: "enhanced"（增强数据集，默认）或"bibliometrix"（标准数据集）
args <- commandArgs(trailingOnly = TRUE)
mode <- ifelse(length(args) >= 1, args[[1]], "enhanced")

# --- 2. 配置输入输出路径 ---
if (mode == "enhanced") {
  config <- list(
    paths = list(
      input = here("data_repository", "02_enhanced_dataset"),
      output = here("data_repository", "03_deduplicated_dataset"),
      logs = here("data_repository", "05_execution_logs", "deduplication_logs"),
      reports = here("data_repository", "06_data_reports", "deduplication_reports")
    ),
    files = list(
      input = "enhanced_data_initial.rds",
      output = "data_deduplicated.rds",
      log = "deduplication_enhanced.log"
    ),
    deduplication = list(
      ut_exact = TRUE,
      title_fuzzy = TRUE,
      abstract_fuzzy = FALSE,
      title_tolerances = c(0.95),
      author_tolerance = 0.95,
      abstract_tolerance = 0.90
    )
  )
} else if (mode == "bibliometrix") {
  config <- list(
    paths = list(
      input = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
      output = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
      logs = here("data_repository", "05_execution_logs", "bibliometrix_logs"),
      reports = here("data_repository", "06_data_reports", "biblioshiny_reports")
    ),
    files = list(
      input = "datay_bibliometrix_initial.rds",
      output = "datax_bibliometrix_advanced.rds",
      log = "deduplication.log"
    ),
    deduplication = list(
      ut_exact = FALSE,
      title_fuzzy = TRUE,
      abstract_fuzzy = TRUE,
      title_tolerances = c(0.98, 0.95, 0.90),
      author_tolerance = 0.95,
      abstract_tolerance = 0.90
    )
  )
} else {
  stop("未知mode参数，仅支持'enhanced'或'bibliometrix'")
}

# --- 3. 定义输入输出路径 ---
input_file <- file.path(config$paths$input, config$files$input)
output_file <- file.path(config$paths$output, config$files$output)
log_file <- file.path(config$paths$logs, config$files$log)

# 创建必要的目录（使用suppressMessages避免重复输出）
suppressMessages({
  for (dir_path in c(config$paths$output, config$paths$logs, config$paths$reports)) {
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = TRUE)
      message("创建目录:", dir_path)
    }
  }
})

# --- 4. 加载数据 ---
if (!file.exists(input_file)) {
  stop(sprintf("输入数据文件不存在: %s\n请先运行数据导入脚本。", input_file))
}
M <- readRDS(input_file)
cat(sprintf("成功加载数据，初始记录数: %d\n", nrow(M)), file = stderr())

# --- 5. 初始化日志文件连接 ---
log_con <- file(log_file, "w")
sink(log_con, append = TRUE, type = "output")
sink(log_con, append = TRUE, type = "message")

# 记录开始时间
start_time <- Sys.time()
cat(sprintf("去重开始时间: %s\n", format(start_time)), file = stderr())
cat("输入文件:", basename(input_file), "\n")
cat("初始记录数:", nrow(M), "\n\n")

# --- 辅助函数：执行去重并记录 ---
perform_deduplication_step <- function(M_current, field, exact, tol = 0.95, step_name, log_connection) {
  step_intro_msg <- paste0("\n--- 开始去重步骤: ", step_name, " (字段: ", field, ", Exact: ", exact, if(!exact) paste0(", Tol: ", tol) else "", ") ---\n")
  cat(step_intro_msg, file = log_connection)
  cat(step_intro_msg, file = stderr())
  
  log_step_header <- paste0("--- ", step_name, " (字段: ", field, ", Exact: ", exact, if(!exact) paste0(", Tol: ", tol) else "", ") ---\n")
  log_before_count <- paste0("步骤开始前记录数: ", nrow(M_current), "\n")
  cat(log_step_header, file = log_connection)
  cat(log_before_count, file = log_connection)
  cat(log_before_count, file = stderr())

  M_before_step <- M_current
  if (!(field %in% names(M_before_step))) {
    warn_msg <- paste0("警告: 字段 '", field, "' 在数据中不存在，跳过此步骤。\n")
    cat(warn_msg, file = log_connection)
    cat(warn_msg, file = stderr())
    return(M_current)
  }
  M_before_step[[field]] <- as.character(M_before_step[[field]])
  M_before_step[[field]][is.na(M_before_step[[field]])] <- ""
  M_to_process <- M_before_step
  M_skipped_due_to_empty_field <- data.frame()
  M_skipped_due_to_no_doi <- data.frame()
  if (exact && field == "DI") {
    has_doi <- M_before_step[[field]] != ""
    M_with_doi <- M_before_step[has_doi, , drop = FALSE]
    M_skipped_due_to_no_doi <- M_before_step[!has_doi, , drop = FALSE]
    cat(sprintf("DOI精确匹配: %d 条记录有DOI, %d 条记录无DOI (将不参与此轮DOI去重)。\n", nrow(M_with_doi), nrow(M_skipped_due_to_no_doi)), file = log_connection)
    cat(sprintf("DOI精确匹配: %d 条记录有DOI, %d 条记录无DOI (将不参与此轮DOI去重)。\n", nrow(M_with_doi), nrow(M_skipped_due_to_no_doi)), file = stderr())
    M_to_process <- M_with_doi
  } else if (!exact) {
    empty_target_field_rows <- which(M_before_step[[field]] == "")
    if (length(empty_target_field_rows) > 0) {
      M_to_process <- M_before_step[-empty_target_field_rows, , drop = FALSE]
      M_skipped_due_to_empty_field <- M_before_step[empty_target_field_rows, , drop = FALSE]
      info_msg <- sprintf("注意: 有 %d 条记录因目标字段 '%s' 为空，未参与此轮模糊匹配。\n", nrow(M_skipped_due_to_empty_field), field)
      cat(info_msg, file = log_connection)
      cat(info_msg, file = stderr())
    }
  }
  M_processed_deduped <- M_to_process
  if (nrow(M_to_process) > 1) {
    if (exact) {
      M_processed_deduped <- duplicatedMatching(M_to_process, Field = field, exact = TRUE)
    } else {
      if (!"UT" %in% names(M_to_process)) stop("UT column is required for deduplication logging but not found.")
      M_processed_deduped <- duplicatedMatching(M_to_process, Field = field, exact = FALSE, tol = tol)
    }
  }
  # 合并回跳过的记录
  M_after_step <- M_processed_deduped
  if (nrow(M_skipped_due_to_empty_field) > 0) {
    if (nrow(M_after_step) == 0) { M_after_step <- M_skipped_due_to_empty_field }
    else {
      common_cols <- intersect(names(M_after_step), names(M_skipped_due_to_empty_field))
      if (length(common_cols) > 0) {
        M_after_step <- rbind(M_after_step[, common_cols, drop = FALSE], M_skipped_due_to_empty_field[, common_cols, drop = FALSE])
      }
    }
  }
  if (exact && field == "DI" && nrow(M_skipped_due_to_no_doi) > 0) {
    if (nrow(M_after_step) == 0) { M_after_step <- M_skipped_due_to_no_doi }
    else {
      common_cols <- intersect(names(M_after_step), names(M_skipped_due_to_no_doi))
      if (length(common_cols) > 0) {
        M_after_step <- rbind(M_after_step[, common_cols, drop = FALSE], M_skipped_due_to_no_doi[, common_cols, drop = FALSE])
      }
    }
  }
  removed_count <- nrow(M_before_step) - nrow(M_after_step)
  summary_msg <- sprintf("步骤完成后记录数: %d (此步骤移除了 %d 条)\n", nrow(M_after_step), removed_count)
  cat(summary_msg, file = stderr())
  cat(summary_msg, file = log_connection)
  return(M_after_step)
}

# --- 7. 执行去重主流程 ---
M_dedup <- M

# 7.1 UT/DOI精确匹配
if (config$deduplication$ut_exact && ("UT" %in% names(M_dedup) || "DI" %in% names(M_dedup))) {
  if ("UT" %in% names(M_dedup)) {
    M_dedup <- perform_deduplication_step(M_dedup, field = "UT", exact = TRUE, step_name = "UT精确匹配", log_connection = log_con)
  }
  if ("DI" %in% names(M_dedup)) {
    M_dedup <- perform_deduplication_step(M_dedup, field = "DI", exact = TRUE, step_name = "DOI精确匹配", log_connection = log_con)
  }
}

# 7.2 标题多轮模糊匹配
if (config$deduplication$title_fuzzy && "TI" %in% names(M_dedup)) {
  for (tol in config$deduplication$title_tolerances) {
    M_dedup <- perform_deduplication_step(M_dedup, field = "TI", exact = FALSE, tol = tol, step_name = sprintf("标题模糊匹配 (tol=%.2f)", tol), log_connection = log_con)
  }
}

# 7.3 作者模糊匹配
if ("author_tolerance" %in% names(config$deduplication) && "AU" %in% names(M_dedup)) {
  M_dedup <- perform_deduplication_step(M_dedup, field = "AU", exact = FALSE, tol = config$deduplication$author_tolerance, step_name = "作者模糊匹配", log_connection = log_con)
}

# 7.4 摘要模糊匹配
if (config$deduplication$abstract_fuzzy && "AB" %in% names(M_dedup)) {
  M_dedup <- perform_deduplication_step(M_dedup, field = "AB", exact = FALSE, tol = config$deduplication$abstract_tolerance, step_name = "摘要模糊匹配", log_connection = log_con)
}

# --- 8. 保存去重结果 ---
saveRDS(M_dedup, file = output_file)
cat(sprintf("\n去重后的数据已保存至: %s\n", output_file))

# --- 9. 生成去重报告 ---
report_file <- file.path(config$paths$reports, "deduplication_report.txt")
report_con <- file(report_file, "w")
cat("=== 文献去重报告 ===\n", file = report_con)
cat(sprintf("报告生成时间: %s\n", format(Sys.time())), file = report_con)
cat(sprintf("初始记录数: %d\n", nrow(M)), file = report_con)
cat(sprintf("最终记录数: %d\n", nrow(M_dedup)), file = report_con)
cat(sprintf("总移除数: %d\n", nrow(M) - nrow(M_dedup)), file = report_con)
cat(sprintf("去重率: %.2f%%\n", round((nrow(M) - nrow(M_dedup)) / nrow(M) * 100, 2)), file = report_con)
cat("\n=== 去重策略说明 ===\n", file = report_con)
cat("1. UT/DOI精确匹配\n", file = report_con)
cat("2. 标题多轮模糊匹配\n", file = report_con)
cat("3. 作者模糊匹配\n", file = report_con)
cat("4. 摘要模糊匹配\n", file = report_con)
close(report_con)
cat(sprintf("去重报告已保存至: %s\n", report_file))

# --- 10. 清理和日志记录 ---
end_time <- Sys.time()
duration <- difftime(end_time, start_time, units = "mins")
cat(sprintf("\n去重完成时间: %s\n", format(end_time)), file = stderr())
cat(sprintf("总耗时: %.2f 分钟\n", as.numeric(duration)), file = stderr())
cat(sprintf("初始记录数: %d\n", nrow(M)), file = stderr())
cat(sprintf("最终记录数: %d\n", nrow(M_dedup)), file = stderr())
cat(sprintf("移除重复数: %d\n", nrow(M) - nrow(M_dedup)), file = stderr())

# 关闭日志
sink(type = "message")
sink(type = "output")
close(log_con)

cat("\n脚本 'enhanced_deduplication_pipeline.R' 执行完毕。\n", file = stderr()) 
# 紧急恢复脚本 - 恢复被误删的重要代码
# 从archive目录恢复需要的文件

cat("=== 紧急代码恢复 ===\n")
cat("检查archive目录中的所有文件...\n\n")

# 列出所有归档的文件
archive_files <- list.files("R/archive", pattern = "\\.R$", full.names = FALSE)

cat("📁 归档文件列表:\n")
for (i in seq_along(archive_files)) {
  cat(sprintf("%2d. %s\n", i, archive_files[i]))
}

# 恢复函数
recover_file <- function(filename, new_name = NULL) {
  archive_path <- file.path("R/archive", filename)
  
  if (is.null(new_name)) {
    new_name <- filename
  }
  
  recovery_path <- file.path("R", new_name)
  
  if (file.exists(archive_path)) {
    if (file.copy(archive_path, recovery_path, overwrite = TRUE)) {
      cat(sprintf("✅ 恢复成功: %s → %s\n", filename, new_name))
      return(TRUE)
    } else {
      cat(sprintf("❌ 恢复失败: %s\n", filename))
      return(FALSE)
    }
  } else {
    cat(sprintf("⚠️  文件不存在: %s\n", filename))
    return(FALSE)
  }
}

# 批量恢复函数
batch_recover <- function(file_list) {
  cat("\n开始批量恢复...\n")
  success_count <- 0
  
  for (file_info in file_list) {
    if (is.character(file_info)) {
      # 简单文件名
      if (recover_file(file_info)) {
        success_count <- success_count + 1
      }
    } else if (is.list(file_info)) {
      # 包含新名称的列表
      if (recover_file(file_info$old_name, file_info$new_name)) {
        success_count <- success_count + 1
      }
    }
  }
  
  cat(sprintf("\n批量恢复完成: %d个文件成功恢复\n", success_count))
}

# 预定义的重要文件恢复列表
important_files <- list(
  # 数据增强相关
  "01_data_enhancement_framework.R",
  "01_data_enhancement_complete.R",
  
  # 去重相关  
  "02_deduplication.R",
  "02_deduplication_enhanced_advanced.R",
  "analyze_deduplication.R",
  
  # DOI补全相关
  "complete_doi_by_ut.R",
  "crossref_doi_lookup.R", 
  "doi_completion_final.R",
  
  # 自动化处理
  "auto_full_processing.R",
  "auto_comprehensive_test.R",
  
  # 最终报告
  "FINAL_AUTO_SUMMARY.R",
  "auto_final_report_generator.R"
)

# 交互式恢复菜单
interactive_recovery <- function() {
  cat("\n=== 交互式恢复菜单 ===\n")
  cat("请选择恢复选项:\n")
  cat("1. 恢复所有重要文件\n")
  cat("2. 恢复数据增强相关文件\n") 
  cat("3. 恢复去重相关文件\n")
  cat("4. 恢复DOI补全相关文件\n")
  cat("5. 恢复自动化处理文件\n")
  cat("6. 恢复报告生成文件\n")
  cat("7. 自定义恢复\n")
  cat("8. 查看文件内容后决定\n")
  cat("0. 退出\n")
  
  # 由于这是脚本，我们提供所有选项的实现
  cat("\n由于这是自动脚本，将显示所有恢复选项...\n")
}

# 恢复数据增强文件
recover_data_enhancement <- function() {
  cat("\n恢复数据增强相关文件...\n")
  files <- c(
    "01_data_enhancement_framework.R",
    "01_data_enhancement_complete.R",
    "01_data_enhancement_simple.R"
  )
  batch_recover(files)
}

# 恢复去重文件
recover_deduplication <- function() {
  cat("\n恢复去重相关文件...\n")
  files <- c(
    "02_deduplication.R",
    "02_deduplication_enhanced_advanced.R", 
    "analyze_deduplication.R",
    "03_deduplication_evaluation.R"
  )
  batch_recover(files)
}

# 恢复DOI补全文件
recover_doi_completion <- function() {
  cat("\n恢复DOI补全相关文件...\n")
  files <- c(
    "complete_doi_by_ut.R",
    "crossref_doi_lookup.R",
    "doi_completion_final.R",
    "complete_doi_resume.R"
  )
  batch_recover(files)
}

# 恢复自动化处理文件
recover_automation <- function() {
  cat("\n恢复自动化处理文件...\n")
  files <- c(
    "auto_full_processing.R",
    "auto_comprehensive_test.R",
    "PROJECT_REORGANIZATION.R"
  )
  batch_recover(files)
}

# 恢复报告文件
recover_reports <- function() {
  cat("\n恢复报告生成文件...\n")
  files <- c(
    "FINAL_AUTO_SUMMARY.R",
    "auto_final_report_generator.R"
  )
  batch_recover(files)
}

# 查看文件内容
preview_file <- function(filename) {
  archive_path <- file.path("R/archive", filename)
  if (file.exists(archive_path)) {
    cat(sprintf("\n=== %s 文件内容预览 ===\n", filename))
    
    # 读取前20行
    lines <- readLines(archive_path, n = 20, warn = FALSE)
    for (i in seq_along(lines)) {
      cat(sprintf("%2d: %s\n", i, lines[i]))
    }
    
    if (length(readLines(archive_path, warn = FALSE)) > 20) {
      cat("... (文件还有更多内容)\n")
    }
    
    cat("=== 预览结束 ===\n\n")
  } else {
    cat(sprintf("文件不存在: %s\n", filename))
  }
}

# 主恢复函数
main_recovery <- function() {
  cat("开始紧急恢复程序...\n\n")
  
  # 显示交互菜单
  interactive_recovery()
  
  cat("\n自动执行所有恢复选项的演示:\n")
  
  cat("\n1️⃣ 数据增强文件恢复:\n")
  recover_data_enhancement()
  
  cat("\n2️⃣ 去重文件恢复:\n") 
  recover_deduplication()
  
  cat("\n3️⃣ DOI补全文件恢复:\n")
  recover_doi_completion()
  
  cat("\n4️⃣ 自动化处理文件恢复:\n")
  recover_automation()
  
  cat("\n5️⃣ 报告文件恢复:\n")
  recover_reports()
  
  cat("\n=== 紧急恢复完成 ===\n")
  cat("✅ 所有重要文件已从archive目录恢复\n")
  cat("📁 请检查R目录确认恢复结果\n")
  cat("⚠️  如需要特定文件，请手动从archive目录复制\n")
  
  # 显示当前R目录状态
  cat("\n📊 当前R目录文件统计:\n")
  r_files <- list.files("R", pattern = "\\.R$")
  cat(sprintf("总计: %d个R脚本文件\n", length(r_files)))
  
  # 按类型分组显示
  core_scripts <- r_files[grepl("^[0-9]{2}_", r_files)]
  other_scripts <- r_files[!grepl("^[0-9]{2}_", r_files)]
  
  cat(sprintf("核心脚本: %d个\n", length(core_scripts)))
  cat(sprintf("其他脚本: %d个\n", length(other_scripts)))
}

# 执行恢复
cat("⚠️  重要提醒: 这将恢复archive目录中的文件到R目录\n")
cat("如果有同名文件将被覆盖！\n")
cat("建议先备份当前的R目录\n\n")

# 执行主恢复程序
main_recovery()

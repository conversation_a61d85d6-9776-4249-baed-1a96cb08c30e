# 设置C栈内存限制
options(expressions = 500000)  # 增加R表达式的递归限制
if (.Platform$OS.type == "windows") {
  memory.limit(size = 16000)  # 在Windows上增加内存限制
} else {
  # 在Unix系统上设置栈大小
  system("ulimit -s 65536")  # 设置栈大小为64MB
}

# 02_deduplication_extreme.R
# bibliometrix 的极限去重策略
# 注意：由于资源限制，当前版本仅执行UT精确匹配和标题模糊匹配
# 摘要模糊匹配因内存和C栈限制暂时无法执行

# --- 0. 配置管理 ---
# 首先加载必要的包
required_packages <- c("bibliometrix", "here", "dplyr", "stringr")
for (pkg in required_packages) {
  if (!requireNamespace(pkg, quietly = TRUE)) {
    install.packages(pkg)
  }
  suppressPackageStartupMessages(library(pkg, character.only = TRUE))
}

# 确保here包正确初始化
if (!requireNamespace("here", quietly = TRUE)) {
  stop("无法加载here包，请手动安装：install.packages('here')")
}

# 定义日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
  if (exists("log_con") && !is.null(log_con) && isOpen(log_con)) {
    cat(formatted_msg, "\n", file = log_con)
  }
}

config <- list(
  # 输入输出路径配置
  paths = list(
    input = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    output = here("data_repository", "01_baseline_datasets", "bibliometrix_processed"),
    logs = here("data_repository", "05_execution_logs", "bibliometrix_logs"),
    reports = here("data_repository", "06_data_reports", "biblioshiny_reports")
  ),
  # 文件命名配置
  files = list(
    input = "datay_bibliometrix_initial.rds",
    output = "datax_bibliometrix_advanced.rds",
    log = "deduplication_extreme.log"
  ),
  # 去重配置 - 极限去重设置
  deduplication = list(
    # 标题去重阈值
    title_tolerance = 0.98
  )
)

# --- 1. 设置环境与加载库 ---
log_message("开始严格去重流程")

# --- 2. 定义输入和输出路径 ---
input_file <- file.path(config$paths$input, config$files$input)
output_file <- file.path(config$paths$output, config$files$output)
log_file <- file.path(config$paths$logs, config$files$log)

# 创建必要的目录
suppressMessages({
  for (dir_path in c(config$paths$output, config$paths$logs, config$paths$reports)) {
    if (!dir.exists(dir_path)) {
      dir.create(dir_path, recursive = TRUE)
      log_message(sprintf("创建目录: %s", dir_path))
    }
  }
})

# --- 3. 加载导入的数据 ---
if (!file.exists(input_file)) {
  log_message("错误：导入的数据文件不存在", "error")
  stop(sprintf("请先运行 01_data_import.R 脚本生成文件: %s", input_file))
}

M <- readRDS(input_file)
log_message(sprintf("成功加载数据，初始记录数: %d", nrow(M)))

# --- 4. 初始化日志文件连接 ---
log_con <- file(log_file, "w")
log_message(sprintf("日志文件已创建: %s", log_file))

# 记录开始时间
start_time <- Sys.time()
log_message(sprintf("去重开始时间: %s", format(start_time)))

# --- 5. 执行去重步骤 ---
M_dedup <- M

# 5.1 UT精确匹配
log_message("开始 UT 精确匹配...")
M_dedup_before_ut <- nrow(M_dedup)
M_dedup <- M_dedup %>% dplyr::distinct(UT, .keep_all = TRUE)
log_message(sprintf("UT 精确匹配后记录数: %d (移除 %d 条)", 
                   nrow(M_dedup), 
                   M_dedup_before_ut - nrow(M_dedup)))

# 5.2 标题模糊匹配
if ("TI" %in% names(M_dedup)) {
  log_message("开始标题模糊匹配...")
  log_message(sprintf("使用标题相似度阈值: %.2f", config$deduplication$title_tolerance))
  M_dedup <- duplicatedMatching(M_dedup, 
                               Field = "TI", 
                               exact = FALSE, 
                               tol = config$deduplication$title_tolerance)
  log_message(sprintf("标题模糊匹配后记录数: %d", nrow(M_dedup)))
} else {
  log_message("警告：未找到标题字段，跳过标题模糊匹配", "warning")
}

# 注意：摘要模糊匹配因资源限制暂时无法执行
log_message("注意：摘要模糊匹配因内存和C栈限制暂时无法执行", "warning")

# --- 6. 保存去重结果 ---
log_message("开始保存去重结果...")
saveRDS(M_dedup, file = output_file)
log_message(sprintf("去重后的数据已保存至: %s", output_file))

# --- 7. 清理和日志记录 ---
end_time <- Sys.time()
duration <- difftime(end_time, start_time, units = "mins")
log_message(sprintf("去重完成，总耗时: %.2f 分钟", as.numeric(duration)))
log_message(sprintf("初始记录数: %d", nrow(M)))
log_message(sprintf("最终记录数: %d", nrow(M_dedup)))
log_message(sprintf("移除重复数: %d", nrow(M) - nrow(M_dedup)))
log_message("脚本执行完毕")

# 关闭日志
close(log_con) 
# 配置参数
CROSSREF_API_URL <- "https://api.crossref.org/works"
USER_AGENT <- "R-script/1.0 (mailto:<EMAIL>)"  # 请替换为你的邮箱
REQUEST_DELAY <- 1  # API请求间隔（秒）
MAX_RETRIES <- 3    # 最大重试次数

# 日志函数
log_info <- function(msg) {
  cat(sprintf("[%s] %s\n", format(Sys.time(), "%H:%M:%S"), msg))
}

log_debug <- function(msg) {
  cat(sprintf("[%s] [DEBUG] %s\n", format(Sys.time(), "%H:%M:%S"), msg))
}

# 字符串清理函数
clean_string <- function(text) {
  if (is.na(text) || is.null(text)) return("")
  
  # 移除标点符号，保留字母数字和空格
  text <- gsub("[^a-zA-Z0-9\\s]", " ", text)
  # 合并多个空格
  text <- gsub("\\s+", " ", text)
  # 去除首尾空格
  text <- trimws(text)
  # 转换为小写
  text <- tolower(text)
  
  return(text)
}

# 计算字符串相似度（改进的Jaccard相似度）
calculate_similarity <- function(str1, str2) {
  if (str1 == "" || str2 == "") return(0)
  
  # 分词
  words1 <- unique(unlist(strsplit(str1, "\\s+")))
  words2 <- unique(unlist(strsplit(str2, "\\s+")))
  
  # 移除空字符串和短词（长度<3）
  words1 <- words1[words1 != "" & nchar(words1) >= 3]
  words2 <- words2[words2 != "" & nchar(words2) >= 3]
  
  if (length(words1) == 0 || length(words2) == 0) return(0)
  
  # 计算Jaccard相似度
  intersection <- length(intersect(words1, words2))
  union <- length(union(words1, words2))
  
  # 如果交集为空，返回0
  if (intersection == 0) return(0)
  
  # 计算相似度
  similarity <- intersection / union
  
  # 如果相似度太低，尝试部分匹配
  if (similarity < 0.3) {
    # 检查是否有部分词匹配
    partial_matches <- sum(sapply(words1, function(w1) {
      any(sapply(words2, function(w2) {
        grepl(w1, w2, fixed = TRUE) || grepl(w2, w1, fixed = TRUE)
      }))
    }))
    
    if (partial_matches > 0) {
      similarity <- max(similarity, partial_matches / max(length(words1), length(words2)))
    }
  }
  
  return(similarity)
}

# 单个DOI查询函数
lookup_doi_single <- function(title, authors = NULL, year = NULL, similarity_threshold = 0.6) {
  
  # 参数验证
  if (is.na(title) || is.null(title) || title == "") {
    log_info("错误: 标题为空")
    return(NA_character_)
  }
  
  log_info(sprintf("查询: '%s'", substr(title, 1, 50)))
  
  # 重试机制
  for (retry in 1:MAX_RETRIES) {
    tryCatch({
      # 构建查询参数
      query_params <- list(
        query = title,
        rows = 10  # 获取前10个结果
      )
      
      # 添加年份过滤
      if (!is.null(year) && !is.na(year) && year != "") {
        query_params$filter <- paste0("from-pub-date:", year, ",until-pub-date:", year)
      }
      
      # 发送API请求
      log_debug(sprintf("发送API请求 (尝试 %d/%d)", retry, MAX_RETRIES))
      response <- GET(
        url = CROSSREF_API_URL,
        query = query_params,
        add_headers(
          "User-Agent" = USER_AGENT,
          "Accept" = "application/json"
        ),
        timeout(30)
      )
      
      # 检查响应状态
      if (status_code(response) != 200) {
        log_info(sprintf("API请求失败，状态码: %d", status_code(response)))
        if (retry < MAX_RETRIES) {
          log_info("等待后重试...")
          Sys.sleep(REQUEST_DELAY * 2)
          next
        }
        return(NA_character_)
      }
      
      # 解析JSON响应
      content_text <- content(response, "text", encoding = "UTF-8")
      data <- fromJSON(content_text, flatten = TRUE)
      
      # 检查是否有结果
      if (is.null(data$message$items) || length(data$message$items) == 0) {
        log_info("未找到匹配结果")
        return(NA_character_)
      }
      
      items <- data$message$items
      
      # 清理查询标题
      query_title_clean <- clean_string(title)
      
      # 提取作者姓氏（如果提供）
      query_author_surname <- ""
      if (!is.null(authors) && !is.na(authors) && authors != "") {
        query_author_surname <- clean_string(extract_author_surname(authors))
      }
      
      # 评估每个候选结果
      best_match <- NULL
      best_score <- 0
      
      for (i in 1:nrow(items)) {
        item <- items[i, ]
        
        # 获取候选标题
        candidate_title <- ""
        if ("title" %in% names(item) && !is.na(item$title)) {
          if (is.list(item$title)) {
            candidate_title <- item$title[[1]][1]
          } else {
            candidate_title <- item$title[1]
          }
        }
        
        if (is.na(candidate_title) || candidate_title == "") next
        
        # 计算标题相似度
        candidate_title_clean <- clean_string(candidate_title)
        title_similarity <- calculate_similarity(query_title_clean, candidate_title_clean)
        
        # 计算作者匹配度
        author_similarity <- 0
        if (query_author_surname != "" && "author" %in% names(item) && !is.null(item$author)) {
          # 处理作者信息
          if (is.data.frame(item$author)) {
            author_surnames <- item$author$family
          } else if (is.list(item$author)) {
            author_surnames <- sapply(item$author, function(x) x$family %||% "")
          } else {
            author_surnames <- character(0)
          }
          
          if (length(author_surnames) > 0) {
            author_surnames_clean <- sapply(author_surnames, clean_string)
            # 检查是否有匹配的姓氏
            if (any(grepl(query_author_surname, author_surnames_clean, fixed = TRUE))) {
              author_similarity <- 1
            }
          }
        } else if (query_author_surname == "") {
          # 如果没有提供作者信息，不惩罚
          author_similarity <- 0.5
        }
        
        # 计算年份匹配度
        year_similarity <- 0
        if (!is.null(year) && !is.na(year) && year != "") {
          published_year <- NA
          if ("published.date-parts" %in% names(item) && !is.null(item$`published.date-parts`)) {
            if (is.list(item$`published.date-parts`) && length(item$`published.date-parts`) > 0) {
              year_parts <- item$`published.date-parts`[[1]]
              if (length(year_parts) > 0) {
                published_year <- year_parts[1]
              }
            }
          }
          
          if (!is.na(published_year)) {
            year_diff <- abs(as.numeric(year) - as.numeric(published_year))
            year_similarity <- ifelse(year_diff <= 1, 1, 0)
          } else {
            year_similarity <- 0.3  # 没有年份信息时给予部分分数
          }
        } else {
          year_similarity <- 0.5  # 如果没有提供年份，不惩罚
        }
        
        # 计算综合分数（标题权重最高）
        total_score <- (title_similarity * 0.7) + (author_similarity * 0.2) + (year_similarity * 0.1)
        
        log_debug(sprintf("候选 %d: 标题相似度=%.2f, 作者匹配=%.2f, 年份匹配=%.2f, 总分=%.2f", 
                        i, title_similarity, author_similarity, year_similarity, total_score))
        
        if (total_score > best_score) {
          best_score <- total_score
          best_match <- item
        }
      }
      
      # 检查最佳匹配是否满足阈值
      if (!is.null(best_match) && best_score >= similarity_threshold) {
        doi <- best_match$DOI
        if (!is.na(doi) && doi != "") {
          log_info(sprintf("找到DOI: %s (匹配分数: %.2f)", doi, best_score))
          return(doi)
        }
      }
      
      log_info(sprintf("未找到满足阈值的匹配 (最高分数: %.2f, 阈值: %.2f)", 
                      best_score, similarity_threshold))
      return(NA_character_)
      
    }, error = function(e) {
      log_info(sprintf("查询出错: %s", e$message))
      if (retry < MAX_RETRIES) {
        log_info("等待后重试...")
        Sys.sleep(REQUEST_DELAY * 2)
        next
      }
      return(NA_character_)
    })
  }
  
  return(NA_character_)
}

# 批量DOI查询函数
lookup_doi_batch <- function(data_df, title_col = "title", author_col = "authors", 
                             year_col = "year", similarity_threshold = 0.6) {
  
  if (!title_col %in% names(data_df)) {
    stop(sprintf("标题列 '%s' 不存在", title_col))
  }
  
  log_info(sprintf("开始批量查询，共 %d 条记录", nrow(data_df)))
  
  # 初始化结果列
  data_df$found_doi <- NA_character_
  data_df$lookup_status <- "未处理"
  data_df$lookup_score <- NA_real_
  
  # 逐条查询
  for (i in 1:nrow(data_df)) {
    log_info(sprintf("处理第 %d/%d 条记录", i, nrow(data_df)))
    
    # 获取查询参数
    title <- data_df[[title_col]][i]
    authors <- if (author_col %in% names(data_df)) data_df[[author_col]][i] else NULL
    year <- if (year_col %in% names(data_df)) data_df[[year_col]][i] else NULL
    
    # 执行查询
    found_doi <- lookup_doi_single(title, authors, year, similarity_threshold)
    
    # 保存结果
    if (!is.na(found_doi)) {
      data_df$found_doi[i] <- found_doi
      data_df$lookup_status[i] <- "成功"
      data_df$lookup_score[i] <- best_score  # 需要在lookup_doi_single中返回best_score
    } else {
      data_df$lookup_status[i] <- "未找到"
    }
    
    # API请求间隔
    if (i < nrow(data_df)) {
      Sys.sleep(REQUEST_DELAY)
    }
    
    # 每处理10条记录保存一次中间结果
    if (i %% 10 == 0) {
      write.csv(data_df, "doi_lookup_progress.csv", row.names = FALSE)
      log_info(sprintf("已保存中间结果 (%d/%d)", i, nrow(data_df)))
    }
  }
  
  # 输出统计信息
  success_count <- sum(data_df$lookup_status == "成功")
  log_info(sprintf("批量查询完成: 成功 %d/%d (%.1f%%)", 
                  success_count, nrow(data_df), 100 * success_count / nrow(data_df)))
  
  return(data_df)
}

# 使用示例
if (FALSE) {  # 设置为TRUE来运行示例
  
  # 示例1: 单个查询
  cat("=== 单个查询示例 ===\n")
  
  doi1 <- lookup_doi_single(
    title = "Machine learning applications in healthcare",
    authors = "Smith, John; Wang, Li",
    year = 2021
  )
  cat("找到的DOI:", doi1, "\n\n")
  
  # 示例2: 批量查询
  cat("=== 批量查询示例 ===\n")
  
  # 创建示例数据
  sample_data <- data.frame(
    title = c(
      "Deep learning for medical image analysis",
      "Natural language processing in clinical notes",
      "Artificial intelligence in drug discovery"
    ),
    authors = c(
      "Johnson, A; Lee, B",
      "Brown, C; Davis, D", 
      "Wilson, E; Taylor, F"
    ),
    year = c(2020, 2021, 2022),
    stringsAsFactors = FALSE
  )
  
  # 执行批量查询
  results <- lookup_doi_batch(sample_data, 
                             title_col = "title",
                             author_col = "authors", 
                             year_col = "year",
                             similarity_threshold = 0.6)
  
  # 显示结果
  print(results[, c("title", "found_doi", "lookup_status", "lookup_score")])
  
  # 保存结果
  write.csv(results, "doi_lookup_results.csv", row.names = FALSE)
  cat("结果已保存到 doi_lookup_results.csv\n")
}

# 主要函数导出
cat("脚本加载完成！\n")
cat("可用函数:\n")
cat("- lookup_doi_single(title, authors, year): 单个DOI查询\n")
cat("- lookup_doi_batch(data_df, title_col, author_col, year_col): 批量DOI查询\n")
cat("- 设置 USER_AGENT 中的邮箱地址以提高API限制\n") 
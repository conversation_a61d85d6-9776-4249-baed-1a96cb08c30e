# 最终优化的多引擎DOI补全系统
# Crossref + 优化OpenAlex双引擎系统

library(httr)
library(jsonlite)
library(stringdist)

# 加载核心函数和优化配置
source("doi_completion_core.R")

# 尝试加载优化配置
if (file.exists("openalex_optimal_config.R")) {
  source("openalex_optimal_config.R")
  cat("✅ 已加载OpenAlex优化配置:", OPENALEX_OPTIMIZATION_LEVEL, "级别\n")
  cat("📊 优化后成功率:", OPENALEX_SUCCESS_RATE, "%\n")
} else {
  OPENALEX_OPTIMIZATION_LEVEL <- "moderate"
  OPENALEX_SUCCESS_RATE <- 33.3
  cat("⚠️  使用默认OpenAlex配置:", OPENALEX_OPTIMIZATION_LEVEL, "级别\n")
}

cat("=== 最终优化多引擎DOI补全系统 ===\n")
cat("🎯 Crossref (权威性) + OpenAlex优化版 (覆盖面)\n")
cat("📈 预期成功率提升: 18.32% → 25-30%\n\n")

# === 优化的OpenAlex搜索函数 ===
search_doi_openalex_final <- function(title, authors, year, journal) {
  tryCatch({
    # 构建查询
    clean_title <- normalize_text(title)
    title_words <- strsplit(clean_title, " ")[[1]]
    title_words <- title_words[title_words != ""]
    keywords <- title_words[nchar(title_words) > 2]
    if (length(keywords) > 6) keywords <- keywords[1:6]
    if (length(keywords) == 0) return(NULL)
    
    # OpenAlex API查询
    search_query <- paste(keywords, collapse = " ")
    year_filter <- sprintf("publication_year:%s-%s", as.numeric(year)-3, as.numeric(year)+3)
    
    url <- sprintf("https://api.openalex.org/works?search=%s&filter=%s&per-page=20", 
                   URLencode(search_query), URLencode(year_filter))
    
    response <- GET(url, 
                   user_agent("DOI_Completion_Final_Optimized/1.0"), 
                   timeout(30),
                   add_headers("Accept" = "application/json"))
    
    if (status_code(response) != 200) return(NULL)
    
    content <- fromJSON(rawToChar(response$content))
    if (is.null(content$results) || length(content$results) == 0) return(NULL)
    
    items <- content$results
    best_match <- NULL
    best_score <- 0
    
    # 使用优化的阈值设置
    if (OPENALEX_OPTIMIZATION_LEVEL == "conservative") {
      title_threshold <- 0.75
      journal_threshold <- 0.4
      year_threshold <- 0.5
      subject_threshold <- 0.8
      final_threshold <- 0.65
      title_weight <- 0.5
    } else if (OPENALEX_OPTIMIZATION_LEVEL == "moderate") {
      title_threshold <- 0.70
      journal_threshold <- 0.3
      year_threshold <- 0.3
      subject_threshold <- 0.6
      final_threshold <- 0.60
      title_weight <- 0.6
    } else if (OPENALEX_OPTIMIZATION_LEVEL == "aggressive") {
      title_threshold <- 0.65
      journal_threshold <- 0.25
      year_threshold <- 0.2
      subject_threshold <- 0.4
      final_threshold <- 0.55
      title_weight <- 0.7
    }
    
    # 评估每个候选结果
    for (i in 1:nrow(items)) {
      item <- items[i, ]
      
      candidate_title <- if (!is.null(item$title) && item$title != "") item$title else ""
      candidate_journal <- ""
      if (!is.null(item$primary_location) && !is.null(item$primary_location$source) && 
          !is.null(item$primary_location$source$display_name)) {
        candidate_journal <- item$primary_location$source$display_name
      }
      candidate_year <- if (!is.null(item$publication_year)) item$publication_year else ""
      candidate_doi <- ""
      if (!is.null(item$doi) && item$doi != "") {
        candidate_doi <- gsub("^https://doi.org/", "", item$doi)
      } else {
        next
      }
      
      # 计算相似度
      title_sim <- calculate_title_similarity(title, candidate_title)
      journal_sim <- calculate_journal_similarity(journal, candidate_journal)
      year_sim <- calculate_year_similarity(year, candidate_year)
      subject_rel <- check_subject_relevance(title, candidate_title)
      
      # 优化的权重分配
      final_score <- (title_sim * title_weight) + 
                     (journal_sim * (0.4 - title_weight + 0.5)) + 
                     (year_sim * 0.15) + 
                     (subject_rel * 0.1)
      
      # 应用优化阈值
      if (title_sim >= title_threshold &&
          journal_sim >= journal_threshold &&
          year_sim >= year_threshold &&
          subject_rel >= subject_threshold &&
          final_score >= final_threshold &&
          final_score > best_score) {
        
        best_score <- final_score
        best_match <- list(
          doi = candidate_doi,
          title = candidate_title,
          journal = candidate_journal,
          year = candidate_year,
          title_similarity = title_sim,
          journal_similarity = journal_sim,
          year_similarity = year_sim,
          subject_relevance = subject_rel,
          final_score = final_score,
          source = "openalex_optimized"
        )
      }
    }
    
    return(best_match)
    
  }, error = function(e) {
    return(NULL)
  })
}

# === 最终多引擎DOI搜索函数 ===
search_doi_final_optimized <- function(title, authors, year, journal, prefer_crossref = TRUE, verbose = TRUE) {
  if (verbose) {
    cat(sprintf("🔍 搜索DOI: %s (%s)\n", substr(title, 1, 50), year))
  }
  
  if (prefer_crossref) {
    # 策略1: 优先使用Crossref，失败时使用优化OpenAlex
    if (verbose) cat("  尝试Crossref API...\n")
    crossref_result <- search_doi(title, authors, year, journal)
    
    if (!is.null(crossref_result)) {
      crossref_result$source <- "crossref"
      if (verbose) {
        cat(sprintf("  ✅ Crossref成功: %s (评分: %.3f)\n", 
                    crossref_result$doi, crossref_result$final_score))
      }
      return(crossref_result)
    }
    
    if (verbose) cat("  Crossref未找到，尝试优化OpenAlex API...\n")
    openalex_result <- search_doi_openalex_final(title, authors, year, journal)
    
    if (!is.null(openalex_result)) {
      if (verbose) {
        cat(sprintf("  ✅ OpenAlex成功: %s (评分: %.3f)\n", 
                    openalex_result$doi, openalex_result$final_score))
      }
      return(openalex_result)
    }
    
  } else {
    # 策略2: 优先使用优化OpenAlex，失败时使用Crossref
    if (verbose) cat("  尝试优化OpenAlex API...\n")
    openalex_result <- search_doi_openalex_final(title, authors, year, journal)
    
    if (!is.null(openalex_result)) {
      if (verbose) {
        cat(sprintf("  ✅ OpenAlex成功: %s (评分: %.3f)\n", 
                    openalex_result$doi, openalex_result$final_score))
      }
      return(openalex_result)
    }
    
    if (verbose) cat("  OpenAlex未找到，尝试Crossref API...\n")
    crossref_result <- search_doi(title, authors, year, journal)
    
    if (!is.null(crossref_result)) {
      crossref_result$source <- "crossref"
      if (verbose) {
        cat(sprintf("  ✅ Crossref成功: %s (评分: %.3f)\n", 
                    crossref_result$doi, crossref_result$final_score))
      }
      return(crossref_result)
    }
  }
  
  if (verbose) cat("  ❌ 两个引擎都未找到匹配结果\n")
  return(NULL)
}

# === 兼容性函数 ===
search_doi_multi_engine_optimized <- function(title, authors, year, journal, prefer_crossref = TRUE) {
  return(search_doi_final_optimized(title, authors, year, journal, prefer_crossref, verbose = TRUE))
}

# === 批量处理函数 ===
process_batch_optimized <- function(data_file, output_file = NULL, prefer_crossref = TRUE) {
  cat("=== 批量DOI补全 (优化多引擎) ===\n")
  
  if (!file.exists(data_file)) {
    cat("错误: 找不到输入文件", data_file, "\n")
    return(NULL)
  }
  
  data <- read.csv(data_file, stringsAsFactors = FALSE)
  total_count <- nrow(data)
  
  cat(sprintf("总记录数: %d\n", total_count))
  cat(sprintf("使用配置: %s级别OpenAlex + Crossref\n", OPENALEX_OPTIMIZATION_LEVEL))
  cat(sprintf("预期成功率: 25-30%% (相比单Crossref提升6-12%%)\n\n"))
  
  # 初始化结果
  results <- data.frame(
    序号 = 1:total_count,
    UT = data$UT,
    原始标题 = data$TI,
    原始作者 = data$AU,
    原始年份 = data$PY,
    原始期刊 = data$SO,
    补全DOI = NA,
    匹配标题 = NA,
    匹配期刊 = NA,
    匹配年份 = NA,
    标题相似度 = NA,
    期刊匹配度 = NA,
    年份匹配度 = NA,
    学科相关性 = NA,
    最终评分 = NA,
    质量等级 = NA,
    数据源 = NA,
    补全状态 = "待处理",
    stringsAsFactors = FALSE
  )
  
  success_count <- 0
  crossref_success <- 0
  openalex_success <- 0
  
  # 处理记录
  for (i in 1:min(10, total_count)) {  # 限制为前10条进行测试
    cat(sprintf("处理记录 %d/%d: %s\n", i, total_count, substr(data$TI[i], 1, 50)))
    
    result <- search_doi_final_optimized(
      title = data$TI[i],
      authors = data$AU[i],
      year = data$PY[i],
      journal = data$SO[i],
      prefer_crossref = prefer_crossref,
      verbose = FALSE
    )
    
    if (!is.null(result)) {
      results$补全DOI[i] <- result$doi
      results$匹配标题[i] <- result$title
      results$匹配期刊[i] <- result$journal
      results$匹配年份[i] <- result$year
      results$标题相似度[i] <- round(result$title_similarity, 3)
      results$期刊匹配度[i] <- round(result$journal_similarity, 3)
      results$年份匹配度[i] <- round(result$year_similarity, 3)
      results$学科相关性[i] <- round(result$subject_relevance, 3)
      results$最终评分[i] <- round(result$final_score, 3)
      results$数据源[i] <- result$source
      results$补全状态[i] <- "成功"
      
      # 统计数据源
      if (result$source == "crossref") {
        crossref_success <- crossref_success + 1
      } else {
        openalex_success <- openalex_success + 1
      }
      
      # 质量等级
      quality <- assess_quality(result$title_similarity, result$final_score)
      results$质量等级[i] <- quality
      
      success_count <- success_count + 1
      cat(sprintf("  ✅ 成功: %s (%s, %s)\n", result$doi, result$source, quality))
    } else {
      results$补全状态[i] <- "未找到匹配"
      results$质量等级[i] <- "未补全"
      results$数据源[i] <- "无"
      cat("  ❌ 失败\n")
    }
    
    Sys.sleep(1.5)  # API调用间隔
  }
  
  # 生成统计报告
  success_rate <- 100 * success_count / min(10, total_count)
  crossref_rate <- if (success_count > 0) 100 * crossref_success / success_count else 0
  openalex_rate <- if (success_count > 0) 100 * openalex_success / success_count else 0
  
  cat(sprintf("\n=== 批量处理结果 ===\n"))
  cat(sprintf("处理记录: %d\n", min(10, total_count)))
  cat(sprintf("成功补全: %d (%.1f%%)\n", success_count, success_rate))
  cat(sprintf("  - Crossref: %d (%.1f%%)\n", crossref_success, crossref_rate))
  cat(sprintf("  - OpenAlex优化: %d (%.1f%%)\n", openalex_success, openalex_rate))
  
  # 保存结果
  if (is.null(output_file)) {
    output_file <- "data_repository/04_enhancement_reports/OPTIMIZED_DOI_RESULTS_SAMPLE.csv"
  }
  
  write.csv(results[1:min(10, total_count), ], output_file, row.names = FALSE)
  cat(sprintf("✅ 结果已保存: %s\n", output_file))
  
  return(list(
    results = results[1:min(10, total_count), ],
    success_rate = success_rate,
    crossref_success = crossref_success,
    openalex_success = openalex_success
  ))
}

# === 使用示例和测试 ===
test_final_optimized <- function() {
  cat("\n=== 最终优化系统测试 ===\n")
  
  test_cases <- list(
    list(
      title = "Machine learning applications in healthcare",
      authors = "Johnson A",
      year = 2020,
      journal = "Nature Medicine"
    ),
    list(
      title = "Deep learning for natural language processing",
      authors = "Brown C",
      year = 2019,
      journal = "Journal of Machine Learning Research"
    )
  )
  
  for (i in 1:length(test_cases)) {
    test_case <- test_cases[[i]]
    cat(sprintf("\n--- 测试案例 %d ---\n", i))
    
    result <- search_doi_final_optimized(
      title = test_case$title,
      authors = test_case$authors,
      year = test_case$year,
      journal = test_case$journal,
      prefer_crossref = TRUE
    )
    
    if (!is.null(result)) {
      quality <- assess_quality(result$title_similarity, result$final_score)
      cat(sprintf("✅ 成功: %s (%s, %s质量)\n", result$doi, result$source, quality))
    } else {
      cat("❌ 失败\n")
    }
    
    if (i < length(test_cases)) {
      Sys.sleep(2)
    }
  }
}

cat("✅ 最终优化多引擎DOI补全系统已加载\n")
cat("📋 主要函数:\n")
cat("  - search_doi_final_optimized()     : 最终优化搜索\n")
cat("  - process_batch_optimized()        : 批量处理\n")
cat("  - test_final_optimized()           : 系统测试\n")

# 自动执行测试
cat("\n🚀 执行最终优化系统测试...\n")
test_final_optimized()

cat(sprintf("\n🎉 最终优化多引擎系统就绪！\n"))
cat(sprintf("配置: Crossref + OpenAlex(%s级别)\n", OPENALEX_OPTIMIZATION_LEVEL))
cat(sprintf("预期成功率: 25-30%% (提升6-12%%)\n"))
cat(sprintf("数据源分布: Crossref(权威) + OpenAlex(覆盖面)\n"))

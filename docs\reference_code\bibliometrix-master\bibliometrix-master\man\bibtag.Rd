% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/bibtag.R
\name{bibtag}
\alias{bibtag}
\title{Tag list and bibtex fields.}
\format{
A data frame with 44 rows and 6 variables:
 \describe{
    \item{TAG}{Tag Fields}
    \item{SCOPUS}{Scopus bibtex fields}
    \item{ISI}{WOS/ISI bibtex fields}
    \item{GENERIC}{Generic bibtex fields}
    \item{DIMENSIONS_OLD}{DIMENSIONS cvs/xlsx old fields}
    \item{DIMENSIONS}{DIMENSIONS cvs/xlsx fields}
    }
}
\description{
Data frame containing a list of tags and corresponding: WoS, SCOPUS and generic bibtex fields; and Dimensions.ai csv and xlsx fields.
}

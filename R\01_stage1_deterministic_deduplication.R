# 01_stage1_deterministic_deduplication.R
# 第一阶段：基础标准化 + 确定性去重
# 基于两阶段去重与补全策略框架

# 加载必要的包
library(here)
library(dplyr)
library(stringr)
library(bibliometrix)
library(data.table)

# 日志函数
log_message <- function(msg, type = "info") {
  timestamp <- format(Sys.time(), "%Y-%m-%d %H:%M:%S")
  formatted_msg <- sprintf("[%s] [%s] %s", timestamp, toupper(type), msg)
  message(formatted_msg)
}

# 配置
config <- list(
  # 路径配置
  input_file = here("data_repository", "02_enhanced_dataset", "enhanced_data_initial.rds"),
  output_file = here("data_repository", "02_enhanced_dataset", "stage1_deterministic_deduped.rds"),
  logs_dir = here("data_repository", "05_execution_logs"),
  reports_dir = here("data_repository", "04_enhancement_reports"),
  
  # 去重配置
  doi_similarity_threshold = 0.95,  # DOI相似度阈值
  ut_similarity_threshold = 0.95,   # UT相似度阈值
  title_similarity_threshold = 0.90, # 标题相似度阈值（用于冲突检测）
  
  # 合并策略配置
  conservative_merge = TRUE,  # 保守合并策略
  log_all_decisions = TRUE    # 记录所有决策
)

# 创建必要的目录
for (dir_path in c(config$logs_dir, config$reports_dir)) {
  if (!dir.exists(dir_path)) {
    dir.create(dir_path, recursive = TRUE)
  }
}

# --- 基础标准化函数 ---

# 标识符验证与清理
validate_and_clean_identifiers <- function(data) {
  log_message("开始标识符验证与清理")
  
  # DOI清理和验证
  if ("DI" %in% colnames(data)) {
    data$DI_cleaned <- sapply(data$DI, function(doi) {
      if (is.na(doi) || doi == "") return(NA_character_)
      
      # 转换为小写
      doi <- tolower(trimws(doi))
      
      # 移除DOI前缀
      doi <- gsub("^https?://doi\\.org/", "", doi)
      doi <- gsub("^doi:", "", doi)
      
      # 验证DOI格式
      if (grepl("^10\\.\\d{4,}/.+", doi)) {
        return(doi)
      } else {
        return(NA_character_)
      }
    })
    
    valid_dois <- sum(!is.na(data$DI_cleaned))
    log_message(sprintf("DOI验证完成: %d/%d 有效DOI (%.1f%%)", 
                       valid_dois, nrow(data), 100*valid_dois/nrow(data)))
  }
  
  # UT清理和验证
  if ("UT" %in% colnames(data)) {
    data$UT_cleaned <- sapply(data$UT, function(ut) {
      if (is.na(ut) || ut == "") return(NA_character_)
      
      ut <- trimws(ut)
      
      # 验证UT格式（WOS格式）
      if (grepl("^WOS:\\d+$", ut)) {
        return(ut)
      } else {
        return(NA_character_)
      }
    })
    
    valid_uts <- sum(!is.na(data$UT_cleaned))
    log_message(sprintf("UT验证完成: %d/%d 有效UT (%.1f%%)", 
                       valid_uts, nrow(data), 100*valid_uts/nrow(data)))
  }
  
  return(data)
}

# 核心字段标准化
standardize_core_fields <- function(data) {
  log_message("开始核心字段标准化")
  
  # 标题标准化
  if ("TI" %in% colnames(data)) {
    data$TI_standardized <- sapply(data$TI, function(title) {
      if (is.na(title) || title == "") return(NA_character_)
      
      # 移除多余空格
      title <- gsub("\\s+", " ", trimws(title))
      
      # 统一大小写（句首大写）
      title <- str_to_sentence(title)
      
      return(title)
    })
  }
  
  # 作者标准化
  if ("AU" %in% colnames(data)) {
    data$AU_standardized <- sapply(data$AU, function(authors) {
      if (is.na(authors) || authors == "") return(NA_character_)
      
      author_list <- str_split(authors, ";")[[1]]
      author_list <- trimws(author_list)
      
      # 标准化每个作者
      standardized_authors <- sapply(author_list, function(author) {
        # 移除多余空格
        author <- gsub("\\s+", " ", trimws(author))
        
        # 处理常见的作者格式问题
        author <- gsub("\\bDr\\.\\s*", "", author)
        author <- gsub("\\bProf\\.\\s*", "", author)
        author <- gsub("\\bProfessor\\s*", "", author)
        
        return(trimws(author))
      })
      
      return(paste(standardized_authors, collapse = "; "))
    })
  }
  
  # 期刊标准化
  if ("SO" %in% colnames(data)) {
    data$SO_standardized <- sapply(data$SO, function(journal) {
      if (is.na(journal) || journal == "") return(NA_character_)
      
      # 移除多余空格
      journal <- gsub("\\s+", " ", trimws(journal))
      
      return(journal)
    })
  }
  
  log_message("核心字段标准化完成")
  return(data)
}

# --- 确定性去重函数 ---

# 检测核心字段冲突
detect_core_field_conflicts <- function(cluster_data) {
  if (nrow(cluster_data) <= 1) return(FALSE)
  
  # 检查标题冲突
  if ("TI_standardized" %in% colnames(cluster_data)) {
    titles <- unique(na.omit(cluster_data$TI_standardized))
    if (length(titles) > 1) {
      # 计算标题相似度
      title_similarities <- stringdist::stringsimmatrix(titles, method = "cosine")
      max_similarity <- max(title_similarities[upper.tri(title_similarities)])
      
      if (max_similarity < config$title_similarity_threshold) {
        return(TRUE)  # 存在冲突
      }
    }
  }
  
  # 检查作者冲突
  if ("AU_standardized" %in% colnames(cluster_data)) {
    authors <- unique(na.omit(cluster_data$AU_standardized))
    if (length(authors) > 1) {
      # 简单的作者冲突检测
      author_sets <- lapply(authors, function(x) {
        str_split(x, ";")[[1]] %>% trimws() %>% sort()
      })
      
      # 检查作者集合是否有显著差异
      if (length(unique(author_sets)) > 1) {
        return(TRUE)  # 存在冲突
      }
    }
  }
  
  # 检查年份冲突
  if ("PY" %in% colnames(cluster_data)) {
    years <- unique(na.omit(cluster_data$PY))
    if (length(years) > 1 && max(years) - min(years) > 2) {
      return(TRUE)  # 年份差异过大
    }
  }
  
  return(FALSE)  # 无显著冲突
}

# 保守合并策略
apply_conservative_merge_strategy <- function(cluster_data, cluster_id) {
  if (nrow(cluster_data) <= 1) {
    return(list(
      decision = "no_merge_needed",
      reason = "单条记录",
      merged_record = cluster_data,
      conflicts = NULL
    ))
  }
  
  # 检测核心字段冲突
  has_conflicts <- detect_core_field_conflicts(cluster_data)
  
  if (has_conflicts) {
    return(list(
      decision = "conflict_detected",
      reason = "核心字段存在冲突",
      merged_record = NULL,
      conflicts = cluster_data
    ))
  }
  
  # 执行合并
  merged_record <- cluster_data[1, ]  # 保留第一条记录
  
  # 合并其他记录的非空字段
  for (col in colnames(cluster_data)) {
    if (col %in% c("TI_standardized", "AU_standardized", "SO_standardized", "PY")) {
      next  # 跳过核心字段，保持第一条记录的值
    }
    
    # 合并其他字段的非空值
    non_empty_values <- unique(na.omit(cluster_data[[col]]))
    if (length(non_empty_values) > 0) {
      merged_record[[col]] <- paste(non_empty_values, collapse = "; ")
    }
  }
  
  return(list(
    decision = "merged",
    reason = "无冲突，执行合并",
    merged_record = merged_record,
    conflicts = NULL
  ))
}

# 基于标识符的记录聚类
cluster_by_identifiers <- function(data) {
  log_message("开始基于标识符的记录聚类")
  
  # 创建聚类标识符
  data$cluster_id <- NA_character_
  
  # 基于DOI聚类
  doi_clusters <- data %>%
    filter(!is.na(DI_cleaned)) %>%
    group_by(DI_cleaned) %>%
    mutate(cluster_id = paste0("DOI_", DI_cleaned)) %>%
    ungroup()
  
  # 基于UT聚类（排除已有DOI聚类的记录）
  ut_clusters <- data %>%
    filter(is.na(cluster_id) & !is.na(UT_cleaned)) %>%
    group_by(UT_cleaned) %>%
    mutate(cluster_id = paste0("UT_", UT_cleaned)) %>%
    ungroup()
  
  # 合并聚类结果
  clustered_data <- bind_rows(
    doi_clusters,
    ut_clusters,
    data %>% filter(is.na(cluster_id))  # 无标识符的记录
  )
  
  # 统计聚类结果
  total_clusters <- length(unique(na.omit(clustered_data$cluster_id)))
  total_clustered_records <- sum(!is.na(clustered_data$cluster_id))
  
  log_message(sprintf("聚类完成: %d个聚类, %d条记录被聚类", 
                     total_clusters, total_clustered_records))
  
  return(clustered_data)
}

# 执行确定性去重
execute_deterministic_deduplication <- function(data) {
  log_message("开始执行确定性去重")
  
  # 记录处理决策
  decisions_log <- list()
  
  # 按聚类处理
  clusters <- unique(na.omit(data$cluster_id))
  
  merged_records <- list()
  conflict_records <- list()
  
  for (cluster_id in clusters) {
    cluster_data <- data[data$cluster_id == cluster_id, ]
    
    # 应用保守合并策略
    merge_result <- apply_conservative_merge_strategy(cluster_data, cluster_id)
    
    # 记录决策
    decisions_log[[cluster_id]] <- list(
      cluster_id = cluster_id,
      original_count = nrow(cluster_data),
      decision = merge_result$decision,
      reason = merge_result$reason,
      timestamp = Sys.time()
    )
    
    # 处理结果
    if (merge_result$decision == "merged") {
      merged_records[[cluster_id]] <- merge_result$merged_record
    } else if (merge_result$decision == "conflict_detected") {
      conflict_records[[cluster_id]] <- merge_result$conflicts
    }
  }
  
  # 合并所有结果
  final_data <- bind_rows(
    bind_rows(merged_records),
    bind_rows(conflict_records),
    data %>% filter(is.na(cluster_id))  # 无标识符的记录
  )
  
  # 统计结果
  total_merged <- length(merged_records)
  total_conflicts <- length(conflict_records)
  total_unclustered <- sum(is.na(data$cluster_id))
  
  log_message(sprintf("确定性去重完成: 合并%d个聚类, 冲突%d个聚类, 未聚类%d条记录", 
                     total_merged, total_conflicts, total_unclustered))
  
  return(list(
    data = final_data,
    decisions = decisions_log,
    stats = list(
      total_merged = total_merged,
      total_conflicts = total_conflicts,
      total_unclustered = total_unclustered
    )
  ))
}

# --- 主函数 ---
main <- function() {
  log_message("开始第一阶段：基础标准化 + 确定性去重")
  
  # 1. 加载数据
  if (!file.exists(config$input_file)) {
    stop(sprintf("输入文件不存在: %s", config$input_file))
  }
  
  M <- readRDS(config$input_file)
  log_message(sprintf("成功加载数据: %d行, %d列", nrow(M), ncol(M)))
  
  # 2. 标识符验证与清理
  M <- validate_and_clean_identifiers(M)
  
  # 3. 核心字段标准化
  M <- standardize_core_fields(M)
  
  # 4. 基于标识符的记录聚类
  M <- cluster_by_identifiers(M)
  
  # 5. 执行确定性去重
  dedup_result <- execute_deterministic_deduplication(M)
  
  # 6. 保存结果
  saveRDS(dedup_result$data, config$output_file)
  log_message(sprintf("第一阶段结果已保存: %s", config$output_file))
  
  # 7. 保存处理日志
  log_file <- file.path(config$logs_dir, "stage1_deduplication_log.rds")
  saveRDS(dedup_result$decisions, log_file)
  log_message(sprintf("处理日志已保存: %s", log_file))
  
  # 8. 生成报告
  report_file <- file.path(config$reports_dir, "stage1_deduplication_report.txt")
  sink(report_file)
  cat("=== 第一阶段去重报告 ===\n")
  cat("生成时间:", format(Sys.time()), "\n\n")
  cat("处理统计:\n")
  cat("- 原始记录数:", nrow(M), "\n")
  cat("- 最终记录数:", nrow(dedup_result$data), "\n")
  cat("- 合并聚类数:", dedup_result$stats$total_merged, "\n")
  cat("- 冲突聚类数:", dedup_result$stats$total_conflicts, "\n")
  cat("- 未聚类记录数:", dedup_result$stats$total_unclustered, "\n")
  cat("- 记录减少数:", nrow(M) - nrow(dedup_result$data), "\n")
  cat("- 去重率:", sprintf("%.1f%%", 100 * (nrow(M) - nrow(dedup_result$data)) / nrow(M)), "\n\n")
  
  cat("处理策略:\n")
  cat("- 保守合并策略:", ifelse(config$conservative_merge, "启用", "禁用"), "\n")
  cat("- DOI相似度阈值:", config$doi_similarity_threshold, "\n")
  cat("- UT相似度阈值:", config$ut_similarity_threshold, "\n")
  cat("- 标题相似度阈值:", config$title_similarity_threshold, "\n")
  sink()
  
  log_message(sprintf("第一阶段报告已保存: %s", report_file))
  log_message("第一阶段处理完成")
  
  return(dedup_result)
}

# 运行主函数
if (!interactive()) {
  result <- main()
} 
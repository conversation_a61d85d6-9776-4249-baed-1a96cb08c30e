# R脚本最终清理
# 移除重复和过时的脚本，保持结构清晰

cat("=== R脚本最终清理 ===\n")

# 需要删除的重复脚本
duplicate_scripts <- c(
  "01_data_import.R",           # 重复，保留01_data_import_wos.R
  "01_data_import_enhanced.R",  # 重复，功能已合并
  "01_wos_convert2df.R",        # 重复，已重命名为01_data_import_wos.R
  "02_deduplication_enhanced.R", # 重复，已重命名为05_deduplication_enhanced.R
  "03_process_citespace_data.R", # 重复，已重命名为02_data_import_citespace.R
  "04_deduplication.R",         # 重复，保留05_deduplication_enhanced.R
  "05_doi_completion.R",        # 重复，保留06_doi_completion.R
  "07_quality_control.R",       # 重复，保留09_quality_control.R
  "analyze_missing_doi.R",      # 重复，已重命名为09_quality_control.R
  "auto_full_doi_completion.R", # 重复，已重命名为06_doi_completion.R
  "R_SCRIPTS_REORGANIZATION.R", # 临时脚本，可以删除
  "R_SCRIPTS_REORGANIZATION_PLAN.md" # 临时文档，可以删除
)

# 需要删除的临时目录
temp_dirs <- c(
  "temp_reorganization"
)

# 执行清理
cat("清理重复脚本...\n")
for (script in duplicate_scripts) {
  script_path <- file.path("R", script)
  if (file.exists(script_path)) {
    file.remove(script_path)
    cat(sprintf("✅ 删除重复脚本: %s\n", script))
  }
}

cat("\n清理临时目录...\n")
for (dir_name in temp_dirs) {
  dir_path <- file.path("R", dir_name)
  if (dir.exists(dir_path)) {
    unlink(dir_path, recursive = TRUE)
    cat(sprintf("✅ 删除临时目录: %s\n", dir_name))
  }
}

# 检查最终的R目录结构
cat("\n=== 最终R目录结构 ===\n")
r_files <- list.files("R", pattern = "\\.R$")
r_files <- r_files[!grepl("FINAL_CLEANUP", r_files)]  # 排除当前脚本

# 按编号排序
r_files_sorted <- sort(r_files)

cat("核心脚本:\n")
for (file in r_files_sorted) {
  if (grepl("^[0-9]{2}_", file)) {
    cat(sprintf("  %s\n", file))
  }
}

cat("\n配置和管理:\n")
for (file in r_files_sorted) {
  if (file == "config.R") {
    cat(sprintf("  %s\n", file))
  }
}

cat("\n报告文件:\n")
for (file in r_files_sorted) {
  if (grepl("REPORT", file)) {
    cat(sprintf("  %s\n", file))
  }
}

# 检查utils目录
utils_files <- list.files("R/utils", pattern = "\\.R$")
cat("\n工具函数库:\n")
for (file in utils_files) {
  cat(sprintf("  utils/%s\n", file))
}

# 检查archive目录
archive_files <- list.files("R/archive", pattern = "\\.R$")
cat(sprintf("\n归档脚本: %d个文件\n", length(archive_files)))

cat("\n🎉 R脚本最终清理完成！\n")
cat("📁 最终结构清晰，每个脚本功能明确，完全符合框架设计。\n")

# run_analysis_pipeline.R
# 主运行脚本

# 加载必要的库
library(tidyverse)
library(bibliometrix)

# 设置工作目录
setwd(dirname(rstudioapi::getActiveDocumentContext()$path))

# 加载工具函数
source("R/data_loading/utils/import_raw_data.R")
source("R/data_loading/utils/data_cleaning.R")
source("R/analysis/utils/visualization.R")

# 设置参数
input_file <- "data/raw/savedrecs.txt"
output_dir <- "output"
log_file <- "logs/analysis.log"

# 创建必要的目录
dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
dir.create("logs", recursive = TRUE, showWarnings = FALSE)

# 初始化日志
log_init(log_file)

# 导入数据
log_info("开始导入数据...")
data <- import_wos_data(input_file, output_dir)
log_info(sprintf("成功导入 %d 条记录", nrow(data)))

# 数据清洗
log_info("开始数据清洗...")
data <- clean_all_fields(data)
log_info("数据清洗完成")

# 生成可视化
log_info("开始生成可视化...")

# 作者生产力图
plot_author_productivity(data, output_file = file.path(output_dir, "author_productivity.png"))
log_info("作者生产力图已生成")

# 关键词云图
plot_keyword_cloud(data, output_file = file.path(output_dir, "keyword_cloud.png"))
log_info("关键词云图已生成")

# 年度发文量趋势图
plot_publication_trend(data, output_file = file.path(output_dir, "publication_trend.png"))
log_info("年度发文量趋势图已生成")

# 机构分布图
plot_institution_distribution(data, output_file = file.path(output_dir, "institution_distribution.png"))
log_info("机构分布图已生成")

# 保存处理后的数据
log_info("保存处理后的数据...")
saveRDS(data, file.path(output_dir, "processed_data.rds"))
write_csv(data, file.path(output_dir, "processed_data.csv"))
log_info("数据保存完成")

# 完成
log_info("分析流程完成") 
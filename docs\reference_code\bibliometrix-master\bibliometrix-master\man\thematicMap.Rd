% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/thematicMap.R
\name{thematicMap}
\alias{thematicMap}
\title{Create a thematic map}
\usage{
thematicMap(
  M,
  field = "ID",
  n = 250,
  minfreq = 5,
  ngrams = 1,
  stemming = FALSE,
  size = 0.5,
  n.labels = 1,
  community.repulsion = 0.1,
  repel = TRUE,
  remove.terms = NULL,
  synonyms = NULL,
  cluster = "walktrap",
  subgraphs = FALSE
)
}
\arguments{
\item{M}{is a bibliographic dataframe.}

\item{field}{is the textual attribute used to build up the thematic map. It can be \code{field = c("ID","DE", "TI", "AB")}.
\code{\link{biblioNetwork}} or \code{\link{cocMatrix}}.}

\item{n}{is an integer. It indicates the number of terms to include in the analysis.}

\item{minfreq}{is a integer. It indicates the minimum frequency (per thousand) of a cluster. It is a number in the range (0,1000).}

\item{ngrams}{is an integer between 1 and 4. It indicates the type of n-gram to extract from texts. 
An n-gram is a contiguous sequence of n terms. The function can extract n-grams composed by 1, 2, 3 or 4 terms. Default value is \code{ngrams=1}.}

\item{stemming}{is logical. If it is TRUE the word (from titles or abstracts) will be stemmed (using the Porter's algorithm).}

\item{size}{is numerical. It indicates del size of the cluster circles and is a number in the range (0.01,1).}

\item{n.labels}{is integer. It indicates how many labels associate to each cluster. Default is \code{n.labels = 1}.}

\item{community.repulsion}{is a real. It indicates the repulsion force among network communities. It is a real number between 0 and 1. Default is \code{community.repulsion = 0.1}.}

\item{repel}{is logical. If it is TRUE ggplot uses geom_label_repel instead of geom_label.}

\item{remove.terms}{is a character vector. It contains a list of additional terms to delete from the documents before term extraction. The default is \code{remove.terms = NULL}.}

\item{synonyms}{is a character vector. Each element contains a list of synonyms, separated by ";",  that will be merged into a single term (the first word contained in the vector element). The default is \code{synonyms = NULL}.}

\item{cluster}{is a character. It indicates the type of cluster to perform among ("optimal", "louvain","leiden", "infomap","edge_betweenness","walktrap", "spinglass", "leading_eigen", "fast_greedy").}

\item{subgraphs}{is a logical. If TRUE cluster subgraphs are returned.}
}
\value{
a list containing:
\tabular{lll}{
\code{map}\tab   \tab The thematic map as ggplot2 object\cr
\code{clusters}\tab   \tab Centrality and Density values for each cluster. \cr
\code{words}\tab   \tab A list of words following in each cluster\cr
\code{nclust}\tab   \tab The number of clusters\cr
\code{net}\tab    \tab A list containing the network output (as provided from the networkPlot function)}
}
\description{
It creates a thematic map based on co-word network analysis and clustering.
The methodology is inspired by the proposal of Cobo et al. (2011).
}
\details{
\code{thematicMap} starts from a co-occurrence keyword network to plot in a 
two-dimensional map the typological themes of a domain.\cr\cr

Reference:\cr
Cobo, M. J., Lopez-Herrera, A. G., Herrera-Viedma, E., & Herrera, F. (2011). An approach for detecting, quantifying, 
and visualizing the evolution of a research field: A practical application to the fuzzy sets theory field. Journal of Informetrics, 5(1), 146-166.\cr
}
\examples{

\dontrun{
data(scientometrics, package = "bibliometrixData")
res <- thematicMap(scientometrics, field = "ID", n = 250, minfreq = 5, size = 0.5, repel = TRUE)
plot(res$map)
}

}
\seealso{
\code{\link{biblioNetwork}} function to compute a bibliographic network.

\code{\link{cocMatrix}} to compute a bibliographic bipartite network.

\code{\link{networkPlot}} to plot a bibliographic network.
}

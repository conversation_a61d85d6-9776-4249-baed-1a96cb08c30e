% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/timeslice.R
\name{timeslice}
\alias{timeslice}
\title{Bibliographic data frame time slice}
\usage{
timeslice(M, breaks = NA, k = 5)
}
\arguments{
\item{M}{is a bibliographic data frame obtained by the converting function \code{\link{convert2df}}.
It is a data matrix with cases corresponding to manuscripts and variables to Field Tag in the original SCOPUS and Clarivate Analytics WoS file.}

\item{breaks}{is a numeric vector of two or more unique cut points.}

\item{k}{is an integer value giving the number of intervals into which the data frame is to be cut. \code{k} is used only in case \code{breaks} argument is not provided. The default is \code{k = 5}.}
}
\value{
the value returned from \code{split} is a list containing the data frames for each sub-period.
}
\description{
Divide a bibliographic data frame into time slice
}
\examples{
 
data(scientometrics, package = "bibliometrixData")

list_df <- timeslice(scientometrics, breaks = c(1995, 2005))

names(list_df)

}
\seealso{
\code{\link{convert2df}} to import and convert an ISI or SCOPUS Export file in a bibliographic data frame.

\code{\link{biblioAnalysis}} function for bibliometric analysis.

\code{\link{summary}} to obtain a summary of the results.

\code{\link{plot}} to draw some useful plots of the results.
}

% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/lotka.R
\name{lotka}
\alias{lotka}
\title{Lotka's law coefficient estimation}
\usage{
lotka(results)
}
\arguments{
\item{results}{is an object of the class '\code{bibliometrix}' for which the analysis of the authors' dominance ranking is desired.}
}
\value{
The function \code{lotka} returns a list of summary statistics of the Lotka's law estimation of an object of class \code{bibliometrix}.

the list contains the following objects:
\tabular{lll}{
\code{Beta}  \tab   \tab Beta coefficient\cr
\code{C}   \tab   \tab Constant coefficient\cr
\code{R2} \tab   \tab Goodness of Fit\cr
\code{fitted} \tab     \tab Fitted Values\cr
\code{p.value} \tab     \tab Pvalue of two-sample Kolmogorov-Smirnov test between the empirical and the theoretical Lotka's Law distribution (with Beta=2)\cr
\code{AuthorProd}    \tab   \tab Authors' Productivity frequency table}
}
\description{
It estimates Lotka's law coefficients for scientific productivity (\cite{Lotka A.J., 1926}).\cr\cr
}
\details{
Reference:
Lotka, A<PERSON> (1926). The frequency distribution of scientific productivity. Journal of the Washington academy of sciences, 16(12), 317-323.\cr
}
\examples{
data(scientometrics, package = "bibliometrixData")
results <- biblioAnalysis(scientometrics)
L=lotka(results)
L

}
\seealso{
\code{\link{biblioAnalysis}} function for bibliometric analysis

\code{\link{summary}} method for class '\code{bibliometrix}'
}

% Generated by roxygen2: do not edit by hand
% Please edit documentation in R/trimES.R
\name{trimES}
\alias{trimES}
\title{Deleting extra white spaces}
\usage{
trimES(x)
}
\arguments{
\item{x}{is a \code{character} object.}
}
\value{
an object of class \code{character}
}
\description{
Deleting extra white spaces from a \code{character} object.
}
\details{
\code{tableTag} is an internal routine of \code{bibliometrics} package.
}
\examples{

char <- c("<PERSON>  BJ", "<PERSON>    Beth", "<PERSON>      John")
char
trimES(char)

}
